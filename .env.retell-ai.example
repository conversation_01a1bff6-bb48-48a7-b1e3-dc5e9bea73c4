# Retell AI Configuration for JSC Alarm Call-Out System
# Copy this file to .env.local and fill in your actual values

# ==============================================
# RETELL AI CONFIGURATION
# ==============================================

# Your Retell AI API key (get from https://retellai.com dashboard)
RETELL_AI_API_KEY=your_retell_ai_api_key_here

# Phone number purchased through Retell AI (E.164 format: +**********)
RETELL_AI_FROM_NUMBER=+**********

# JSC Alarm Call-Out Agent ID (created by this setup)
RETELL_AI_AGENT_ID=agent_beac3aef1a176d48c4d85d2541

# JSC Alarm Call-Out LLM ID (created by this setup)
RETELL_AI_LLM_ID=llm_f42d54584f5cfc07e6ee62b1cb83

# Webhook secret for verifying Retell AI callbacks (generate a random string)
RETELL_AI_WEBHOOK_SECRET=your_webhook_secret_here

# ==============================================
# SUPABASE CONFIGURATION
# ==============================================

# Your Supabase project URL
SUPABASE_URL=https://your-project.supabase.co

# Supabase service role key (for server-side operations)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Supabase anon key (for client-side operations)
SUPABASE_ANON_KEY=your_anon_key_here

# ==============================================
# ESCALATION CONFIGURATION
# ==============================================

# Maximum escalation level before giving up
MAX_ESCALATION_LEVEL=3

# Minutes to wait before escalating to next contact
ESCALATION_TIMEOUT_MINUTES=5

# Maximum retry attempts per contact
MAX_RETRY_ATTEMPTS=2

# ==============================================
# WEBHOOK CONFIGURATION
# ==============================================

# Base URL for your application (used for webhook callbacks)
APP_BASE_URL=https://your-app-domain.com

# Webhook endpoint for Retell AI callbacks (automatically constructed)
# Format: ${SUPABASE_URL}/functions/v1/retell-webhook-handler
RETELL_WEBHOOK_URL=${SUPABASE_URL}/functions/v1/retell-webhook-handler

# ==============================================
# TESTING CONFIGURATION
# ==============================================

# Phone number for testing (your own number in E.164 format)
TEST_PHONE_NUMBER=+**********

# Enable test mode (prevents actual calls in development)
TEST_MODE=true

# ==============================================
# SETUP INSTRUCTIONS
# ==============================================

# 1. Sign up for Retell AI account at https://retellai.com
# 2. Purchase a phone number through Retell AI dashboard
# 3. Copy this file to .env.local
# 4. Fill in your actual API keys and phone numbers
# 5. Deploy the Supabase Edge Function for webhook handling
# 6. Test the configuration using test-voice-agent.js
# 7. Configure escalation contacts in your database

# ==============================================
# SECURITY NOTES
# ==============================================

# - Never commit .env.local to version control
# - Use strong, random webhook secrets
# - Restrict API key permissions where possible
# - Monitor call usage and costs
# - Test thoroughly before production deployment
