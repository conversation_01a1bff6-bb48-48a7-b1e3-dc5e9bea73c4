# Sentry Configuration for JSC Alarm Call-Out App
# Copy this file to .env.local and fill in your actual values

# ==============================================
# SENTRY CONFIGURATION
# ==============================================

# Sentry Auth Token for uploading source maps (get from https://sentry.io/settings/account/api/auth-tokens/)
# Required for production builds to upload source maps
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here

# Sentry Release Name (optional - will auto-generate if not provided)
# Format: jsc-alarm-app@version
SENTRY_RELEASE=jsc-alarm-app@1.0.0

# Application Version (optional - used in release name)
VITE_APP_VERSION=1.0.0

# ==============================================
# ENVIRONMENT CONFIGURATION
# ==============================================

# Environment name (development, staging, production)
# This affects Sentry sampling rates and error filtering
NODE_ENV=production

# ==============================================
# SENTRY PROJECT INFORMATION
# ==============================================

# Organization: s-tier-building-automation-3b
# Project: alarm-call-out
# DSN: https://<EMAIL>/****************

# ==============================================
# USAGE INSTRUCTIONS
# ==============================================

# 1. Copy this file to .env.local:
#    cp .env.sentry.example .env.local

# 2. Get your Sentry Auth Token:
#    - Go to https://sentry.io/settings/account/api/auth-tokens/
#    - Create a new token with 'project:releases' and 'org:read' scopes
#    - Replace 'your_sentry_auth_token_here' with your actual token

# 3. Update the version number as needed

# 4. For production deployment, ensure NODE_ENV=production

# ==============================================
# SECURITY NOTES
# ==============================================

# - Never commit .env.local to version control
# - The auth token has sensitive permissions
# - The DSN is already configured in the code and is safe to be public
# - Source maps will only be uploaded in production builds
