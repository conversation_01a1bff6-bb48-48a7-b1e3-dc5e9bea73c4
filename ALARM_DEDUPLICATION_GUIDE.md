# Alarm Deduplication Implementation Guide

This guide explains the alarm deduplication system implemented for the JSC Alarm Call-Out App to prevent duplicate alarm entries when the same alarm ID is received multiple times via Mailgun webhooks.

## Overview

The deduplication system uses unique alarm IDs from building alarm systems to prevent duplicate database entries. When the same alarm is received multiple times, the system updates the existing record instead of creating a new one.

## Key Components

### 1. Database Schema Changes

#### New Column: `building_alarm_id`
- **Type**: `VARCHAR(255)`
- **Purpose**: Stores the unique alarm ID from the building's alarm system
- **Nullable**: Yes (for backward compatibility)
- **Index**: Yes (for performance)

#### Unique Constraint
- **Name**: `unique_building_alarm`
- **Columns**: `(building_id, building_alarm_id)`
- **Purpose**: Prevents duplicate alarms per building

### 2. Alarm ID Parsing

The system recognizes various alarm ID patterns commonly used by building systems:

```javascript
const alarmIdPatterns = [
  /Alarm\s*ID:\s*([A-Za-z0-9\-_]+)/i,
  /Event\s*ID:\s*([A-Za-z0-9\-_]+)/i,
  /Reference\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
  /Incident\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
  /Alert\s*ID:\s*([A-Za-z0-9\-_]+)/i,
  /Notification\s*ID:\s*([A-Za-z0-9\-_]+)/i,
  /System\s*ID:\s*([A-Za-z0-9\-_]+)/i,
  /ID:\s*([A-Za-z0-9\-_]{4,})/i, // Generic ID pattern
  /#([A-Za-z0-9\-_]{4,})/i, // Hash-prefixed ID
  /\[([A-Za-z0-9\-_]{4,})\]/i // Bracket-enclosed ID
];
```

### 3. Fallback ID Generation

If no alarm ID is found in the email content, the system generates a fallback ID:

1. **First Priority**: Building system alarm ID (parsed from email)
2. **Second Priority**: Email message ID
3. **Fallback**: Generated ID: `fallback-{timestamp}-{random}`

### 4. Upsert Logic

The system uses PostgreSQL's `UPSERT` functionality:

```sql
INSERT INTO alarm_notifications (...)
VALUES (...)
ON CONFLICT (building_id, building_alarm_id) 
DO UPDATE SET
  -- Update webhook data with latest information
  webhook_signature = EXCLUDED.webhook_signature,
  webhook_timestamp = EXCLUDED.webhook_timestamp,
  -- Preserve existing alarm data unless it was empty
  alarm_details = COALESCE(NULLIF(alarm_notifications.alarm_details, ''), EXCLUDED.alarm_details)
```

## Implementation Files

### Database Schema
- **File**: `database-setup.sql`
- **Changes**: Added `building_alarm_id` column and unique constraint

### Migration Script
- **File**: `database-migration-alarm-deduplication.sql`
- **Purpose**: Applies changes to existing databases
- **Includes**: Column addition, constraint creation, data migration

### Webhook Processing
- **File**: `supabase/functions/mailgun-webhook-handler/index.ts`
- **Changes**: 
  - Enhanced alarm ID parsing
  - Implemented upsert logic
  - Added duplicate detection logging

### Client-Side Utilities
- **File**: `src/lib/alarmUtils.js`
- **Changes**: Added building alarm ID parsing to client-side utilities

### React Hook
- **File**: `src/hooks/useAlarms.js`
- **Changes**: Updated to handle building alarm ID in webhook processing

## Usage Examples

### Example 1: Fire Alarm Email
```
Subject: CRITICAL FIRE ALARM - Main Building
Alarm ID: FA-2025-001234
Time: 1/15/2025, 2:30:15 PM EST
Alarm Type: Fire Detection System
Severity: CRITICAL
Details: Smoke detectors triggered in server room
Location: Building A, Floor 2, Server Room 201
```

**Parsed Result**:
- `buildingAlarmId`: "FA-2025-001234"
- `alarmType`: "Fire Detection System"
- `severity`: "CRITICAL"

### Example 2: HVAC Alarm Email
```
Subject: HVAC System Alert
Event ID: HVAC-2025-005678
Time: 1/15/2025, 3:45:22 PM EST
Alarm Type: HVAC System
Severity: MEDIUM
Details: Chiller temperature exceeded threshold
```

**Parsed Result**:
- `buildingAlarmId`: "HVAC-2025-005678"
- `alarmType`: "HVAC System"
- `severity`: "MEDIUM"

## Deployment Steps

### 1. Apply Database Migration
Run the migration script in your Supabase SQL Editor:
```sql
-- Execute database-migration-alarm-deduplication.sql
```

### 2. Deploy Edge Function
Update the Mailgun webhook handler Edge Function:
```bash
supabase functions deploy mailgun-webhook-handler
```

### 3. Update Client Application
Deploy the updated client-side code with enhanced parsing logic.

### 4. Test Deduplication
1. Send a test alarm email to a building address
2. Send the same alarm email again
3. Verify only one record exists in the database
4. Check that the second webhook updated the existing record

## Monitoring and Logging

### Edge Function Logs
The webhook handler logs deduplication events:
```javascript
console.log(`Successfully processed alarm: ${insertedAlarm.id} (${isNewAlarm ? 'new' : 'duplicate updated'})`)
```

### Response Indicators
The webhook response includes deduplication information:
```json
{
  "success": true,
  "alarmId": "uuid-here",
  "buildingAlarmId": "FA-2025-001234",
  "isNewAlarm": false,
  "message": "Duplicate alarm updated successfully"
}
```

## Benefits

1. **Prevents Duplicate Records**: Eliminates multiple database entries for the same alarm
2. **Preserves Data Integrity**: Updates existing records with latest webhook data
3. **Maintains Audit Trail**: Tracks when duplicates are received
4. **Flexible ID Recognition**: Supports various alarm ID formats
5. **Backward Compatible**: Works with existing alarms and email formats

## Troubleshooting

### Common Issues

1. **No Alarm ID Found**: System falls back to message ID or generates unique ID
2. **Constraint Violations**: Check for data type mismatches in building_alarm_id
3. **Performance Issues**: Ensure indexes are properly created

### Debugging

1. Check Edge Function logs for parsing results
2. Verify unique constraint exists: `\d+ alarm_notifications` in psql
3. Test alarm ID parsing with sample emails
4. Monitor webhook response for `isNewAlarm` flag

## Future Enhancements

1. **Custom ID Patterns**: Allow buildings to configure custom alarm ID patterns
2. **Duplicate Alerts**: Notify administrators when duplicates are detected
3. **Retention Policies**: Automatically clean up old duplicate webhook data
4. **Analytics**: Track duplicate rates per building for system health monitoring
