# Alarm Email Integration Guide

This guide explains how the JSC Alarm Call-Out App integrates with Mailgun webhooks to process alarm notifications automatically.

## Overview

The application processes alarm emails sent to unique building email addresses (e.g., `<EMAIL>`) and stores them in a Supabase database for monitoring and management.

## Database Schema

### Core Tables

1. **severity_levels** - Defines alarm severity levels (LOW, MEDIUM, HIGH, CRITICAL)
2. **alarm_types** - Categorizes different types of alarms (Fire, Security, HVAC, etc.)
3. **buildings** - Stores building information with unique email addresses
4. **alarm_notifications** - Main table storing all alarm records

### Key Features

- **Row Level Security (RLS)** - Ensures data security
- **Automatic timestamps** - Tracks creation and updates
- **Foreign key relationships** - Maintains data integrity
- **Indexes** - Optimized for performance
- **JSONB storage** - Preserves raw webhook data

## Email Processing Flow

### 1. Email Reception
- Mailgun receives emails sent to building-specific addresses
- Webhook is triggered and sent to your endpoint
- Raw email data is captured including headers, body, and metadata

### 2. Parsing and Validation
- Email content is parsed to extract alarm details:
  - **Time**: When the alarm occurred
  - **Alarm Type**: Category of alarm (Fire, Security, etc.)
  - **Severity**: Priority level (LOW, MEDIUM, HIGH, CRITICAL)
  - **Details**: Description of the alarm condition
  - **Location**: Where the alarm occurred (if specified)

### 3. Database Storage
- Building is identified by recipient email address
- Alarm type and severity are matched to reference tables
- Complete alarm record is created with all metadata
- Status is set to 'received' for new alarms

### 4. Status Management
- **Received** → **Acknowledged** → **Resolved**
- Users can update status through the dashboard
- Timestamps and user IDs are tracked for audit trail

## Application Components

### 1. AlarmDashboard (`src/components/AlarmDashboard.jsx`)
- Real-time alarm monitoring interface
- Filtering by status and severity
- Status management (acknowledge/resolve)
- Test alarm functionality

### 2. useAlarms Hook (`src/hooks/useAlarms.js`)
- Centralized alarm data management
- CRUD operations for alarms
- Real-time data synchronization
- Webhook processing integration

### 3. Alarm Utilities (`src/lib/alarmUtils.js`)
- Email parsing functions
- Data validation and formatting
- Building/type/severity matching
- Display formatting helpers

### 4. Webhook Handler (`src/lib/webhookHandler.js`)
- Webhook processing logic
- Signature validation (placeholder)
- Error handling and logging
- Setup instructions and documentation



## Sample Alarm Email Format

```
Subject: ALARM NOTIFICATION
To: <EMAIL>
From: <EMAIL>

Time: 4/27/2025, 11:16:06 PM EST
Alarm Type: Fire Detection System
Severity: CRITICAL
Details: Smoke detectors have been triggered in the specified location.
```

## Production Setup

### 1. Server-Side Webhook Endpoint

Create an HTTP endpoint to receive Mailgun webhooks:

```javascript
// Example Express.js endpoint
app.post('/webhook/mailgun', express.raw({type: 'application/x-www-form-urlencoded'}), async (req, res) => {
  try {
    const webhookData = req.body;
    
    // Validate signature
    if (!validateWebhookSignature(webhookData)) {
      return res.status(401).send('Unauthorized');
    }
    
    // Process the alarm
    const result = await processAlarmWebhook(webhookData);
    
    if (result.success) {
      res.status(200).send('OK');
    } else {
      res.status(500).send('Error processing alarm');
    }
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).send('Internal server error');
  }
});
```

### 2. Mailgun Configuration

1. **Domain Setup**: Configure your domain in Mailgun
2. **Webhook URL**: Set webhook URL to your endpoint
3. **Event Types**: Enable "Incoming Messages" events
4. **Security**: Configure webhook signing key

### 3. Environment Variables

Add to your `.env` file:

```bash
# Mailgun Configuration
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_WEBHOOK_SIGNING_KEY=your_webhook_signing_key
MAILGUN_DOMAIN=mg.stieralarms.online

# Supabase Configuration (already configured)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Security Considerations

### 1. Webhook Signature Verification
- Always verify Mailgun webhook signatures
- Use the provided signing key to validate authenticity
- Reject requests with invalid signatures

### 2. Input Validation
- Sanitize all email content before storage
- Validate email addresses and timestamps
- Check for malicious content in email bodies

### 3. Rate Limiting
- Implement rate limiting on webhook endpoints
- Monitor for unusual traffic patterns
- Set up alerts for processing failures

### 4. Data Protection
- Use HTTPS for all webhook endpoints
- Implement proper RLS policies in Supabase
- Store sensitive data securely
- Regular security audits

## Testing and Development

### 1. Local Testing
- Test webhook processing with real Mailgun webhooks in development
- Generate sample alarm data for development
- Test different alarm types and severities

### 2. Integration Testing
- Test with real Mailgun webhooks in staging
- Verify signature validation works correctly
- Test error handling and edge cases

### 3. Monitoring
- Log all webhook processing attempts
- Monitor alarm processing success rates
- Set up alerts for system failures

## Troubleshooting

### Common Issues

1. **Webhook not received**
   - Check Mailgun webhook configuration
   - Verify endpoint URL is accessible
   - Check firewall and security settings

2. **Parsing errors**
   - Verify email format matches expected structure
   - Check for encoding issues in email content
   - Review parsing logic for edge cases

3. **Database errors**
   - Verify Supabase connection and credentials
   - Check RLS policies allow insertions
   - Review foreign key constraints

4. **Building not found**
   - Ensure building email addresses are configured
   - Check for typos in email addresses
   - Verify building records exist in database

5. **Supabase Query Errors (PGRST100)**
   - **Issue**: "failed to parse select parameter" with auth.users references
   - **Cause**: Direct joins to auth.users table are not supported in PostgREST
   - **Solution**: Remove auth.users joins from select queries and handle user info separately
   - **Fixed in**: useAlarms.js with processAlarmsWithUserInfo helper function

### Debug Tools

- Use browser developer tools to inspect network requests
- Check Supabase logs for database errors
- Review Mailgun logs for webhook delivery status
- Test webhook processing with curl or similar tools

## Future Enhancements

### Planned Features
- Email notifications for alarm status changes
- Escalation rules for unacknowledged alarms
- Mobile push notifications
- Advanced reporting and analytics
- Integration with building management systems
- Automated alarm resolution for known issues

### Scalability Considerations
- Implement message queuing for high-volume processing
- Add database read replicas for reporting
- Consider microservices architecture for large deployments
- Implement caching for frequently accessed data

## Support and Maintenance

### Regular Tasks
- Monitor webhook processing success rates
- Review and update alarm type configurations
- Backup database regularly
- Update security certificates and keys
- Review and optimize database performance

### Documentation Updates
- Keep this guide updated with any changes
- Document new alarm types and formats
- Update setup instructions for new environments
- Maintain troubleshooting knowledge base
