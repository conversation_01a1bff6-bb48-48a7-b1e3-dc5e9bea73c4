# Browser Extension Error Management

This document explains how to handle browser extension-related errors that can flood the development console and mask legitimate application errors.

## Problem Description

Browser extensions often inject scripts into web pages and attempt to communicate with their background scripts. When these communications fail (which is common), they generate console errors like:

- "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received"
- "Unchecked runtime.lastError"
- "Extension context invalidated"
- "Could not establish connection. Receiving end does not exist"

These errors are **not caused by your application** but can make debugging difficult by cluttering the console.

## Solution Implemented

The JSC Alarm Call-Out App includes an automatic error filtering system that:

1. **Filters Extension Errors**: Automatically suppresses known browser extension error patterns
2. **Preserves App Errors**: Ensures legitimate application errors remain visible
3. **Provides Debug Mode**: Allows developers to see filtered errors when needed
4. **Offers Manual Control**: Provides tools to enable/disable filtering

## Automatic Error Filtering

The error filtering is automatically initialized when the application starts. It:

- Intercepts `console.error` and `console.warn` calls
- Filters out known extension error patterns
- Handles unhandled promise rejections from extensions
- Provides statistics on filtered errors

## Development Tools

### Visual Interface

In development mode, you'll see a floating settings button (⚙️) in the bottom-right corner. Click it to access:

- **Extension Detection**: See which browser extensions are detected
- **Filtering Controls**: Enable/disable error filtering and debug mode
- **Statistics**: View how many errors have been filtered
- **Console Commands**: Quick reference for manual control

### Console Commands

You can manually control error filtering using these console commands:

```javascript
// Enable debug mode to see filtered errors with [FILTERED] prefix
enableExtensionErrorDebugging()

// Disable debug mode
disableExtensionErrorDebugging()

// Completely disable error filtering (restore original console)
restoreConsole()

// Get statistics about filtered errors
getErrorFilteringStats()

// Detect problematic extensions
detectProblematicExtensions()
```

## Common Extension Types That Cause Errors

### Password Managers
- LastPass
- 1Password
- Bitwarden
- Dashlane

### Ad Blockers
- uBlock Origin
- AdBlock Plus
- Ghostery

### Developer Tools
- React DevTools
- Redux DevTools
- Vue DevTools

### Social Media Extensions
- Facebook Container
- Twitter extensions
- Pinterest Save Button

### Privacy Extensions
- Privacy Badger
- DuckDuckGo Privacy Essentials
- Disconnect

## Best Practices for Development

### 1. Use Incognito/Private Mode
Test your application in incognito/private browsing mode to avoid extension interference:
- Chrome: Ctrl+Shift+N (Windows/Linux) or Cmd+Shift+N (Mac)
- Firefox: Ctrl+Shift+P (Windows/Linux) or Cmd+Shift+P (Mac)
- Safari: Cmd+Shift+N (Mac)

### 2. Disable Non-Essential Extensions
For development, consider disabling extensions that aren't needed:
1. Go to browser extension management page
2. Temporarily disable extensions you don't need for development
3. Re-enable them when done developing

### 3. Use Error Filtering
Keep the automatic error filtering enabled to maintain a clean console while preserving application errors.

### 4. Enable Debug Mode When Needed
If you suspect an extension error might be related to your application:
```javascript
enableExtensionErrorDebugging()
```

## Troubleshooting

### Still Seeing Extension Errors?

1. **Check if filtering is active**: Look for the "Error filtering initialized" message in console
2. **Enable debug mode**: Use `enableExtensionErrorDebugging()` to see what's being filtered
3. **Add custom patterns**: Modify `src/lib/errorFilter.js` to add new error patterns

### Missing Application Errors?

1. **Disable filtering temporarily**: Use `restoreConsole()` to see all errors
2. **Check filter patterns**: Ensure your application errors don't match extension patterns
3. **Use debug mode**: Enable debug mode to see if legitimate errors are being filtered

### Performance Concerns?

The error filtering system:
- Only runs in development mode
- Has minimal performance impact
- Can be completely disabled if needed

## Configuration

### Adding Custom Error Patterns

To filter additional error patterns, edit `src/lib/errorFilter.js`:

```javascript
const EXTENSION_ERROR_PATTERNS = [
  // Add your custom patterns here
  /your-custom-error-pattern/,
  // ... existing patterns
]
```

### Excluding Application Patterns

To ensure your application errors are never filtered:

```javascript
const APPLICATION_ERROR_PATTERNS = [
  // Add your application-specific patterns here
  /your-app-specific-pattern/,
  // ... existing patterns
]
```

## Environment Variables

You can control error filtering behavior with environment variables:

```bash
# Enable extension error debugging by default
VITE_DEBUG_EXTENSION_ERRORS=true

# Disable error filtering entirely
VITE_DISABLE_ERROR_FILTERING=true
```

## Production Behavior

In production builds:
- Error filtering is automatically disabled
- Extension errors may still appear but won't affect users
- The DevTools button is hidden
- Console commands are not available

## Support

If you encounter issues with error filtering:

1. Check the browser console for initialization messages
2. Use the DevTools panel to diagnose issues
3. Try disabling and re-enabling filtering
4. Test in incognito mode to isolate extension issues

## Technical Details

The error filtering system works by:

1. **Intercepting Console Methods**: Overriding `console.error` and `console.warn`
2. **Pattern Matching**: Using regex patterns to identify extension errors
3. **Event Handling**: Listening for unhandled rejections and global errors
4. **Selective Filtering**: Only filtering errors that match known extension patterns

The system is designed to be:
- **Non-intrusive**: Doesn't affect application functionality
- **Reversible**: Can be disabled at any time
- **Transparent**: Provides visibility into what's being filtered
- **Configurable**: Allows customization of filter patterns
