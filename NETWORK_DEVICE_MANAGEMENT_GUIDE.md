# Network Device Management Feature Guide

## 📋 Overview

The Network Device Management feature provides comprehensive LAN (Local Area Network) device management capabilities for the JSC Alarm Call-Out building management system. This feature allows facility managers to track, configure, and manage all network devices within their buildings.

## ✨ Features

### **Core Functionality**
- ✅ **Complete CRUD Operations**: Create, read, update, and delete network devices
- ✅ **Building-Based Organization**: Devices are organized by building with proper access control
- ✅ **Comprehensive Device Information**: Store all essential network and authentication details
- ✅ **Real-Time Validation**: IP address, subnet mask, and network configuration validation
- ✅ **Secure Credential Storage**: Encrypted password storage for device authentication
- ✅ **Search and Filtering**: Advanced search by device name, IP, type, and building
- ✅ **Responsive Design**: Works seamlessly on desktop and mobile devices

### **Device Information Fields**
- **Station Name**: Device identifier/hostname
- **Device Type**: BACnet router, switch, access point, server, workstation, JACE, etc.
- **Host ID**: Unique device identifier within the building
- **IP Address**: IPv4 network address with validation
- **Subnet Mask**: Network mask with common presets
- **Gateway**: Default gateway IP address
- **DNS Servers**: Primary and secondary internal DNS servers
- **Authentication Credentials**: Multiple authentication layers with secure storage
  - **Station Username/Password**: Device-level authentication
  - **Windows Username/Password**: Windows domain/local authentication
  - **Platform Username/Password**: Platform-level authentication credentials
- **Security Passphrase**: Encrypted security passphrases or encryption keys
- **Software Version**: Firmware/OS version tracking
- **Notes**: Additional device information and documentation
- **Status**: Active/inactive device status

### **Security Features**
- ✅ **Row Level Security (RLS)**: Users only access devices for their authorized buildings
- ✅ **Multi-Layer Authentication**: Support for station, Windows, and platform-level credentials
- ✅ **Encrypted Credential Storage**: All passwords and passphrases are encrypted in the database
- ✅ **Secure Passphrase Support**: Dedicated field for encryption keys and security passphrases
- ✅ **Password Strength Validation**: Enforced strong password requirements with real-time feedback
- ✅ **Visibility Controls**: Toggle password visibility with secure input handling
- ✅ **Input Validation**: Comprehensive validation for all network-specific fields
- ✅ **Private IP Validation**: Ensures IP addresses are in valid private ranges
- ✅ **Unique Constraints**: Prevents duplicate host IDs and IP addresses per building
- ✅ **Secure CSV Import**: Password fields are handled securely during bulk import operations

### **User Interface Features**
- ✅ **Modal Forms**: Clean, comprehensive forms for device creation and editing
- ✅ **Data Table**: Sortable, filterable table view with device details
- ✅ **Advanced Filters**: Filter by building, device type, and search terms
- ✅ **Auto-Generation**: Automatic host ID suggestions based on station names
- ✅ **Password Visibility**: Toggle password visibility for secure entry
- ✅ **Auto-Save**: Form data is automatically saved as drafts
- ✅ **Confirmation Dialogs**: Safe delete operations with confirmation
- ✅ **Real-Time Feedback**: Success/error notifications for all operations

## 📁 Files Created

### **1. Database Schema: `database-network-devices.sql`**
- Complete database table definition with all required fields
- Indexes for optimal query performance
- Row Level Security (RLS) policies
- Helper functions for IP validation
- Triggers for audit trail maintenance

### **2. Validation Library: `src/lib/networkDeviceValidation.js`**
- Comprehensive form validation functions
- Network-specific validation (IP addresses, subnet masks, host IDs)
- Password strength validation
- Data cleaning and normalization
- Auto-save functionality for form drafts
- Device type and subnet mask constants

### **3. Custom Hook: `src/hooks/useNetworkDevices.js`**
- Network device data management and state
- CRUD operations with Supabase integration
- Uniqueness validation for host IDs and IP addresses
- Search and filtering utilities
- Status management functions
- Building-based device filtering

### **4. Main Component: `src/components/NetworkDeviceManagement.jsx`**
- Complete network device management interface
- Modal forms for create/edit operations with comprehensive fields
- Advanced filtering and search capabilities
- Data table with device information display
- Status toggle and delete confirmation
- Real-time validation and error display
- Auto-save and draft restoration
- Secure password handling with visibility toggles

### **5. Navigation Integration: `src/App.jsx`**
- Added "Network" navigation item to main app navigation
- Lazy loading for optimal performance
- Mobile-responsive navigation support

## 🚀 Getting Started

### **1. Database Setup**

Run the SQL commands from `database-network-devices.sql` in your Supabase SQL Editor:

```sql
-- The schema has been automatically deployed to your Supabase project
-- All tables, indexes, policies, and functions are ready to use
```

### **2. Access the Feature**

1. **Navigate to Network Management**: Click the "Network" tab in the main navigation
2. **Add Your First Device**: Click "Add Network Device" to create your first device
3. **Fill Device Information**: Complete all required fields (marked with *)
4. **Save and Manage**: Use the table to view, edit, and manage your devices

### **3. Key Workflows**

#### **Adding a New Device**
1. Click "Add Network Device"
2. Select the building from the dropdown
3. Choose the device type (BACnet router, switch, etc.)
4. Enter station name (host ID will be auto-generated)
5. Configure network settings (IP, subnet mask, gateway, DNS)
6. Add authentication credentials if needed
7. Include software version and notes
8. Save the device

#### **Managing Existing Devices**
1. Use search and filters to find specific devices
2. Click "Edit" to modify device information
3. Use "Activate/Deactivate" to change device status
4. Click "Delete" to remove devices (with confirmation)

#### **Bulk Management**
1. Use building filter to view devices for specific buildings
2. Use device type filter to focus on specific equipment types
3. Search by device name, IP address, or host ID
4. **CSV Import with Passwords**: Import devices with encrypted credentials
5. **Secure Export**: Export device data with password fields masked for security

### **Enhanced Authentication Management:**

#### **Multi-Layer Credential Support**
The system now supports three distinct authentication layers:

1. **Station Credentials**: Device-level authentication for direct device access
2. **Windows Credentials**: Domain or local Windows authentication
3. **Platform Credentials**: Platform-specific authentication (e.g., cloud platforms, management interfaces)

#### **Security Passphrase Management**
- **Dedicated Passphrase Field**: Store encryption keys, certificates, or security passphrases
- **Extended Length Support**: Supports longer passphrases (minimum 12 characters)
- **Secure Storage**: All passphrases are encrypted using the same security standards as passwords
- **Visibility Toggle**: Secure input with optional visibility for verification

#### **Password Security Enhancements**
- **Strength Validation**: Real-time password strength checking with specific requirements
- **Secure Input Handling**: Password fields never pre-populate for security
- **Encrypted Storage**: All credentials are encrypted before database storage
- **Bulk Import Security**: CSV import handles passwords securely with validation

## 🔧 Technical Implementation

### **Database Design**
- **Primary Table**: `network_devices` with comprehensive device information and enhanced authentication fields
- **New Authentication Fields**:
  - `platform_username` and `platform_password_encrypted`
  - `passphrase_encrypted` for security passphrases
- **Foreign Key**: Links to `buildings` table for organization
- **Constraints**: Unique host IDs and IP addresses per building
- **Indexes**: Optimized for common query patterns
- **RLS Policies**: Building-based access control
- **Encryption**: All password and passphrase fields are encrypted before storage

### **Frontend Architecture**
- **React Components**: Modular, reusable component design
- **Custom Hooks**: Centralized data management with `useNetworkDevices`
- **Validation Library**: Comprehensive input validation and sanitization
- **State Management**: Local state with auto-save functionality
- **Responsive Design**: Mobile-first approach with Tailwind CSS

### **Security Implementation**
- **Multi-Layer Encryption**: All passwords and passphrases are encrypted before database storage
- **Enhanced Authentication**: Support for station, Windows, and platform-level credentials
- **Passphrase Security**: Dedicated encrypted storage for security passphrases and encryption keys
- **Input Validation**: Client and server-side validation with strength requirements
- **Access Control**: RLS policies ensure proper data isolation
- **Network Validation**: IP address and subnet mask validation
- **Private IP Enforcement**: Ensures devices use private IP ranges
- **Secure Import/Export**: CSV operations handle credentials securely with masked exports

## 📊 Usage Examples

### **Common Device Types**
- **BACnet Router**: Building automation network routers
- **Switch**: Network switches for LAN connectivity
- **Access Point**: Wireless access points
- **Server**: Building management servers
- **Workstation**: Administrative workstations
- **JACE**: Johnson Controls automation controllers
- **Gateway**: Network gateways and firewalls

### **Network Configuration Examples**
- **Standard LAN**: 192.168.1.x with ************* subnet
- **Building Automation**: 10.0.x.x with *********** subnet
- **Segmented Networks**: 172.16.x.x with various subnet masks

### **Enhanced CSV Import Examples**

#### **CSV Template with Authentication Fields**
The updated CSV template now includes all authentication fields:

```csv
Building Name,Station Name,Device Type,Host ID,IP Address,Subnet Mask,Gateway,DNS Server 1,DNS Server 2,Station Username,Station Password,Windows Username,Windows Password,Platform Username,Platform Password,Passphrase,Software Version,Notes,Active
Example Building,MAIN-ROUTER-01,BACnet router,RTR-001-MAIN,***********,*************,***********,*******,*******,admin,SecurePassword123!,domain\admin,DomainPassword456!,platform_user,PlatformPassword789!,MySecurePassphrase2024,v2.1.3,Main building router,Yes
```

#### **Secure Import Process**
1. **Download Template**: Get the latest template with all authentication fields
2. **Fill Credentials**: Add actual passwords and passphrases (they will be encrypted)
3. **Import Validation**: System validates all fields including password strength
4. **Secure Processing**: Passwords are encrypted during import process
5. **Error Reporting**: Detailed validation errors for any issues

#### **Export Security**
- **Masked Passwords**: Exported CSV files show `***ENCRYPTED***` instead of actual passwords
- **Secure Data**: Only non-sensitive information is exported in readable format
- **Template Generation**: Fresh templates include example passwords for reference

## 🔒 Security Best Practices

### **Enhanced Password Management**
- **Multi-Layer Authentication**: Use different credentials for different access levels
- **Strong Password Requirements**: All passwords must meet strength requirements (8+ characters, mixed case, numbers)
- **Passphrase Security**: Use strong passphrases (12+ characters) for encryption keys
- **Encrypted Storage**: All credentials are automatically encrypted in the database
- **No Credential Sharing**: Never share credentials between devices or authentication layers
- **Regular Updates**: Regularly update all device passwords and passphrases
- **Secure Import**: When importing via CSV, ensure password fields contain actual credentials (not placeholders)

### **Authentication Layer Best Practices**
- **Station Credentials**: Use for direct device access and configuration
- **Windows Credentials**: Use for domain authentication and Windows-based management
- **Platform Credentials**: Use for cloud platforms, management interfaces, or specialized access
- **Passphrase Usage**: Store encryption keys, certificate passphrases, or security tokens

### **Network Security**
- Use private IP address ranges only
- Implement proper subnet segmentation
- Document all network access credentials
- Maintain current software versions

### **Access Control**
- Users only see devices for their authorized buildings
- All operations are logged with user information
- Regular access review and cleanup

## 🚀 Future Enhancements

### **Planned Features**
- **Import/Export**: Bulk device import from CSV files
- **Network Scanning**: Automatic device discovery
- **Ping Monitoring**: Real-time device connectivity status
- **Configuration Backup**: Device configuration backup and restore
- **Network Diagrams**: Visual network topology mapping
- **Maintenance Scheduling**: Device maintenance and update tracking

### **Integration Opportunities**
- **Work Order Integration**: Link devices to maintenance work orders
- **Alarm Integration**: Connect network issues to alarm notifications
- **Asset Management**: Integration with equipment management
- **Monitoring Systems**: Integration with network monitoring tools

## 📞 Support

For questions or issues with the Network Device Management feature:

1. **Check Documentation**: Review this guide and inline help text
2. **Validate Configuration**: Ensure Supabase connection is properly configured
3. **Review Logs**: Check browser console for error messages
4. **Test Permissions**: Verify user has access to the required buildings

The Network Device Management feature provides a comprehensive solution for tracking and managing all network infrastructure within your building management system, ensuring proper documentation and secure access to critical network resources.
