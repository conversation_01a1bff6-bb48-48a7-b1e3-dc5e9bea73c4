# Network Device Schema Mismatch Fix

## Problem Summary

The network device CSV import was failing with database schema errors indicating that password-related columns were missing from the network_devices table. The errors showed:

- `'passphrase'` column missing
- `'windows_password'` column missing  
- `'platform_password'` column missing

## Root Cause Analysis

### **Database Schema (Correct)**
The database actually has the correct encrypted column names:
- ✅ `station_password_encrypted`
- ✅ `windows_password_encrypted`
- ✅ `platform_password_encrypted`
- ✅ `passphrase_encrypted`

### **Application Code Issue**
The problem was in the data flow between form processing and database insertion:

1. **CSV Import** → Creates objects with non-encrypted field names (`passphrase`, `windows_password`, etc.)
2. **Data Cleaning** → `cleanNetworkDeviceFormData()` was passing through non-encrypted field names
3. **Password Processing** → `createDevice()` correctly converts to encrypted names
4. **Database Insert** → Sometimes the non-encrypted field names were reaching the database

## ✅ **Fixes Implemented**

### **1. Enhanced Password Field Processing (`src/hooks/useNetworkDevices.js`)**

**Before:**
```javascript
if (processedData.station_password) {
  processedData.station_password_encrypted = processedData.station_password
  delete processedData.station_password
}
```

**After:**
```javascript
// Handle password fields - convert to encrypted fields and remove originals
if (processedData.station_password && processedData.station_password.trim() !== '') {
  processedData.station_password_encrypted = processedData.station_password
}
delete processedData.station_password // Always delete, even if empty
```

**Key Changes:**
- ✅ **Always delete** non-encrypted field names to prevent schema errors
- ✅ **Check for non-empty values** before setting encrypted fields
- ✅ **Applied to all password fields**: station, windows, platform, passphrase

### **2. Improved Data Cleaning (`src/lib/networkDeviceValidation.js`)**

**Enhanced Logic:**
- ✅ **Conditional password inclusion** - only include password fields if they have values
- ✅ **Clear separation** between regular fields and password fields
- ✅ **Better documentation** explaining the password processing flow

### **3. Consistent Processing in Both Create and Update Functions**

Applied the same password handling logic to:
- ✅ `createDevice()` function
- ✅ `updateDevice()` function

## 🧪 **Testing Verification**

### **Database Schema Test:**
```sql
INSERT INTO network_devices (
  building_id, station_name, device_type, host_id, ip_address, subnet_mask,
  station_password_encrypted, windows_password_encrypted, 
  platform_password_encrypted, passphrase_encrypted
) VALUES (...);
```
**Result:** ✅ **Success** - Schema is correct

### **Expected CSV Import Behavior:**

**Input CSV:**
```csv
Building Name,Station Name,Station Password,Windows Password,Platform Password,Passphrase
Main Office Building,TEST-DEVICE,weak123,domain456,platform789,mypassphrase
```

**Processing Flow:**
1. **CSV Parse** → `{station_password: "weak123", windows_password: "domain456", ...}`
2. **Data Clean** → Conditionally includes password fields
3. **Password Process** → Converts to `{station_password_encrypted: "weak123", ...}`
4. **Database Insert** → Uses correct encrypted column names

## 📋 **Field Mapping Reference**

| CSV Column | Cleaned Data Field | Database Column |
|------------|-------------------|-----------------|
| Station Password | `station_password` | `station_password_encrypted` |
| Windows Password | `windows_password` | `windows_password_encrypted` |
| Platform Password | `platform_password` | `platform_password_encrypted` |
| Passphrase | `passphrase` | `passphrase_encrypted` |

## 🔄 **Data Flow (Fixed)**

```
CSV Import
    ↓
parseCSVToDevices() → {passphrase: "value", windows_password: "value"}
    ↓
cleanNetworkDeviceFormData() → Conditionally includes password fields
    ↓
createDevice() → Converts to encrypted fields + deletes originals
    ↓
Database Insert → {passphrase_encrypted: "value", windows_password_encrypted: "value"}
    ↓
✅ SUCCESS
```

## 🎯 **Key Improvements**

1. **Eliminated Schema Errors** - Non-encrypted field names never reach database
2. **Robust Password Handling** - Handles empty, null, and valid password values
3. **Consistent Processing** - Same logic for create and update operations
4. **Better Error Prevention** - Always delete temporary password fields
5. **Maintained Security** - All passwords still go to encrypted columns

## 🧪 **Testing Recommendations**

### **Test Cases to Verify:**

1. **CSV with all password fields populated**
2. **CSV with some password fields empty**
3. **CSV with no password fields**
4. **Manual form submission with passwords**
5. **Device updates with password changes**

### **Expected Results:**
- ✅ No "column not found" errors
- ✅ Passwords stored in encrypted columns
- ✅ Empty passwords handled gracefully
- ✅ CSV import completes successfully

The schema mismatch has been resolved, and CSV imports should now process password fields correctly without database errors.
