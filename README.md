# JSC Comprehensive Building Management System (BMS)

A world-class commercial building management system built with modern web technologies. This application provides comprehensive monitoring and management of building systems including HVAC, energy, security, fire safety, equipment, and maintenance operations.

## 🏢 System Overview

This BMS integrates multiple building systems into a unified platform:

- **Alarm Management**: Real-time alarm monitoring with Mailgun webhook integration
- **Equipment Management**: Complete asset tracking and maintenance scheduling
- **Energy Management**: Consumption monitoring, cost tracking, and efficiency analytics
- **Work Order Management**: Maintenance request tracking and technician dispatch
- **Building Management**: Multi-building portfolio management
- **Real-time Monitoring**: Live system status and performance metrics

## 🚀 Technology Stack

- **Frontend Framework**: React 19 with Vite
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **Backend**: Supabase (Database + Authentication + Real-time)
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Build Tool**: Vite
- **Package Manager**: npm
- **Communication Protocols**: BACnet/IP, Modbus, MQTT (via webhooks)

## 🎯 Key Features

### 👤 User Profile Management System
- **Complete 4-tab interface**: Personal, Professional, Roles & Access, Activity
- **Professional user information**: Department, job title, employee ID, certifications
- **Emergency contact management**: Name, phone, relationship for safety compliance
- **Role-based access control**: Building-specific permissions and access levels
- **Activity tracking**: Login history, system interactions, audit trails
- **Real-time form validation**: Comprehensive error handling and user feedback

### ⚙️ Settings & Preferences Management
- **Dashboard customization**: Default view, auto-refresh intervals, UI preferences
- **Notification controls**: Email/SMS/in-app notifications by alarm severity
- **Display preferences**: Theme, timezone, date/time formats, measurement units
- **Security settings**: Password change, 2FA setup (ready for implementation)
- **Quiet hours scheduling**: Reduce notifications during specified times

### 🛡️ Browser Extension Error Filtering
- **Automatic error filtering**: Suppresses 20+ common browser extension error patterns
- **Clean development console**: Filters extension noise while preserving app errors
- **Visual DevTools panel**: Manage filtering, view statistics, debug mode
- **Console commands**: Programmatic control for advanced debugging
- **Extension detection**: Automatically identifies problematic browser extensions

### 📊 BMS Overview Dashboard
- **System-wide monitoring**: Real-time status of all building systems
- **Critical alerts**: Immediate notification of critical issues requiring attention
- **Performance metrics**: Equipment status, energy consumption, work order tracking
- **Quick actions**: Direct access to key management functions

### 🚨 Advanced Alarm Management
- **Real-time processing**: Mailgun webhook integration for instant alarm notifications
- **Intelligent categorization**: Automatic alarm type and severity classification
- **Multi-view interface**: Card and table views for different user preferences
- **Status tracking**: Complete alarm lifecycle from receipt to resolution
- **Escalation management**: Automatic escalation for unacknowledged critical alarms

### ⚙️ Comprehensive Equipment Management
- **Asset hierarchy**: Parent-child equipment relationships
- **Lifecycle tracking**: Installation dates, warranty information, maintenance schedules
- **Status monitoring**: Real-time equipment status (active, maintenance, offline)
- **Location management**: Detailed location tracking with floor, room, and zone information
- **Maintenance integration**: Direct work order creation from equipment issues

### ⚡ Energy Management & Analytics
- **Multi-utility monitoring**: Electricity, gas, water, steam, chilled water tracking
- **Cost analysis**: Real-time cost tracking and budget monitoring
- **Consumption analytics**: Historical trends and usage patterns
- **Demand management**: Peak demand tracking and optimization
- **Sustainability metrics**: Carbon footprint and efficiency calculations

### 🔧 Work Order Management System
- **Complete lifecycle**: From creation to completion with full audit trail
- **Priority management**: Critical, high, medium, low priority classification
- **Resource allocation**: Technician assignment and scheduling
- **Cost tracking**: Estimated vs. actual hours and costs
- **Integration**: Automatic work order creation from alarms and equipment issues

### 🏢 Multi-Building Portfolio Management
- **Centralized control**: Manage multiple buildings from single interface
- **Building-specific data**: Isolated data with proper access controls
- **Unique identifiers**: Automatic building code generation
- **Contact management**: Building-specific contact information and escalation procedures

## 📋 Prerequisites

- Node.js (version 18 or higher)
- npm or yarn
- A Supabase account and project
- Mailgun account for webhook integration (optional)

## 🛠️ Setup Instructions

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone <your-repo-url>
cd jsc-alarm-call-out-app

# Install dependencies
npm install
```

### 2. Supabase Configuration

**Note**: The application will work without Supabase configuration, but authentication and database features will be disabled.

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to your project settings and find your project URL and anon key
3. Update `.env.local` with your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your_actual_anon_key_here
   ```
4. Restart the development server to apply changes

### 3. Database Setup (Required for BMS Functionality)

To enable the comprehensive Building Management System, you need to create the required database schema:

1. **Go to your Supabase Dashboard** → SQL Editor
2. **Copy and run the complete SQL** from `database-setup.sql` file in this project

**Core Tables Created:**
- `buildings` - Building information and contact details
- `alarm_notifications` - Real-time alarm processing and tracking
- `equipment` - Asset management with hierarchical relationships
- `sensor_readings` - Time-series sensor data storage
- `energy_data` - Energy consumption and cost tracking
- `occupancy_data` - Space utilization monitoring
- `work_orders` - Maintenance and service request management
- `user_roles` - Role-based access control
- `user_profiles` - Extended user profile information
- `user_preferences` - Dashboard and system preferences
- `notification_preferences` - Alarm notification settings
- `user_activity` - Activity logging and audit trails
- `call_outs` - Technician dispatch and response tracking
- `severity_levels` - Alarm severity classification
- `alarm_types` - Categorized alarm types

**Features Included:**
- ✅ Row Level Security (RLS) policies for data protection
- ✅ Automatic timestamp triggers for audit trails
- ✅ Optimized indexes for high-performance queries
- ✅ JSONB storage for flexible metadata
- ✅ Foreign key relationships for data integrity
- ✅ Sample data for immediate testing

**Note**: The complete SQL setup with all tables, policies, indexes, and sample data is available in the `database-setup.sql` file.

### 4. Run the Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:5173`

**🚀 Quick Start**: The app works immediately! Click "View Demo" to explore all features without any setup.

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # Shadcn/ui components (Avatar, Dialog, Tabs, etc.)
│   ├── LoginForm.jsx   # Authentication form
│   ├── UserProfile.jsx # User profile dropdown with avatar
│   ├── UserInfoModal.jsx # User information management modal
│   ├── SettingsModal.jsx # Settings and preferences modal
│   ├── DevTools.jsx    # Development tools for error filtering
│   ├── AlarmDashboard.jsx # Alarm monitoring dashboard
│   ├── BMSDashboard.jsx # Building management system overview
│   ├── BuildingManagement.jsx # Building management interface
│   ├── EquipmentManagement.jsx # Equipment management interface
│   ├── EnergyDashboard.jsx # Energy monitoring dashboard
│   └── WorkOrderManagement.jsx # Work order management interface
├── contexts/           # React contexts
│   └── AuthContext.jsx # Authentication context
├── hooks/              # Custom React hooks
│   ├── useAlarms.js    # Alarm-specific operations hook
│   ├── useBuildings.js # Building management operations hook
│   └── useUserProfile.js # User profile management hook
├── lib/                # Utility libraries
│   ├── supabase.js     # Supabase client configuration
│   ├── utils.js        # Utility functions
│   ├── alarmUtils.js   # Alarm parsing and processing utilities
│   ├── edgeFunctionClient.js # Edge function client utilities
│   ├── webhookHandler.js # Mailgun webhook processing
│   ├── errorFilter.js  # Browser extension error filtering
│   └── demoData.js     # Demo data for user profile features
├── pages/              # Page components (for future routing)
├── App.jsx             # Main application component
├── main.jsx            # Application entry point
├── index.css           # Global styles with Tailwind
└── BROWSER_EXTENSION_ERRORS.md # Documentation for error filtering
```

## 🎨 Available Components

The application includes comprehensive pre-configured Shadcn/ui components:

- **Avatar**: User profile images with fallback initials
- **Button**: Various button styles and sizes
- **Card**: Container component for content sections
- **Dialog**: Modal dialogs for user interactions
- **Form**: Form components with validation
- **Input**: Form input fields
- **Label**: Form labels with accessibility
- **Select**: Dropdown selection components
- **Switch**: Toggle switches for preferences
- **Tabs**: Tabbed interface components
- **Textarea**: Multi-line text input
- **Badge**: Status and category indicators
- **Separator**: Visual content dividers

To add more Shadcn/ui components:

```bash
npx shadcn@latest add [component-name]
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🔐 Authentication

The application includes a complete authentication system using Supabase Auth:

- User registration and login
- Session management
- Protected routes
- Automatic token refresh

## 📊 Database Operations

The application provides specialized hooks for different data operations:

```javascript
// Alarm operations
const { alarms, loading, error, acknowledgeAlarm, resolveAlarm } = useAlarms()

// Building management operations
const { buildings, loading, error, addBuilding, updateBuilding } = useBuildings()
```

## 🎯 Features Demonstrated

- ✅ React with Vite setup
- ✅ Tailwind CSS integration
- ✅ Comprehensive Shadcn/ui component library
- ✅ Supabase authentication and database operations
- ✅ Row Level Security (RLS) with user-specific data access
- ✅ User profile management with 4-tab interface
- ✅ Settings and preferences management
- ✅ Browser extension error filtering system
- ✅ Visual development tools and debugging
- ✅ Demo mode (works without Supabase setup)
- ✅ Mobile-responsive design with hamburger navigation
- ✅ Dark/light mode support (via Tailwind)
- ✅ Advanced form handling and validation
- ✅ Professional loading states and error handling
- ✅ Activity logging and audit trails
- ✅ Role-based access control
- ✅ Real-time data updates
- ✅ Graceful fallbacks for missing configuration

## 🚨 Alarm System Features

This application now includes a comprehensive alarm notification system that integrates with Mailgun webhooks:

### Database Schema
- **Buildings**: Store building information with unique email addresses
- **Alarm Types**: Categorized alarm types (Fire, Security, HVAC, etc.)
- **Severity Levels**: LOW, MEDIUM, HIGH, CRITICAL with color coding
- **Alarm Notifications**: Complete alarm records with processing status

### Key Features
- ✅ **Mailgun Webhook Integration**: Process incoming alarm emails automatically
- ✅ **Real-time Alarm Dashboard**: Monitor all alarms with filtering and status management
- ✅ **Dual View Modes**: Switch between detailed Card View and compact Table View
- ✅ **Comprehensive Building Management**: Full CRUD operations for buildings with automatic email generation
- ✅ **Alarm Processing**: Parse email content to extract alarm details
- ✅ **Status Tracking**: Received → Acknowledged → Resolved workflow
- ✅ **Test Functionality**: Simulate alarm notifications for testing
- ✅ **Responsive Design**: Works on desktop and mobile devices

### View Modes
The Alarm Dashboard supports two display modes:

**Card View (Default)**
- Detailed cards showing complete alarm information
- Full alarm details, location, and timestamps
- Large action buttons for easy interaction
- Best for detailed review and comprehensive information

**Table View**
- Compact table format for efficient scanning
- Columns: Building, Type, Severity, Time, Status, Details, Actions
- Hover tooltips for truncated information
- Ideal for monitoring multiple alarms quickly
- Responsive design with horizontal scrolling on mobile

Switch between views using the toggle buttons in the dashboard header.

### Building Management
The app includes a comprehensive building management interface:

**Features**
- Create, edit, and delete buildings with full form validation
- Automatic generation of unique alarm email addresses (`bldg-[random-id]@mg.stieralarms.online`)
- Search and filter buildings by name, code, or address
- Toggle building active/inactive status
- Auto-save form drafts with restoration capability
- Real-time validation with clear error messages

**Building Information**
- Building Name and Address (required)
- Unique alarm email address (auto-generated)
- Building code (optional, auto-suggested from name)
- Contact phone and email (optional)
- Active/inactive status management

**Integration**
- New buildings immediately appear in alarm filtering options
- Building updates reflect in real-time across the application
- Maintains compatibility with existing alarm notification system

### Alarm Email Format
The system expects alarm emails in this format:
```
Subject: ALARM NOTIFICATION
To: bldg-[unique-id]@mg.stieralarms.online

Time: 4/27/2025, 11:16:06 PM EST
Alarm Type: Fire Detection System
Severity: CRITICAL
Details: Smoke detectors have been triggered in the specified location.
```

### Webhook Setup
To receive real alarm notifications:
1. Set up a server-side webhook endpoint (see `src/lib/webhookHandler.js` for instructions)
2. Configure Mailgun to send webhooks to your endpoint
3. Implement proper signature verification for security
4. Use the provided parsing utilities to process alarm emails

## 🚀 Next Steps

1. Set up your Supabase project and configure environment variables
2. Run the database setup SQL to create alarm tables
3. Configure Mailgun webhook endpoint for production use
4. Add more buildings and customize alarm types as needed
5. Implement additional features like:
   - Email notifications for alarm status changes
   - Escalation rules for unacknowledged alarms
   - Reporting and analytics dashboard
   - Mobile push notifications
   - Integration with building management systems

## 📝 Notes

- The application uses React 19, which may have peer dependency warnings with some packages
- Shadcn/ui components are installed with `--force` flag to handle React 19 compatibility
- All components are fully accessible and follow modern React patterns
- The project is configured for both development and production builds
- Supabase client uses a singleton pattern to prevent multiple instances during development

## 🐛 Troubleshooting

**Multiple GoTrueClient instances warning**: This has been resolved with a singleton pattern, but if you see this warning, it's harmless and only occurs during development with hot module replacement.

**White screen on load**: Make sure your `.env.local` file has commented-out placeholder values or valid Supabase credentials.
