# JSC Alarm Call-Out Voice Agent Configuration

## Agent Details

**Agent ID**: `agent_beac3aef1a176d48c4d85d2541`
**LLM ID**: `llm_f42d54584f5cfc07e6ee62b1cb83`
**Agent Name**: JSC Alarm Call-Out Agent
**Voice**: <PERSON> (ElevenLabs) - Professional American male voice
**Language**: English (US)

## Configuration Summary

This voice agent has been specifically configured for the JSC Building Management alarm call-out system with the following optimizations:

### Voice Settings
- **Voice Model**: eleven_turbo_v2_5 (fast, high-quality)
- **Voice Speed**: 1.0 (normal pace for clarity)
- **Voice Temperature**: 0.2 (consistent, professional tone)
- **Responsiveness**: 0.8 (quick to respond)
- **Interruption Sensitivity**: 0.7 (allows natural conversation flow)

### Emergency Communication Features
- **Max Call Duration**: 10 minutes (600,000ms)
- **Silence Timeout**: 10 seconds before ending call
- **Voicemail Detection**: Enabled with custom emergency message
- **Backchannel**: Enabled with professional acknowledgments
- **Boosted Keywords**: alarm, emergency, building, acknowledged, on-way, etc.

### Post-Call Analysis
The agent automatically analyzes each call and extracts:
- **Acknowledgment Status**: acknowledged, not_acknowledged, voicemail, no_answer
- **Response Status**: on_way, acknowledged, need_assistance, cannot_respond, no_response
- **Estimated Response Time**: Contact's stated ETA
- **Transfer Requested**: Whether contact needs to transfer to someone else
- **Additional Notes**: Any extra information provided

## Integration with Existing System

This agent integrates seamlessly with the existing JSC alarm system:

### Database Integration
- **alarm_notifications**: Source of alarm data for calls
- **call_outs**: Tracks call-out records and escalation
- **call_out_attempts**: Logs individual call attempts
- **escalation_contacts**: Contact information and priority levels

### Escalation Service Integration
The agent works with `src/lib/escalationService.js`:
- Receives alarm data through dynamic variables
- Updates call status via webhooks
- Supports automatic escalation to next contact level
- Tracks acknowledgment and response status

### Dynamic Variables
The LLM uses these variables for personalized calls:
```javascript
{
  "building_name": "Building name from database",
  "building_address": "Physical address",
  "alarm_type": "Type of alarm (fire, security, HVAC, etc.)",
  "alarm_severity": "Critical/High/Medium/Low",
  "alarm_location": "Specific location within building",
  "alarm_details": "Detailed alarm description",
  "contact_name": "Name of person being called",
  "contact_role": "Their role (Facility Manager, Security, etc.)",
  "escalation_level": "Current escalation level (1, 2, 3)",
  "retry_attempt": "Attempt number for this contact",
  "webhook_url": "Supabase Edge Function URL for callbacks"
}
```

## Call Flow Design

### 1. Call Initiation
```
"Hello, this is the JSC Building Management emergency alarm notification system. 
I'm calling regarding a {{alarm_severity}} alarm at {{building_name}}. 
Is this {{contact_name}}?"
```

### 2. Alarm Details Delivery
- Building name and address
- Alarm type and severity level
- Specific location within building
- Alarm details and timestamp

### 3. Confirmation and Response Collection
- Confirms contact understands the alarm
- Collects response status:
  - "Acknowledged" - Will handle the alarm
  - "On-way" - Responding to location
  - "Need assistance" - Requires escalation
  - "Cannot respond" - Unavailable, needs transfer

### 4. Call Completion
- Records response in database via webhook
- Ends call appropriately
- Triggers escalation if needed

## Available Tools

### 1. end_alarm_call
Ends the call after successfully collecting alarm acknowledgment and response information.

### 2. transfer_to_backup_contact
Transfers to next available emergency contact when primary contact is unavailable.

### 3. record_alarm_response
Records the contact's response including acknowledgment status and ETA via webhook.

### 4. escalate_alarm
Escalates to next contact level when current contact cannot respond adequately.

## Environment Variables Required

Add these to your application environment:

```bash
# Retell AI Configuration
RETELL_AI_API_KEY=your_retell_ai_api_key
RETELL_AI_AGENT_ID=agent_beac3aef1a176d48c4d85d2541
RETELL_AI_LLM_ID=llm_f42d54584f5cfc07e6ee62b1cb83
RETELL_AI_FROM_NUMBER=your_purchased_phone_number
RETELL_AI_WEBHOOK_SECRET=your_webhook_secret

# Supabase Configuration (for webhooks)
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Usage Example

```javascript
import { retellAI } from './lib/retellAI.js'

// Create a call for an alarm
const callResult = await retellAI.createPhoneCall({
  toNumber: '+15551234567',
  alarmData: {
    id: 'alarm-uuid',
    building_id: 'building-uuid',
    alarm_type: 'Fire Alarm',
    severity: { name: 'Critical' },
    alarm_details: 'Smoke detected in server room',
    location_details: 'Building A, Floor 3, Server Room 301'
  },
  contactData: {
    id: 'contact-uuid',
    contact_name: 'John Smith',
    contact_role: 'Facility Manager'
  },
  metadata: {
    call_out_id: 'callout-uuid',
    escalation_level: 1,
    retry_attempt: 1
  }
})
```

## Monitoring and Analytics

The agent provides comprehensive call analytics:
- Call duration and completion status
- Acknowledgment detection accuracy
- Response time tracking
- Escalation effectiveness
- Contact availability patterns

## Next Steps

1. **Deploy Webhook Handler**: Set up Supabase Edge Function for call status updates
2. **Update Environment Variables**: Add the agent and LLM IDs to your configuration
3. **Test Integration**: Run test calls to verify the complete workflow
4. **Monitor Performance**: Track call success rates and response times
5. **Optimize Prompts**: Refine based on real-world usage patterns

## Testing the Agent

Use the provided test script to verify the agent configuration:

```bash
# 1. Copy environment template
cp .env.retell-ai.example .env.local

# 2. Fill in your actual API keys and phone numbers in .env.local

# 3. Run the test script
node test-voice-agent.js
```

The test script will:
- Verify agent configuration
- Test phone number formatting
- Test acknowledgment analysis
- Optionally place a real test call (when configured)

## Deployment Checklist

### 1. Retell AI Setup
- [ ] Create Retell AI account
- [ ] Purchase phone number
- [ ] Note down API key and phone number
- [ ] Agent and LLM are already created (IDs provided above)

### 2. Environment Configuration
- [ ] Copy `.env.retell-ai.example` to `.env.local`
- [ ] Fill in all required environment variables
- [ ] Generate strong webhook secret
- [ ] Verify Supabase URLs and keys

### 3. Database Setup
- [ ] Run `database-retell-ai-integration.sql` if not already done
- [ ] Verify escalation_contacts table exists
- [ ] Add test escalation contacts for your buildings
- [ ] Verify call_outs and call_out_attempts tables

### 4. Webhook Handler
- [ ] Deploy Supabase Edge Function for webhook handling
- [ ] Test webhook endpoint accessibility
- [ ] Verify webhook signature validation

### 5. Integration Testing
- [ ] Run test script with your phone number
- [ ] Place test call and verify call flow
- [ ] Check database records are created correctly
- [ ] Verify escalation logic works

### 6. Production Deployment
- [ ] Update production environment variables
- [ ] Configure real escalation contacts
- [ ] Set up monitoring and alerting
- [ ] Train staff on new voice call system

## Troubleshooting

### Common Issues

#### "Retell AI is not properly configured"
- Check environment variables are loaded correctly
- Verify API key is valid and has correct permissions
- Ensure phone number is in E.164 format

#### Calls not connecting
- Verify from_number is purchased and active in Retell AI
- Check to_number format (must be E.164)
- Review Retell AI dashboard for call logs

#### Webhooks not working
- Verify webhook URL is accessible from internet
- Check webhook secret matches between Retell AI and your app
- Review Supabase Edge Function logs

#### Agent not responding correctly
- Check dynamic variables are being passed correctly
- Review LLM prompts and tools configuration
- Monitor call transcripts for issues

## Support

For issues or questions:
- Check Retell AI dashboard for call logs and analytics
- Review Supabase Edge Function logs for webhook processing
- Monitor database call_out_attempts table for detailed call tracking
- Test with the provided test script for debugging
- Review agent configuration in Retell AI dashboard
