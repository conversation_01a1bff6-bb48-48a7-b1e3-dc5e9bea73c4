# Retell AI Integration Guide for JSC Alarm Call-Out App

This guide provides comprehensive instructions for integrating Retell AI voice calling capabilities into the JSC Alarm Call-Out App for automated alarm notifications with escalation.

## Overview

The Retell AI integration enables:
- **Automated voice calls** for critical alarm notifications
- **Escalation sequences** with configurable contact priorities
- **Call status tracking** and acknowledgment detection
- **Integration with existing alarm deduplication** system
- **Comprehensive call-out logging** and history

## Prerequisites

1. **Retell AI Account**: Sign up at [https://retellai.com](https://retellai.com)
2. **Phone Number**: Purchase a phone number through Retell AI
3. **AI Agent**: Create and configure an AI agent for alarm notifications
4. **Webhook Endpoint**: Set up webhook handling for call status updates

## Setup Instructions

### 1. Retell AI Dashboard Configuration

#### Create an AI Agent
1. Log into Retell AI Dashboard
2. Navigate to "Agents" → "Create Agent"
3. Configure the agent with alarm notification prompts:

```
You are an emergency alarm notification system for a commercial building management company. 

Your role is to call facility managers and emergency contacts when critical alarms are received from building systems.

When you call someone:
1. Identify yourself as the JSC Alarm Notification System
2. State that you're calling about a {{alarm_type}} alarm at {{building_name}}
3. Provide the alarm details: {{alarm_details}}
4. Mention the alarm occurred at {{alarm_time}}
5. Ask the contact to acknowledge receipt of this alarm notification
6. If they acknowledge, thank them and end the call
7. If they don't acknowledge or seem confused, repeat the information once more

Key phrases that indicate acknowledgment:
- "acknowledged", "confirm", "confirmed", "received", "understand"
- "got it", "copy", "roger", "yes", "okay", "ok"
- "will handle", "on my way", "responding", "en route"

Be professional, clear, and concise. This is an emergency notification system.
```

#### Purchase Phone Number
1. Go to "Phone Numbers" → "Buy Number"
2. Select a number in your desired area code
3. Note the number in E.164 format (e.g., +1234567890)

#### Configure Webhooks
1. Navigate to "Webhooks"
2. Add webhook URL: `https://your-supabase-project.supabase.co/functions/v1/retell-webhook-handler`
3. Select events: `call_started`, `call_ended`, `call_analyzed`
4. Generate and save the webhook secret

### 2. Environment Configuration

Update your `.env.local` file with Retell AI credentials:

```env
# Retell AI Configuration
RETELL_AI_API_KEY=your_retell_ai_api_key_here
RETELL_AI_FROM_NUMBER=+1234567890
RETELL_AI_AGENT_ID=your_retell_ai_agent_id_here
RETELL_AI_WEBHOOK_SECRET=your_retell_ai_webhook_secret_here

# Call-out Configuration
MAX_ESCALATION_LEVEL=3
ESCALATION_TIMEOUT_MINUTES=5
MAX_RETRY_ATTEMPTS=2
```

### 3. Database Setup

Run the database migration script to add Retell AI support:

```sql
-- Run this in your Supabase SQL Editor
-- See database-retell-ai-integration.sql for complete script
```

Key tables added:
- `escalation_contacts`: Contact information and priority levels
- Enhanced `call_outs`: Retell AI integration fields
- `call_out_attempts`: Detailed call attempt logging

### 4. Supabase Edge Function Deployment

Deploy the Retell AI webhook handler:

```bash
# Using Supabase CLI
supabase functions deploy retell-webhook-handler

# Or deploy via Supabase Dashboard
# Upload the function code from supabase/functions/retell-webhook-handler/
```

### 5. Configure Escalation Contacts

Use the Escalation Contacts Management component to set up contacts:

1. Navigate to the Escalation Contacts page
2. Add contacts for each building with:
   - Contact name and phone number (E.164 format)
   - Priority level (1 = highest priority)
   - Role (facility manager, security, etc.)
   - Available hours and notification preferences

## How It Works

### Alarm Processing Flow

1. **Alarm Received**: Mailgun webhook processes incoming alarm email
2. **Deduplication Check**: System checks for duplicate alarms using building alarm ID
3. **Severity Assessment**: Determines if alarm requires escalation (Critical/High/Emergency)
4. **Escalation Trigger**: If criteria met, starts escalation process
5. **Contact Selection**: Finds highest priority available contact for the building
6. **Voice Call**: Retell AI places call to contact
7. **Status Monitoring**: Webhook updates track call progress
8. **Acknowledgment Detection**: AI analyzes transcript for acknowledgment keywords
9. **Escalation Logic**: If not acknowledged, escalates to next contact or retries

### Escalation Logic

```
Level 1: Primary Contact (Priority 1)
├── Call Attempt 1
├── Call Attempt 2 (if no answer)
├── Wait 5 minutes
└── Escalate to Level 2

Level 2: Secondary Contact (Priority 2)
├── Call Attempt 1
├── Call Attempt 2 (if no answer)
├── Wait 5 minutes
└── Escalate to Level 3

Level 3: Emergency Contact (Priority 3)
├── Call Attempt 1
├── Call Attempt 2 (if no answer)
└── Mark as Failed (max escalation reached)
```

### Call Status Flow

```
pending → calling → answered/no_answer/busy/failed
                 ↓
              acknowledged/not_acknowledged
                 ↓
           completed/escalated
```

## API Integration

### Key Services

#### RetellAI Service (`src/lib/retellAI.js`)
- `createPhoneCall()`: Initiate outbound calls
- `getCall()`: Retrieve call details
- `verifyWebhookSignature()`: Validate webhook authenticity
- `analyzeAcknowledgment()`: Parse transcripts for acknowledgment

#### Escalation Service (`src/lib/escalationService.js`)
- `startEscalation()`: Begin escalation process
- `makeCall()`: Place individual calls
- `handleCallCompletion()`: Process webhook events
- `escalateCallOut()`: Move to next contact level

### Webhook Events

The system handles these Retell AI webhook events:

#### call_started
- Updates call status to "calling"
- Records call start time

#### call_ended
- Updates call duration and status
- Triggers escalation if call failed

#### call_analyzed
- Analyzes transcript for acknowledgment
- Completes call-out if acknowledged
- Escalates if not acknowledged

## Components

### Escalation Contacts Management
- CRUD operations for escalation contacts
- Phone number validation
- Priority level management
- Building association

### Call-Out History
- View all call-out records
- Filter by status and building
- Detailed call attempt information
- Access to call recordings and transcripts

### Enhanced Alarm Dashboard
- Manual escalation triggers
- Call-out status indicators
- Integration with existing alarm management

## Testing

### Test Escalation Flow

1. **Create Test Contacts**: Add test phone numbers for a building
2. **Send Test Alarm**: Use webhook testing to send a critical alarm
3. **Monitor Call-Out**: Check Call-Out History for progress
4. **Verify Escalation**: Ensure calls escalate properly if not answered

### Webhook Testing

Use tools like ngrok for local testing:

```bash
# Expose local development server
ngrok http 54321

# Update Retell AI webhook URL to ngrok URL
https://your-ngrok-url.ngrok.io/functions/v1/retell-webhook-handler
```

## Troubleshooting

### Common Issues

#### Calls Not Being Placed
- Check Retell AI API key configuration
- Verify phone number format (E.164)
- Ensure agent ID is correct
- Check Supabase logs for errors

#### Webhooks Not Working
- Verify webhook URL is accessible
- Check webhook secret configuration
- Review Supabase Edge Function logs
- Validate webhook signature verification

#### Escalation Not Triggering
- Confirm alarm severity levels
- Check building association
- Verify escalation contacts exist
- Review shouldTriggerEscalation logic

### Debugging Tools

#### Supabase Logs
```sql
-- Check call-out records
SELECT * FROM call_outs ORDER BY created_at DESC LIMIT 10;

-- Check call attempts
SELECT * FROM call_out_attempts ORDER BY created_at DESC LIMIT 10;

-- Check escalation contacts
SELECT * FROM escalation_contacts WHERE building_id = 'your-building-id';
```

#### Retell AI Dashboard
- Monitor call logs and recordings
- Review agent performance metrics
- Check webhook delivery status

## Security Considerations

1. **Webhook Signature Verification**: Always verify webhook signatures
2. **API Key Protection**: Store API keys securely in environment variables
3. **Phone Number Validation**: Validate all phone numbers before calling
4. **Access Control**: Implement proper RLS policies for call-out data
5. **Audit Logging**: Maintain comprehensive logs of all call activities

## Cost Management

### Retell AI Pricing
- Monitor call volume and duration
- Set up usage alerts
- Consider peak hours for escalation timing
- Review monthly usage reports

### Optimization Tips
- Use appropriate escalation timeouts
- Limit retry attempts
- Configure available hours to avoid unnecessary calls
- Implement smart escalation based on alarm severity

## Support and Maintenance

### Regular Tasks
- Review call-out success rates
- Update escalation contacts
- Monitor webhook delivery
- Analyze acknowledgment patterns

### Monitoring
- Set up alerts for failed escalations
- Track response times
- Monitor Retell AI service status
- Review call quality metrics

## Future Enhancements

### Planned Features
- SMS fallback for failed voice calls
- Multi-language support
- Advanced acknowledgment detection
- Integration with calendar systems for availability
- Automated escalation schedule adjustments

### Integration Opportunities
- HVAC system integration
- Security system alerts
- Fire safety notifications
- Energy management alarms

---

For technical support, refer to:
- [Retell AI Documentation](https://docs.retellai.com)
- [Supabase Edge Functions Guide](https://supabase.com/docs/guides/functions)
- JSC Alarm System internal documentation
