# Sentry Integration Guide for JSC Alarm Call-Out App

This guide provides comprehensive instructions for the Sentry error monitoring and performance tracking integration in the JSC Alarm Call-Out App.

## Overview

The JSC Alarm Call-Out App is now integrated with Sentry for production-ready error monitoring, performance tracking, and debugging capabilities. This integration provides:

- **Comprehensive Error Tracking**: Automatic capture of JavaScript errors, unhandled promise rejections, and React component errors
- **Performance Monitoring**: Real-time performance metrics, transaction tracking, and browser profiling
- **User Context**: Automatic user identification and context for better debugging
- **Alarm-Specific Context**: Custom context for alarm and building operations
- **Source Maps**: Production debugging with original source code
- **Error Filtering**: Intelligent filtering of browser extension errors and noise
- **Session Replay**: Visual debugging for critical errors (production only)

## Sentry Project Information

- **Organization**: `s-tier-building-automation-3b`
- **Project**: `alarm-call-out`
- **Dashboard URL**: https://s-tier-building-automation-3b.sentry.io
- **DSN**: `https://<EMAIL>/****************`

## Configuration

### Environment Variables

Create a `.env.local` file based on `.env.sentry.example`:

```bash
# Copy the example file
cp .env.sentry.example .env.local

# Edit with your values
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here
SENTRY_RELEASE=jsc-alarm-app@1.0.0
VITE_APP_VERSION=1.0.0
NODE_ENV=production
```

### Getting a Sentry Auth Token

1. Go to https://sentry.io/settings/account/api/auth-tokens/
2. Create a new token with these scopes:
   - `project:releases`
   - `org:read`
   - `project:write` (for source maps)
3. Copy the token to your `.env.local` file

## Features

### 1. Error Tracking

**Automatic Error Capture:**
- JavaScript errors and exceptions
- Unhandled promise rejections
- React component errors via Error Boundaries
- Network errors and failed API calls

**Custom Error Capture:**
```javascript
import { captureError, captureMessage } from '../lib/sentry'

// Capture an error with context
try {
  // risky operation
} catch (error) {
  captureError(error, {
    operation: 'alarm_processing',
    building_id: 'bldg-123'
  })
}

// Capture a custom message
captureMessage('Alarm processing completed', 'info', {
  alarms_processed: 5,
  duration_ms: 150
})
```

### 2. Performance Monitoring

**Automatic Performance Tracking:**
- Page load times
- Component render times
- API request durations
- Browser profiling (production only)

**Custom Performance Tracking:**
```javascript
import { Sentry } from '../lib/sentry'

const transaction = Sentry.startTransaction({
  name: 'alarm-processing',
  op: 'alarm.process'
})

// Do work...

transaction.setData('alarms_processed', 10)
transaction.finish()
```

### 3. User Context

**Automatic User Tracking:**
User context is automatically set when users log in via the AuthContext integration:

```javascript
// Automatically called on login
setSentryUser({
  id: user.id,
  email: user.email,
  username: user.email
})
```

### 4. Alarm-Specific Context

**Alarm Context:**
```javascript
import { setSentryAlarmContext } from '../lib/sentry'

setSentryAlarmContext({
  building_id: 'bldg-123',
  alarm_type: 'FIRE_ALARM',
  severity: 'CRITICAL'
})
```

**Building Context:**
```javascript
import { setSentryBuildingContext } from '../lib/sentry'

setSentryBuildingContext({
  building_id: 'bldg-123',
  building_type: 'COMMERCIAL_OFFICE'
})
```

### 5. Error Boundaries

**Automatic Error Boundaries:**
The app is wrapped with Sentry error boundaries that provide:
- Graceful error handling
- User-friendly error messages
- Automatic error reporting
- Error recovery options

**Custom Error Boundaries:**
```javascript
import { withSentryErrorBoundary } from '../components/SentryErrorBoundary'

const MyComponent = () => {
  // Component code
}

export default withSentryErrorBoundary(MyComponent, {
  context: { component: 'AlarmDashboard' }
})
```

### 6. Error Filtering

The integration includes intelligent error filtering to reduce noise:

- **Browser Extension Errors**: Automatically filtered out
- **Network Errors**: Common network issues are filtered
- **Development vs Production**: Different filtering rules
- **Integration with Existing Filter**: Works with the existing error filter system

## Testing the Integration

### Development Testing

1. **Open Development Tools**: Click the floating dev tools button (development only)
2. **Navigate to Sentry Testing Tab**: Test various Sentry features
3. **Run Tests**: Use the test buttons to verify integration
4. **Check Sentry Dashboard**: Verify events appear in your Sentry project

### Test Components Available

- **Basic Error Capture**: Test error reporting
- **Custom Messages**: Test message capture
- **Alarm Context**: Test alarm-specific context
- **Building Context**: Test building-specific context
- **Performance Tracking**: Test transaction monitoring
- **Error Hooks**: Test React hook integration
- **Breadcrumbs**: Test breadcrumb functionality
- **User Context**: Test user identification

### Production Testing

1. **Deploy with Source Maps**: Ensure `SENTRY_AUTH_TOKEN` is set
2. **Monitor Real Errors**: Check dashboard for actual production errors
3. **Verify Performance Data**: Monitor transaction performance
4. **Test Error Recovery**: Verify error boundaries work correctly

## Deployment

### Development Builds

- Source maps are not uploaded
- All errors and performance data are captured
- Full debugging information available

### Production Builds

- Source maps are automatically uploaded to Sentry
- Sampling rates are applied (10% for performance, 10% for session replay)
- Error filtering is more aggressive
- Session replay is enabled for critical errors only

### Build Commands

```bash
# Development
npm run dev

# Production build
SENTRY_AUTH_TOKEN=your_token npm run build

# Preview production build
npm run preview
```

## Monitoring and Alerts

### Key Metrics to Monitor

1. **Error Rate**: Track application error frequency
2. **Performance**: Monitor page load and API response times
3. **User Impact**: Track how many users are affected by errors
4. **Alarm Processing**: Monitor alarm-specific operations
5. **Building Operations**: Track building management performance

### Recommended Alerts

Set up alerts in Sentry for:
- Error rate spikes
- Performance degradation
- Critical alarm processing errors
- Authentication failures
- Database connection issues

## Security Considerations

### Data Privacy

- **PII Filtering**: No personally identifiable information is sent to Sentry
- **Sensitive Data Masking**: Session replay masks all text and inputs
- **Building Data**: Only building IDs and types are included, not addresses
- **User Data**: Only user ID and email are tracked

### Access Control

- Sentry project access is restricted to authorized team members
- Auth tokens should be kept secure and rotated regularly
- Production source maps are uploaded securely

## Troubleshooting

### Common Issues

1. **Source Maps Not Uploading**
   - Verify `SENTRY_AUTH_TOKEN` is set
   - Check token permissions
   - Ensure production build

2. **Events Not Appearing**
   - Check DSN configuration
   - Verify network connectivity
   - Check error filtering rules

3. **Performance Data Missing**
   - Verify `tracesSampleRate` configuration
   - Check browser compatibility
   - Ensure transactions are properly finished

### Debug Commands

```javascript
// Check Sentry configuration
console.log(Sentry.getCurrentHub().getClient().getOptions())

// Test error capture
Sentry.captureException(new Error('Test error'))

// Check user context
console.log(Sentry.getCurrentHub().getScope().getUser())
```

## Support

For issues with the Sentry integration:

1. Check the Sentry dashboard for error details
2. Review the integration test results in dev tools
3. Consult the Sentry documentation: https://docs.sentry.io/
4. Check the application logs for Sentry-related messages

## Future Enhancements

Potential improvements to consider:

1. **Custom Dashboards**: Create alarm-specific dashboards
2. **Advanced Alerts**: Set up complex alerting rules
3. **Integration with Incident Management**: Connect to PagerDuty or similar
4. **Custom Metrics**: Track business-specific metrics
5. **A/B Testing**: Use Sentry for feature flag monitoring
