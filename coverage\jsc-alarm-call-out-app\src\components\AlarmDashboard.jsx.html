
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for jsc-alarm-call-out-app/src/components/AlarmDashboard.jsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">jsc-alarm-call-out-app/src/components</a> AlarmDashboard.jsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/453</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/453</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >import React, { useState } from 'react'</span></span></span>
<span class="cstat-no" title="statement not covered" >import { useAlarms } from '../hooks/useAlarms'</span>
<span class="cstat-no" title="statement not covered" >import { useAuth } from '../contexts/AuthContext'</span>
<span class="cstat-no" title="statement not covered" >import { useViewMode } from '../hooks/useViewMode'</span>
<span class="cstat-no" title="statement not covered" >import { formatAlarmNotification } from '../lib/alarmUtils'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >const AlarmDashboard = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const { user } = useAuth()</span>
<span class="cstat-no" title="statement not covered" >  const { viewMode, setViewMode } = useViewMode('alarms', 'card')</span>
<span class="cstat-no" title="statement not covered" >  const {</span>
<span class="cstat-no" title="statement not covered" >    alarms,</span>
<span class="cstat-no" title="statement not covered" >    loading,</span>
<span class="cstat-no" title="statement not covered" >    error,</span>
<span class="cstat-no" title="statement not covered" >    acknowledgeAlarm,</span>
<span class="cstat-no" title="statement not covered" >    resolveAlarm,</span>
<span class="cstat-no" title="statement not covered" >    getAlarmsByStatus,</span>
<span class="cstat-no" title="statement not covered" >    getAlarmsBySeverity,</span>
<span class="cstat-no" title="statement not covered" >    severityLevels</span>
<span class="cstat-no" title="statement not covered" >  } = useAlarms()</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const [selectedStatus, setSelectedStatus] = useState('all')</span>
<span class="cstat-no" title="statement not covered" >  const [selectedSeverity, setSelectedSeverity] = useState('all')</span>
<span class="cstat-no" title="statement not covered" >  const [processingAlarmId, setProcessingAlarmId] = useState(null)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Filter alarms based on selected criteria</span>
<span class="cstat-no" title="statement not covered" >  const filteredAlarms = alarms.filter(alarm =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const statusMatch = selectedStatus === 'all' || alarm.status === selectedStatus</span>
<span class="cstat-no" title="statement not covered" >    const severityMatch = selectedSeverity === 'all' || </span>
<span class="cstat-no" title="statement not covered" >      alarm.severity?.name?.toLowerCase() === selectedSeverity.toLowerCase()</span>
<span class="cstat-no" title="statement not covered" >    return statusMatch &amp;&amp; severityMatch</span>
<span class="cstat-no" title="statement not covered" >  })</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const handleAcknowledgeAlarm = async (alarmId) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!user) return</span>
<span class="cstat-no" title="statement not covered" >    </span>
<span class="cstat-no" title="statement not covered" >    setProcessingAlarmId(alarmId)</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      const result = await acknowledgeAlarm(alarmId, user.id, 'Acknowledged via dashboard')</span>
<span class="cstat-no" title="statement not covered" >      if (!result.success) {</span>
<span class="cstat-no" title="statement not covered" >        console.error('Failed to acknowledge alarm:', result.error)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    } finally {</span>
<span class="cstat-no" title="statement not covered" >      setProcessingAlarmId(null)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  const handleResolveAlarm = async (alarmId) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!user) return</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    setProcessingAlarmId(alarmId)</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      const result = await resolveAlarm(alarmId, user.id, 'Resolved via dashboard')</span>
<span class="cstat-no" title="statement not covered" >      if (!result.success) {</span>
<span class="cstat-no" title="statement not covered" >        console.error('Failed to resolve alarm:', result.error)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    } finally {</span>
<span class="cstat-no" title="statement not covered" >      setProcessingAlarmId(null)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  if (loading) {</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="flex items-center justify-center p-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p className="text-gray-600"&gt;Loading alarms...&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    )</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  if (error) {</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h3 className="text-red-800 font-medium"&gt;Error Loading Alarms&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-red-600 mt-1"&gt;{error}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    )</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Get alarm counts by status</span>
<span class="cstat-no" title="statement not covered" >  const statusCounts = {</span>
<span class="cstat-no" title="statement not covered" >    received: getAlarmsByStatus('received').length,</span>
<span class="cstat-no" title="statement not covered" >    acknowledged: getAlarmsByStatus('acknowledged').length,</span>
<span class="cstat-no" title="statement not covered" >    resolved: getAlarmsByStatus('resolved').length,</span>
<span class="cstat-no" title="statement not covered" >    total: alarms.length</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  // Get critical alarms count</span>
<span class="cstat-no" title="statement not covered" >  const criticalAlarms = getAlarmsBySeverity('CRITICAL').filter(alarm =&gt; </span>
<span class="cstat-no" title="statement not covered" >    alarm.status !== 'resolved'</span>
<span class="cstat-no" title="statement not covered" >  ).length</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="p-3 sm:p-4 max-w-full mx-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >      {/* Header */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h1 className="text-3xl font-bold text-gray-900 mb-2"&gt;Alarm Dashboard&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p className="text-gray-600"&gt;Monitor and manage building alarm notifications&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex flex-col sm:flex-row items-start sm:items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {/* View Toggle */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center bg-gray-100 rounded-lg p-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button</span>
<span class="cstat-no" title="statement not covered" >              onClick={() =&gt; setViewMode('card')}</span>
<span class="cstat-no" title="statement not covered" >              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${</span>
<span class="cstat-no" title="statement not covered" >                viewMode === 'card'</span>
<span class="cstat-no" title="statement not covered" >                  ? 'bg-white text-gray-900 shadow-sm'</span>
<span class="cstat-no" title="statement not covered" >                  : 'text-gray-600 hover:text-gray-900'</span>
<span class="cstat-no" title="statement not covered" >              }`}</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="hidden sm:inline"&gt;Cards&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            &lt;button</span>
<span class="cstat-no" title="statement not covered" >              onClick={() =&gt; setViewMode('table')}</span>
<span class="cstat-no" title="statement not covered" >              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${</span>
<span class="cstat-no" title="statement not covered" >                viewMode === 'table'</span>
<span class="cstat-no" title="statement not covered" >                  ? 'bg-white text-gray-900 shadow-sm'</span>
<span class="cstat-no" title="statement not covered" >                  : 'text-gray-600 hover:text-gray-900'</span>
<span class="cstat-no" title="statement not covered" >              }`}</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="hidden sm:inline"&gt;Table&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      {/* Status Overview Cards */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h3 className="text-sm font-medium text-gray-500"&gt;Total Alarms&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p className="text-2xl font-bold text-gray-900"&gt;{statusCounts.total}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white rounded-lg shadow p-4 border-l-4 border-red-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h3 className="text-sm font-medium text-gray-500"&gt;Critical Active&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p className="text-2xl font-bold text-red-600"&gt;{criticalAlarms}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h3 className="text-sm font-medium text-gray-500"&gt;Acknowledged&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p className="text-2xl font-bold text-yellow-600"&gt;{statusCounts.acknowledged}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        </span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="bg-white rounded-lg shadow p-4 border-l-4 border-green-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h3 className="text-sm font-medium text-gray-500"&gt;Resolved&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p className="text-2xl font-bold text-green-600"&gt;{statusCounts.resolved}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      {/* Filters */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="bg-white rounded-lg shadow p-4 mb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex flex-wrap gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;label className="block text-sm font-medium text-gray-700 mb-1"&gt;Status&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;select</span>
<span class="cstat-no" title="statement not covered" >              value={selectedStatus}</span>
<span class="cstat-no" title="statement not covered" >              onChange={(e) =&gt; setSelectedStatus(e.target.value)}</span>
<span class="cstat-no" title="statement not covered" >              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;option value="all"&gt;All Statuses&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;option value="received"&gt;Received&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;option value="acknowledged"&gt;Acknowledged&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;option value="resolved"&gt;Resolved&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/select&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          </span>
<span class="cstat-no" title="statement not covered" >          &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;label className="block text-sm font-medium text-gray-700 mb-1"&gt;Severity&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;select</span>
<span class="cstat-no" title="statement not covered" >              value={selectedSeverity}</span>
<span class="cstat-no" title="statement not covered" >              onChange={(e) =&gt; setSelectedSeverity(e.target.value)}</span>
<span class="cstat-no" title="statement not covered" >              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;option value="all"&gt;All Severities&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >              {severityLevels.map(level =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;option key={level.id} value={level.name}&gt;{level.name}&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >              ))}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/select&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      {/* Alarms List */}</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="bg-white rounded-lg shadow overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="px-4 py-3 border-b border-gray-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h2 className="text-lg font-medium text-gray-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >            Alarm Notifications ({filteredAlarms.length})</span>
<span class="cstat-no" title="statement not covered" >          &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        {filteredAlarms.length === 0 ? (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="p-8 text-center text-gray-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p&gt;No alarms found matching the selected criteria.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        ) : viewMode === 'card' ? (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="divide-y divide-gray-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {filteredAlarms.map(alarm =&gt; {</span>
<span class="cstat-no" title="statement not covered" >              const formatted = formatAlarmNotification(alarm)</span>
<span class="cstat-no" title="statement not covered" >              return (</span>
<span class="cstat-no" title="statement not covered" >                &lt;AlarmCard</span>
<span class="cstat-no" title="statement not covered" >                  key={alarm.id}</span>
<span class="cstat-no" title="statement not covered" >                  alarm={formatted}</span>
<span class="cstat-no" title="statement not covered" >                  onAcknowledge={() =&gt; handleAcknowledgeAlarm(alarm.id)}</span>
<span class="cstat-no" title="statement not covered" >                  onResolve={() =&gt; handleResolveAlarm(alarm.id)}</span>
<span class="cstat-no" title="statement not covered" >                  isProcessing={processingAlarmId === alarm.id}</span>
<span class="cstat-no" title="statement not covered" >                  canAcknowledge={user &amp;&amp; alarm.status === 'received'}</span>
<span class="cstat-no" title="statement not covered" >                  canResolve={user &amp;&amp; ['received', 'acknowledged'].includes(alarm.status)}</span>
<span class="cstat-no" title="statement not covered" >                /&gt;</span>
<span class="cstat-no" title="statement not covered" >              )</span>
<span class="cstat-no" title="statement not covered" >            })}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        ) : (</span>
<span class="cstat-no" title="statement not covered" >          &lt;AlarmTable</span>
<span class="cstat-no" title="statement not covered" >            alarms={filteredAlarms}</span>
<span class="cstat-no" title="statement not covered" >            onAcknowledge={handleAcknowledgeAlarm}</span>
<span class="cstat-no" title="statement not covered" >            onResolve={handleResolveAlarm}</span>
<span class="cstat-no" title="statement not covered" >            processingAlarmId={processingAlarmId}</span>
<span class="cstat-no" title="statement not covered" >            user={user}</span>
<span class="cstat-no" title="statement not covered" >          /&gt;</span>
<span class="cstat-no" title="statement not covered" >        )}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Alarm table component for compact view</span>
<span class="cstat-no" title="statement not covered" >const AlarmTable = ({ alarms, onAcknowledge, onResolve, processingAlarmId, user }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="overflow-x-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;table className="w-full table-auto divide-y divide-gray-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;thead className="bg-gray-50"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;tr&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Building</span>
<span class="cstat-no" title="statement not covered" >            &lt;/th&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Type</span>
<span class="cstat-no" title="statement not covered" >            &lt;/th&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Severity</span>
<span class="cstat-no" title="statement not covered" >            &lt;/th&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Time</span>
<span class="cstat-no" title="statement not covered" >            &lt;/th&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Status</span>
<span class="cstat-no" title="statement not covered" >            &lt;/th&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[25%]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Details</span>
<span class="cstat-no" title="statement not covered" >            &lt;/th&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[13%]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Actions</span>
<span class="cstat-no" title="statement not covered" >            &lt;/th&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/tr&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/thead&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;tbody className="bg-white divide-y divide-gray-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {alarms.map(alarm =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            const formatted = formatAlarmNotification(alarm)</span>
<span class="cstat-no" title="statement not covered" >            const isProcessing = processingAlarmId === alarm.id</span>
<span class="cstat-no" title="statement not covered" >            const canAcknowledge = user &amp;&amp; alarm.status === 'received'</span>
<span class="cstat-no" title="statement not covered" >            const canResolve = user &amp;&amp; ['received', 'acknowledged'].includes(alarm.status)</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            return (</span>
<span class="cstat-no" title="statement not covered" >              &lt;tr key={alarm.id} className="hover:bg-gray-50"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;td className="px-3 py-3 text-sm text-gray-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="font-medium truncate" title={alarm.building?.name || 'Unknown'}&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {alarm.building?.name || 'Unknown'}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {alarm.building?.building_code &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="text-gray-500 text-xs truncate" title={alarm.building?.building_code}&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {alarm.building?.building_code}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  )}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/td&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                &lt;td className="px-3 py-3 text-sm text-gray-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="truncate" title={alarm.alarm_type?.name || 'Unknown'}&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {alarm.alarm_type?.name || 'Unknown'}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/td&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                &lt;td className="px-3 py-3 text-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div</span>
<span class="cstat-no" title="statement not covered" >                      className="w-3 h-3 rounded-full mr-2 flex-shrink-0"</span>
<span class="cstat-no" title="statement not covered" >                      style={{ backgroundColor: alarm.severity?.color || '#6b7280' }}</span>
<span class="cstat-no" title="statement not covered" >                    &gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span className="font-medium truncate" title={alarm.severity?.name || 'Unknown'}&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {alarm.severity?.name || 'Unknown'}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/td&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                &lt;td className="px-3 py-3 text-sm text-gray-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="truncate" title={formatted.formattedTime}&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {formatted.formattedTime}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="text-gray-500 text-xs truncate" title={`Received: ${new Date(alarm.created_at).toLocaleTimeString()}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >                    Received: {new Date(alarm.created_at).toLocaleTimeString()}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/td&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                &lt;td className="px-3 py-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span</span>
<span class="cstat-no" title="statement not covered" >                    className="inline-flex px-2 py-1 text-xs font-semibold rounded-full whitespace-nowrap"</span>
<span class="cstat-no" title="statement not covered" >                    style={{</span>
<span class="cstat-no" title="statement not covered" >                      backgroundColor: formatted.statusColor + '20',</span>
<span class="cstat-no" title="statement not covered" >                      color: formatted.statusColor</span>
<span class="cstat-no" title="statement not covered" >                    }}</span>
<span class="cstat-no" title="statement not covered" >                  &gt;</span>
<span class="cstat-no" title="statement not covered" >                    {alarm.status.replace('_', ' ').toUpperCase()}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/td&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                &lt;td className="px-3 py-3 text-sm text-gray-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="line-clamp-2 cursor-help" title={alarm.alarm_details}&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {alarm.alarm_details || 'No details available'}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {alarm.location_details &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="text-gray-500 text-xs line-clamp-1 cursor-help mt-1" title={alarm.location_details}&gt;</span>
<span class="cstat-no" title="statement not covered" >                      📍 {alarm.location_details}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  )}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/td&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                &lt;td className="px-3 py-3 text-right text-sm font-medium"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex justify-end gap-1 flex-wrap"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {canAcknowledge &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                      &lt;button</span>
<span class="cstat-no" title="statement not covered" >                        onClick={() =&gt; onAcknowledge(alarm.id)}</span>
<span class="cstat-no" title="statement not covered" >                        disabled={isProcessing}</span>
<span class="cstat-no" title="statement not covered" >                        className="text-yellow-600 hover:text-yellow-900 disabled:opacity-50 text-xs px-2 py-1 border border-yellow-300 rounded hover:bg-yellow-50 whitespace-nowrap"</span>
<span class="cstat-no" title="statement not covered" >                      &gt;</span>
<span class="cstat-no" title="statement not covered" >                        {isProcessing ? '...' : 'Ack'}</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    )}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                    {canResolve &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                      &lt;button</span>
<span class="cstat-no" title="statement not covered" >                        onClick={() =&gt; onResolve(alarm.id)}</span>
<span class="cstat-no" title="statement not covered" >                        disabled={isProcessing}</span>
<span class="cstat-no" title="statement not covered" >                        className="text-green-600 hover:text-green-900 disabled:opacity-50 text-xs px-2 py-1 border border-green-300 rounded hover:bg-green-50 whitespace-nowrap"</span>
<span class="cstat-no" title="statement not covered" >                      &gt;</span>
<span class="cstat-no" title="statement not covered" >                        {isProcessing ? '...' : 'Resolve'}</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    )}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/td&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/tr&gt;</span>
<span class="cstat-no" title="statement not covered" >            )</span>
<span class="cstat-no" title="statement not covered" >          })}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/tbody&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/table&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Individual alarm card component</span>
<span class="cstat-no" title="statement not covered" >const AlarmCard = ({ alarm, onAcknowledge, onResolve, isProcessing, canAcknowledge, canResolve }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="p-4 hover:bg-gray-50"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="flex items-start justify-between gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex-1 min-w-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {/* Header */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center gap-3 mb-3 flex-wrap"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div</span>
<span class="cstat-no" title="statement not covered" >              className="w-3 h-3 rounded-full flex-shrink-0"</span>
<span class="cstat-no" title="statement not covered" >              style={{ backgroundColor: alarm.severityColor }}</span>
<span class="cstat-no" title="statement not covered" >            &gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h3 className="font-medium text-gray-900 flex-1 min-w-0 truncate"&gt;</span>
<span class="cstat-no" title="statement not covered" >              {alarm.subject || 'Alarm Notification'}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span</span>
<span class="cstat-no" title="statement not covered" >              className="px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap"</span>
<span class="cstat-no" title="statement not covered" >              style={{</span>
<span class="cstat-no" title="statement not covered" >                backgroundColor: alarm.statusColor + '20',</span>
<span class="cstat-no" title="statement not covered" >                color: alarm.statusColor</span>
<span class="cstat-no" title="statement not covered" >              }}</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              {alarm.status.replace('_', ' ').toUpperCase()}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {/* Details - Enhanced layout for wider screens */}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600 mb-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;&lt;strong&gt;Building:&lt;/strong&gt; &lt;span className="break-words"&gt;{alarm.building?.name || 'Unknown'}&lt;/span&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;&lt;strong&gt;Type:&lt;/strong&gt; &lt;span className="break-words"&gt;{alarm.alarm_type?.name || 'Unknown'}&lt;/span&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;&lt;strong&gt;Severity:&lt;/strong&gt; &lt;span className="break-words"&gt;{alarm.severity?.name || 'Unknown'}&lt;/span&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;&lt;strong&gt;Alarm Time:&lt;/strong&gt; &lt;span className="break-words"&gt;{alarm.formattedTime}&lt;/span&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;&lt;strong&gt;Received:&lt;/strong&gt; &lt;span className="break-words"&gt;{alarm.formattedCreatedAt}&lt;/span&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;&lt;strong&gt;From:&lt;/strong&gt; &lt;span className="break-words"&gt;{alarm.sender_email}&lt;/span&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            {alarm.location_details &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p&gt;&lt;strong&gt;Location:&lt;/strong&gt; &lt;span className="break-words"&gt;{alarm.location_details}&lt;/span&gt;&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {/* Alarm Details */}</span>
<span class="cstat-no" title="statement not covered" >          {alarm.alarm_details &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-gray-50 rounded p-3 mb-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p className="text-sm text-gray-700 break-words"&gt;{alarm.alarm_details}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        {/* Actions */}</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex flex-col gap-2 flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {canAcknowledge &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;button</span>
<span class="cstat-no" title="statement not covered" >              onClick={onAcknowledge}</span>
<span class="cstat-no" title="statement not covered" >              disabled={isProcessing}</span>
<span class="cstat-no" title="statement not covered" >              className="px-3 py-1 text-sm bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 disabled:opacity-50 whitespace-nowrap"</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              {isProcessing ? 'Processing...' : 'Acknowledge'}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          {canResolve &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;button</span>
<span class="cstat-no" title="statement not covered" >              onClick={onResolve}</span>
<span class="cstat-no" title="statement not covered" >              disabled={isProcessing}</span>
<span class="cstat-no" title="statement not covered" >              className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200 disabled:opacity-50 whitespace-nowrap"</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
<span class="cstat-no" title="statement not covered" >              {isProcessing ? 'Processing...' : 'Resolve'}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          )}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  )</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >export default AlarmDashboard</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-03T00:20:09.823Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    