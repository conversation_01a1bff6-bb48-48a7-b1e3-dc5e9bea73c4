
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for jsc-alarm-call-out-app/src/lib</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> jsc-alarm-call-out-app/src/lib</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.82% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>516/2741</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.66% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>76/114</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>17/34</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.82% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>516/2741</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="alarmUtils.js"><a href="alarmUtils.js.html">alarmUtils.js</a></td>
	<td data-value="76.47" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 76%"></div><div class="cover-empty" style="width: 24%"></div></div>
	</td>
	<td data-value="76.47" class="pct medium">76.47%</td>
	<td data-value="408" class="abs medium">312/408</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="52" class="abs medium">40/52</td>
	<td data-value="61.53" class="pct medium">61.53%</td>
	<td data-value="13" class="abs medium">8/13</td>
	<td data-value="76.47" class="pct medium">76.47%</td>
	<td data-value="408" class="abs medium">312/408</td>
	</tr>

<tr>
	<td class="file medium" data-value="buildingValidation.js"><a href="buildingValidation.js.html">buildingValidation.js</a></td>
	<td data-value="78.57" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="252" class="abs medium">198/252</td>
	<td data-value="67.3" class="pct medium">67.3%</td>
	<td data-value="52" class="abs medium">35/52</td>
	<td data-value="72.72" class="pct medium">72.72%</td>
	<td data-value="11" class="abs medium">8/11</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="252" class="abs medium">198/252</td>
	</tr>

<tr>
	<td class="file low" data-value="demoData.js"><a href="demoData.js.html">demoData.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="206" class="abs low">0/206</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="206" class="abs low">0/206</td>
	</tr>

<tr>
	<td class="file low" data-value="edgeFunctionClient.js"><a href="edgeFunctionClient.js.html">edgeFunctionClient.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="93" class="abs low">0/93</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="93" class="abs low">0/93</td>
	</tr>

<tr>
	<td class="file low" data-value="errorFilter.js"><a href="errorFilter.js.html">errorFilter.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="305" class="abs low">0/305</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="305" class="abs low">0/305</td>
	</tr>

<tr>
	<td class="file low" data-value="escalationService.js"><a href="escalationService.js.html">escalationService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="486" class="abs low">0/486</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="486" class="abs low">0/486</td>
	</tr>

<tr>
	<td class="file low" data-value="googleMapsLoader.js"><a href="googleMapsLoader.js.html">googleMapsLoader.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="155" class="abs low">0/155</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="155" class="abs low">0/155</td>
	</tr>

<tr>
	<td class="file low" data-value="retellAI.js"><a href="retellAI.js.html">retellAI.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="323" class="abs low">0/323</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="323" class="abs low">0/323</td>
	</tr>

<tr>
	<td class="file low" data-value="sentry.js"><a href="sentry.js.html">sentry.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="264" class="abs low">0/264</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="264" class="abs low">0/264</td>
	</tr>

<tr>
	<td class="file low" data-value="supabase.js"><a href="supabase.js.html">supabase.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	</tr>

<tr>
	<td class="file high" data-value="utils.js"><a href="utils.js.html">utils.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	</tr>

<tr>
	<td class="file low" data-value="webhookHandler.js"><a href="webhookHandler.js.html">webhookHandler.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="216" class="abs low">0/216</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="216" class="abs low">0/216</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-03T00:20:09.823Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    