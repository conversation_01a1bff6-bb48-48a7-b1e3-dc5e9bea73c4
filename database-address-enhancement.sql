-- Address Enhancement for JSC Alarm Call-Out App
-- Optional: Add geographic coordinates and structured address data

-- Add columns to buildings table for enhanced address data
ALTER TABLE buildings 
ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS formatted_address TEXT,
ADD COLUMN IF NOT EXISTS address_components JSONB,
ADD COLUMN IF NOT EXISTS place_id VARCHAR(255);

-- Create index for geographic queries (useful for proximity searches)
CREATE INDEX IF NOT EXISTS idx_buildings_location ON buildings USING GIST (
  POINT(longitude, latitude)
);

-- Create index for place_id lookups
CREATE INDEX IF NOT EXISTS idx_buildings_place_id ON buildings (place_id);

-- Add comments for documentation
COMMENT ON COLUMN buildings.latitude IS 'Latitude coordinate from Google Places API';
COMMENT ON COLUMN buildings.longitude IS 'Longitude coordinate from Google Places API';
COMMENT ON COLUMN buildings.formatted_address IS 'Formatted address from Google Places API';
COMMENT ON COLUMN buildings.address_components IS 'Structured address components from Google Places API';
COMMENT ON COLUMN buildings.place_id IS 'Google Places API place ID for future lookups';

-- Example of how address_components might be structured:
-- {
--   "street_number": "123",
--   "route": "Main Street",
--   "locality": "Anytown",
--   "administrative_area_level_1": "CA",
--   "country": "US",
--   "postal_code": "12345"
-- }

-- Function to calculate distance between two points (useful for emergency response)
-- Security: SET search_path = '' prevents search path manipulation attacks
CREATE OR REPLACE FUNCTION calculate_distance(
  lat1 DECIMAL(10,8),
  lon1 DECIMAL(11,8),
  lat2 DECIMAL(10,8),
  lon2 DECIMAL(11,8)
) RETURNS DECIMAL AS $$
BEGIN
  -- Haversine formula for calculating distance in kilometers
  RETURN (
    6371 * acos(
      cos(radians(lat1)) *
      cos(radians(lat2)) *
      cos(radians(lon2) - radians(lon1)) +
      sin(radians(lat1)) *
      sin(radians(lat2))
    )
  );
END;
$$ LANGUAGE plpgsql SET search_path = '';

-- Example query to find buildings within a certain radius of a point:
-- SELECT *, calculate_distance(latitude, longitude, 40.7128, -74.0060) as distance_km
-- FROM buildings 
-- WHERE latitude IS NOT NULL AND longitude IS NOT NULL
-- ORDER BY distance_km
-- LIMIT 10;
