-- =====================================================
-- ALARM DEDUPLICATION MIGRATION SCRIPT
-- =====================================================
-- This script adds alarm deduplication functionality to existing databases
-- Run this in your Supabase SQL Editor to add the building_alarm_id column
-- and unique constraint for preventing duplicate alarms

-- Step 1: Add the building_alarm_id column to alarm_notifications table
-- This column will store the unique alarm ID from the building's alarm system
ALTER TABLE alarm_notifications 
ADD COLUMN IF NOT EXISTS building_alarm_id VARCHAR(255);

-- Step 2: Create an index on the building_alarm_id column for performance
CREATE INDEX IF NOT EXISTS alarm_notifications_building_alarm_id_idx 
ON alarm_notifications(building_alarm_id);

-- Step 3: Populate existing records with fallback alarm IDs
-- For existing records without building_alarm_id, use message_id or generate a fallback
UPDATE alarm_notifications 
SET building_alarm_id = COALESCE(
  message_id, 
  'migrated-' || extract(epoch from created_at)::text || '-' || substr(md5(random()::text), 1, 8)
)
WHERE building_alarm_id IS NULL;

-- Step 4: Add the unique constraint to prevent duplicate alarms per building
-- This constraint ensures that the same building_alarm_id cannot be inserted twice for the same building
ALTER TABLE alarm_notifications 
ADD CONSTRAINT IF NOT EXISTS unique_building_alarm 
UNIQUE (building_id, building_alarm_id);

-- Step 5: Create a function to handle alarm deduplication during webhook processing
-- This function can be called from the Edge Function to handle upsert logic
CREATE OR REPLACE FUNCTION upsert_alarm_notification(
  p_building_id UUID,
  p_building_alarm_id VARCHAR(255),
  p_alarm_type_id INTEGER DEFAULT NULL,
  p_severity_id INTEGER DEFAULT NULL,
  p_subject TEXT DEFAULT NULL,
  p_sender_email VARCHAR(255) DEFAULT NULL,
  p_recipient_email VARCHAR(255) DEFAULT NULL,
  p_message_id VARCHAR(255) DEFAULT NULL,
  p_alarm_time TIMESTAMPTZ DEFAULT NULL,
  p_alarm_details TEXT DEFAULT NULL,
  p_location_details TEXT DEFAULT NULL,
  p_body_html TEXT DEFAULT NULL,
  p_body_plain TEXT DEFAULT NULL,
  p_webhook_signature VARCHAR(255) DEFAULT NULL,
  p_webhook_timestamp BIGINT DEFAULT NULL,
  p_webhook_token VARCHAR(255) DEFAULT NULL,
  p_raw_webhook_data JSONB DEFAULT NULL,
  p_status VARCHAR(50) DEFAULT 'received'
)
RETURNS TABLE (
  id UUID,
  is_new_alarm BOOLEAN,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  v_alarm_id UUID;
  v_is_new BOOLEAN;
  v_created_at TIMESTAMPTZ;
  v_updated_at TIMESTAMPTZ;
BEGIN
  -- Try to insert the new alarm
  INSERT INTO public.alarm_notifications (
    building_id,
    building_alarm_id,
    alarm_type_id,
    severity_id,
    subject,
    sender_email,
    recipient_email,
    message_id,
    alarm_time,
    alarm_details,
    location_details,
    body_html,
    body_plain,
    webhook_signature,
    webhook_timestamp,
    webhook_token,
    raw_webhook_data,
    status
  ) VALUES (
    p_building_id,
    p_building_alarm_id,
    p_alarm_type_id,
    p_severity_id,
    p_subject,
    p_sender_email,
    p_recipient_email,
    p_message_id,
    p_alarm_time,
    p_alarm_details,
    p_location_details,
    p_body_html,
    p_body_plain,
    p_webhook_signature,
    p_webhook_timestamp,
    p_webhook_token,
    p_raw_webhook_data,
    p_status
  )
  ON CONFLICT (building_id, building_alarm_id) 
  DO UPDATE SET
    -- Update webhook-related fields with new data
    webhook_signature = EXCLUDED.webhook_signature,
    webhook_timestamp = EXCLUDED.webhook_timestamp,
    webhook_token = EXCLUDED.webhook_token,
    raw_webhook_data = EXCLUDED.raw_webhook_data,
    updated_at = timezone('utc'::text, now()),
    -- Only update other fields if they were null or empty
    alarm_type_id = COALESCE(public.alarm_notifications.alarm_type_id, EXCLUDED.alarm_type_id),
    severity_id = COALESCE(public.alarm_notifications.severity_id, EXCLUDED.severity_id),
    subject = COALESCE(NULLIF(public.alarm_notifications.subject, ''), EXCLUDED.subject),
    alarm_time = COALESCE(public.alarm_notifications.alarm_time, EXCLUDED.alarm_time),
    alarm_details = COALESCE(NULLIF(public.alarm_notifications.alarm_details, ''), EXCLUDED.alarm_details),
    location_details = COALESCE(NULLIF(public.alarm_notifications.location_details, ''), EXCLUDED.location_details)
  RETURNING 
    public.alarm_notifications.id,
    public.alarm_notifications.created_at,
    public.alarm_notifications.updated_at
  INTO v_alarm_id, v_created_at, v_updated_at;

  -- Determine if this was a new alarm or an update
  v_is_new := (v_created_at = v_updated_at);

  -- Return the result
  RETURN QUERY SELECT v_alarm_id, v_is_new, v_created_at, v_updated_at;
END;
$$;

-- Step 6: Grant necessary permissions for the function
GRANT EXECUTE ON FUNCTION upsert_alarm_notification TO authenticated;
GRANT EXECUTE ON FUNCTION upsert_alarm_notification TO service_role;

-- Step 7: Add comments for documentation
COMMENT ON COLUMN alarm_notifications.building_alarm_id IS 'Unique alarm ID from the building alarm system, used for deduplication';
COMMENT ON CONSTRAINT unique_building_alarm ON alarm_notifications IS 'Prevents duplicate alarms from the same building system';
COMMENT ON FUNCTION upsert_alarm_notification IS 'Handles alarm insertion with deduplication logic for webhook processing';

-- Step 8: Verify the migration
-- Check that the column was added successfully
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'alarm_notifications' 
    AND column_name = 'building_alarm_id'
  ) THEN
    RAISE NOTICE 'SUCCESS: building_alarm_id column added to alarm_notifications table';
  ELSE
    RAISE EXCEPTION 'FAILED: building_alarm_id column was not added';
  END IF;

  IF EXISTS (
    SELECT 1 
    FROM information_schema.table_constraints 
    WHERE table_name = 'alarm_notifications' 
    AND constraint_name = 'unique_building_alarm'
  ) THEN
    RAISE NOTICE 'SUCCESS: unique_building_alarm constraint added';
  ELSE
    RAISE EXCEPTION 'FAILED: unique_building_alarm constraint was not added';
  END IF;

  RAISE NOTICE 'Migration completed successfully! Alarm deduplication is now enabled.';
END $$;
