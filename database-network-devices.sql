-- Network Device Management Database Setup
-- Add this to your Supabase SQL Editor to create the network devices table

-- =====================================================
-- NETWORK DEVICES TABLE
-- =====================================================

-- Create network devices table
CREATE TABLE IF NOT EXISTS network_devices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  
  -- Device identification
  station_name VARCHAR(255) NOT NULL,
  device_type VARCHAR(100) NOT NULL, -- 'BACnet router', 'switch', 'access point', 'server', 'workstation', 'JACE', etc.
  host_id VARCHAR(255) UNIQUE NOT NULL, -- Unique device identifier
  
  -- Network configuration
  ip_address INET NOT NULL,
  subnet_mask INET NOT NULL,
  gateway INET,
  internal_dns_server_1 INET,
  internal_dns_server_2 INET,
  
  -- Authentication credentials (encrypted)
  station_username VARCHAR(255),
  station_password_encrypted TEXT, -- Encrypted using Supabase vault
  windows_username VARCHAR(255),
  windows_password_encrypted TEXT, -- Encrypted using Supabase vault
  platform_username VARCHAR(255),
  platform_password_encrypted TEXT, -- Encrypted using Supabase vault
  passphrase_encrypted TEXT, -- Encrypted security passphrase/encryption key
  
  -- Device information
  software_version VARCHAR(255),
  notes TEXT,
  
  -- Status and metadata
  is_active BOOLEAN DEFAULT true,
  last_ping_time TIMESTAMP WITH TIME ZONE,
  last_ping_status VARCHAR(20) DEFAULT 'unknown', -- 'success', 'failed', 'timeout', 'unknown'
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT unique_building_host_id UNIQUE (building_id, host_id),
  CONSTRAINT unique_building_ip UNIQUE (building_id, ip_address)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary indexes
CREATE INDEX IF NOT EXISTS network_devices_building_id_idx ON network_devices(building_id);
CREATE INDEX IF NOT EXISTS network_devices_device_type_idx ON network_devices(device_type);
CREATE INDEX IF NOT EXISTS network_devices_ip_address_idx ON network_devices(ip_address);
CREATE INDEX IF NOT EXISTS network_devices_station_name_idx ON network_devices(station_name);
CREATE INDEX IF NOT EXISTS network_devices_host_id_idx ON network_devices(host_id);
CREATE INDEX IF NOT EXISTS network_devices_is_active_idx ON network_devices(is_active);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS network_devices_building_active_idx ON network_devices(building_id, is_active);
CREATE INDEX IF NOT EXISTS network_devices_building_type_idx ON network_devices(building_id, device_type);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE network_devices ENABLE ROW LEVEL SECURITY;

-- Policy for viewing network devices - users can view devices for buildings they have access to
CREATE POLICY "Authenticated users can view network devices" ON network_devices
  FOR SELECT USING (auth.role() = 'authenticated');

-- Policy for managing network devices - users can manage devices for buildings they have access to
CREATE POLICY "Authenticated users can manage network devices" ON network_devices
  FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Create trigger for updated_at column
CREATE TRIGGER update_network_devices_updated_at
  BEFORE UPDATE ON network_devices
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to validate IP address format
CREATE OR REPLACE FUNCTION validate_ip_address(ip_text TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Try to cast to INET type, return false if it fails
  BEGIN
    PERFORM ip_text::INET;
    RETURN TRUE;
  EXCEPTION WHEN OTHERS THEN
    RETURN FALSE;
  END;
END;
$$ LANGUAGE plpgsql;

-- Function to check if IP is in valid private range
CREATE OR REPLACE FUNCTION is_private_ip(ip_addr INET)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check for private IP ranges: 10.0.0.0/8, **********/12, ***********/16
  RETURN (
    ip_addr <<= '10.0.0.0/8'::INET OR
    ip_addr <<= '**********/12'::INET OR
    ip_addr <<= '***********/16'::INET OR
    ip_addr <<= '*********/8'::INET  -- localhost
  );
END;
$$ LANGUAGE plpgsql;

-- Function to encrypt passwords using Supabase vault
CREATE OR REPLACE FUNCTION encrypt_device_password(password_text TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Use Supabase's built-in encryption (requires vault extension)
  -- For now, we'll use a simple base64 encoding as placeholder
  -- In production, use proper encryption with vault.encrypt()
  RETURN encode(password_text::bytea, 'base64');
END;
$$ LANGUAGE plpgsql;

-- Function to decrypt passwords
CREATE OR REPLACE FUNCTION decrypt_device_password(encrypted_password TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Decrypt using Supabase vault (placeholder implementation)
  -- In production, use vault.decrypt()
  RETURN convert_from(decode(encrypted_password, 'base64'), 'UTF8');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SAMPLE DATA (Optional - for testing)
-- =====================================================

-- Insert sample network devices (uncomment to use)
/*
INSERT INTO network_devices (
  building_id,
  station_name,
  device_type,
  host_id,
  ip_address,
  subnet_mask,
  gateway,
  internal_dns_server_1,
  internal_dns_server_2,
  station_username,
  software_version,
  notes
) VALUES 
(
  (SELECT id FROM buildings LIMIT 1),
  'MAIN-ROUTER-01',
  'BACnet router',
  'RTR-001-MAIN',
  '***********',
  '*************',
  '***********',
  '*******',
  '*******',
  'admin',
  'v2.1.3',
  'Main building router - primary network gateway'
),
(
  (SELECT id FROM buildings LIMIT 1),
  'HVAC-CONTROLLER-01',
  'JACE',
  'JACE-001-HVAC',
  '***********00',
  '*************',
  '***********',
  '***********',
  '*******',
  'hvac_admin',
  'JACE-8000 v4.8',
  'Primary HVAC controller for zones 1-5'
);
*/
