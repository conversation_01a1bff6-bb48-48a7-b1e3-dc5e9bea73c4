-- Retell AI Integration Database Schema Updates
-- Run this SQL in your Supabase SQL Editor to add Retell AI support

-- =====================================================
-- ESCALATION CONTACTS TABLE
-- =====================================================

-- Create escalation contacts table for call-out sequences
CREATE TABLE IF NOT EXISTS escalation_contacts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  contact_name VARCHAR(255) NOT NULL,
  contact_phone VARCHAR(20) NOT NULL, -- E.164 format: +1234567890
  contact_email VARCHAR(255),
  contact_role VARCHAR(100), -- 'facility_manager', 'security', 'maintenance', 'emergency_contact'
  priority_level INTEGER NOT NULL DEFAULT 1, -- 1 = highest priority, higher numbers = lower priority
  is_active BOOLEAN DEFAULT true,
  available_hours JSONB DEFAULT '{"monday": {"start": "00:00", "end": "23:59"}, "tuesday": {"start": "00:00", "end": "23:59"}, "wednesday": {"start": "00:00", "end": "23:59"}, "thursday": {"start": "00:00", "end": "23:59"}, "friday": {"start": "00:00", "end": "23:59"}, "saturday": {"start": "00:00", "end": "23:59"}, "sunday": {"start": "00:00", "end": "23:59"}}', -- Available hours per day
  notification_preferences JSONB DEFAULT '{"voice_calls": true, "sms": false, "email": true}',
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(building_id, contact_phone, priority_level)
);

-- =====================================================
-- ENHANCED CALL_OUTS TABLE
-- =====================================================

-- Add Retell AI specific columns to existing call_outs table
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS retell_call_id VARCHAR(255); -- Retell AI call ID
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS contact_id UUID REFERENCES escalation_contacts(id); -- Which contact was called
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS call_type VARCHAR(50) DEFAULT 'voice'; -- 'voice', 'sms', 'email'
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS call_status VARCHAR(50) DEFAULT 'pending'; -- 'pending', 'calling', 'answered', 'no_answer', 'busy', 'failed', 'completed'
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS call_duration_seconds INTEGER; -- Call duration from Retell AI
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS call_transcript TEXT; -- Call transcript from Retell AI
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS call_recording_url TEXT; -- Recording URL from Retell AI
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0; -- Number of retry attempts
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS escalation_reason VARCHAR(255); -- Why escalation occurred
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS acknowledged_by_contact BOOLEAN DEFAULT false; -- Did contact acknowledge the alarm
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS contact_response TEXT; -- What the contact said/did
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS next_escalation_time TIMESTAMPTZ; -- When to escalate if no response
ALTER TABLE call_outs ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'; -- Additional Retell AI metadata

-- =====================================================
-- CALL_OUT_ATTEMPTS TABLE
-- =====================================================

-- Track individual call attempts for detailed logging
CREATE TABLE IF NOT EXISTS call_out_attempts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  call_out_id UUID NOT NULL REFERENCES call_outs(id) ON DELETE CASCADE,
  contact_id UUID NOT NULL REFERENCES escalation_contacts(id),
  retell_call_id VARCHAR(255), -- Retell AI call ID for this attempt
  attempt_number INTEGER NOT NULL, -- 1, 2, 3, etc.
  call_status VARCHAR(50) NOT NULL, -- 'calling', 'answered', 'no_answer', 'busy', 'failed'
  call_start_time TIMESTAMPTZ,
  call_end_time TIMESTAMPTZ,
  call_duration_seconds INTEGER,
  disconnection_reason VARCHAR(100), -- From Retell AI: 'user_hangup', 'agent_hangup', 'dial_failed', etc.
  call_transcript TEXT,
  call_recording_url TEXT,
  call_analysis JSONB, -- Retell AI call analysis data
  acknowledged BOOLEAN DEFAULT false,
  contact_response TEXT,
  error_message TEXT, -- If call failed
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Escalation contacts indexes
CREATE INDEX IF NOT EXISTS escalation_contacts_building_id_idx ON escalation_contacts(building_id);
CREATE INDEX IF NOT EXISTS escalation_contacts_priority_idx ON escalation_contacts(building_id, priority_level);
CREATE INDEX IF NOT EXISTS escalation_contacts_active_idx ON escalation_contacts(is_active);
CREATE INDEX IF NOT EXISTS escalation_contacts_phone_idx ON escalation_contacts(contact_phone);

-- Enhanced call_outs indexes
CREATE INDEX IF NOT EXISTS call_outs_retell_call_id_idx ON call_outs(retell_call_id);
CREATE INDEX IF NOT EXISTS call_outs_contact_id_idx ON call_outs(contact_id);
CREATE INDEX IF NOT EXISTS call_outs_call_status_idx ON call_outs(call_status);
CREATE INDEX IF NOT EXISTS call_outs_escalation_time_idx ON call_outs(next_escalation_time);

-- Call out attempts indexes
CREATE INDEX IF NOT EXISTS call_out_attempts_call_out_id_idx ON call_out_attempts(call_out_id);
CREATE INDEX IF NOT EXISTS call_out_attempts_contact_id_idx ON call_out_attempts(contact_id);
CREATE INDEX IF NOT EXISTS call_out_attempts_retell_call_id_idx ON call_out_attempts(retell_call_id);
CREATE INDEX IF NOT EXISTS call_out_attempts_status_idx ON call_out_attempts(call_status);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS for new tables
ALTER TABLE escalation_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_out_attempts ENABLE ROW LEVEL SECURITY;

-- Escalation contacts policies
CREATE POLICY "Authenticated users can view escalation contacts" ON escalation_contacts
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage escalation contacts" ON escalation_contacts
  FOR ALL USING (auth.role() = 'authenticated');

-- Call out attempts policies
CREATE POLICY "Authenticated users can view call out attempts" ON call_out_attempts
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage call out attempts" ON call_out_attempts
  FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Create triggers for updated_at columns
CREATE TRIGGER update_escalation_contacts_updated_at
  BEFORE UPDATE ON escalation_contacts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE ESCALATION CONTACTS
-- =====================================================

-- Insert sample escalation contacts for existing buildings
INSERT INTO escalation_contacts (building_id, contact_name, contact_phone, contact_email, contact_role, priority_level) 
SELECT 
  b.id,
  'Primary Facility Manager',
  '+15551234567', -- Replace with actual phone numbers
  '<EMAIL>',
  'facility_manager',
  1
FROM buildings b
WHERE b.building_code = 'MOB-001'
ON CONFLICT (building_id, contact_phone, priority_level) DO NOTHING;

INSERT INTO escalation_contacts (building_id, contact_name, contact_phone, contact_email, contact_role, priority_level) 
SELECT 
  b.id,
  'Security Supervisor',
  '+15551234568', -- Replace with actual phone numbers
  '<EMAIL>',
  'security',
  2
FROM buildings b
WHERE b.building_code = 'MOB-001'
ON CONFLICT (building_id, contact_phone, priority_level) DO NOTHING;

INSERT INTO escalation_contacts (building_id, contact_name, contact_phone, contact_email, contact_role, priority_level) 
SELECT 
  b.id,
  'Emergency Contact',
  '+15551234569', -- Replace with actual phone numbers
  '<EMAIL>',
  'emergency_contact',
  3
FROM buildings b
WHERE b.building_code = 'MOB-001'
ON CONFLICT (building_id, contact_phone, priority_level) DO NOTHING;
