-- PostgreSQL Security Fixes for JSC Alarm Call-Out App
-- This script fixes critical search_path vulnerabilities in database functions
-- 
-- SECURITY ISSUE: Functions without SET search_path = '' are vulnerable to 
-- search path manipulation attacks where malicious users could potentially 
-- manipulate function behavior by altering the search path.
--
-- FIXES APPLIED:
-- 1. update_updated_at_column() - Added SET search_path = ''
-- 2. create_user_profile() - Added SET search_path = '' (CRITICAL: has SECURITY DEFINER)
-- 3. calculate_distance() - Added SET search_path = ''
-- 4. create_default_user_settings() - Added SET search_path = '' (CRITICAL: has SECURITY DEFINER)

-- =====================================================
-- FIX 1: update_updated_at_column() Function
-- =====================================================

-- Create an updated_at trigger function with secure search_path
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql' SET search_path = '';

-- =====================================================
-- FIX 2: create_user_profile() Function (CRITICAL)
-- =====================================================

-- Function to create user profile when user signs up
-- CRITICAL: This function has SECURITY DEFINER privileges
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, email, display_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email)
  );
  RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER SET search_path = '';

-- =====================================================
-- FIX 3: calculate_distance() Function
-- =====================================================

-- Function to calculate distance between two points (useful for emergency response)
CREATE OR REPLACE FUNCTION calculate_distance(
  lat1 DECIMAL(10,8), 
  lon1 DECIMAL(11,8), 
  lat2 DECIMAL(10,8), 
  lon2 DECIMAL(11,8)
) RETURNS DECIMAL AS $$
BEGIN
  -- Haversine formula for calculating distance in kilometers
  RETURN (
    6371 * acos(
      cos(radians(lat1)) * 
      cos(radians(lat2)) * 
      cos(radians(lon2) - radians(lon1)) + 
      sin(radians(lat1)) * 
      sin(radians(lat2))
    )
  );
END;
$$ LANGUAGE plpgsql SET search_path = '';

-- =====================================================
-- FIX 4: create_default_user_settings() Function (CRITICAL)
-- =====================================================

-- Function to create default user settings when user profile is created
-- CRITICAL: This function has SECURITY DEFINER privileges
CREATE OR REPLACE FUNCTION public.create_default_user_settings()
RETURNS TRIGGER AS $$
BEGIN
  -- Create default user preferences
  INSERT INTO public.user_preferences (
    user_id,
    default_view,
    theme,
    timezone,
    date_format,
    time_format,
    temperature_unit,
    measurement_unit,
    auto_refresh_interval,
    show_tooltips,
    compact_mode
  ) VALUES (
    NEW.user_id,
    'bms',
    'light',
    'UTC',
    'MM/DD/YYYY',
    '12h',
    'fahrenheit',
    'imperial',
    30,
    true,
    false
  ) ON CONFLICT (user_id) DO NOTHING;

  -- Create default notification preferences
  INSERT INTO public.notification_preferences (
    user_id,
    email_enabled,
    email_critical,
    email_high,
    email_medium,
    email_low,
    email_digest,
    email_digest_frequency,
    in_app_enabled,
    in_app_sound,
    in_app_desktop,
    sms_enabled,
    sms_critical,
    sms_high,
    quiet_hours_enabled,
    quiet_hours_start,
    quiet_hours_end,
    weekend_notifications
  ) VALUES (
    NEW.user_id,
    true,
    true,
    true,
    false,
    false,
    true,
    'daily',
    true,
    true,
    true,
    false,
    false,
    false,
    false,
    '22:00',
    '06:00',
    true
  ) ON CONFLICT (user_id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = '';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify that all functions now have secure search_path settings
-- Run these queries to confirm the fixes were applied correctly:

-- Check update_updated_at_column function
SELECT 
  proname as function_name,
  proconfig as search_path_setting
FROM pg_proc 
WHERE proname = 'update_updated_at_column';

-- Check create_user_profile function  
SELECT 
  proname as function_name,
  proconfig as search_path_setting,
  prosecdef as security_definer
FROM pg_proc 
WHERE proname = 'create_user_profile';

-- Check calculate_distance function
SELECT
  proname as function_name,
  proconfig as search_path_setting
FROM pg_proc
WHERE proname = 'calculate_distance';

-- Check create_default_user_settings function
SELECT
  proname as function_name,
  proconfig as search_path_setting,
  prosecdef as security_definer
FROM pg_proc
WHERE proname = 'create_default_user_settings';

-- Expected results should show search_path_setting as: {search_path=''}
-- For create_user_profile and create_default_user_settings, security_definer should be 't' (true)

-- =====================================================
-- SECURITY VALIDATION
-- =====================================================

-- Test that the functions still work correctly after security fixes
-- These are safe test queries that don't modify data:

-- Test 1: Verify update_updated_at_column trigger still works
-- (This will be tested automatically when any record is updated)

-- Test 2: Verify calculate_distance function works
SELECT calculate_distance(40.7128, -74.0060, 34.0522, -118.2437) as distance_ny_to_la_km;
-- Expected result: approximately 3944 km (distance from NYC to LA)

-- Test 3: Verify create_user_profile function is properly secured
-- (This will be tested when new users sign up)

-- =====================================================
-- DEPLOYMENT NOTES
-- =====================================================

-- 1. These fixes address critical security vulnerabilities
-- 2. All functions maintain their original functionality
-- 3. The search_path = '' setting prevents search path manipulation attacks
-- 4. Schema-qualified names (like public.user_profiles) are used where needed
-- 5. All existing triggers and references continue to work unchanged

-- DEPLOYMENT STATUS: Ready for production deployment
-- RISK LEVEL: Low (maintains all existing functionality)
-- SECURITY IMPACT: High (fixes critical vulnerabilities)
