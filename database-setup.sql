-- JSC Alarm Call-Out App Database Setup
-- Run this SQL in your Supabase SQL Editor to set up the required tables

-- =====================================================
-- ALARM SYSTEM TABLES
-- =====================================================

-- Create severity levels reference table
CREATE TABLE IF NOT EXISTS severity_levels (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL,
  level INTEGER UNIQUE NOT NULL,
  color VARCHAR(7) DEFAULT '#000000',
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Insert default severity levels
INSERT INTO severity_levels (name, level, color, description) VALUES
  ('LOW', 1, '#22c55e', 'Low priority alarm'),
  ('MEDIUM', 2, '#f59e0b', 'Medium priority alarm'),
  ('HIGH', 3, '#ef4444', 'High priority alarm'),
  ('CRITICAL', 4, '#dc2626', 'Critical priority alarm requiring immediate attention')
ON CONFLICT (name) DO NOTHING;

-- Create alarm types reference table
CREATE TABLE IF NOT EXISTS alarm_types (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  category VARCHAR(50),
  description TEXT,
  default_severity_id INTEGER REFERENCES severity_levels(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Insert default alarm types
INSERT INTO alarm_types (name, category, description, default_severity_id) VALUES
  ('Fire Detection System', 'Fire Safety', 'Smoke detectors and fire alarm systems', 4),
  ('Security Breach', 'Security', 'Unauthorized access or intrusion detection', 3),
  ('HVAC System', 'Building Systems', 'Heating, ventilation, and air conditioning alerts', 2),
  ('Power System', 'Electrical', 'Power outages and electrical system issues', 3),
  ('Water System', 'Plumbing', 'Water leaks and plumbing system alerts', 2),
  ('Elevator System', 'Transportation', 'Elevator malfunctions and service alerts', 2)
ON CONFLICT (name) DO NOTHING;

-- Create buildings table
CREATE TABLE IF NOT EXISTS buildings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  address TEXT,
  email_address VARCHAR(255) UNIQUE NOT NULL,
  building_code VARCHAR(50),
  contact_phone VARCHAR(20),
  contact_email VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create alarm notifications table
CREATE TABLE IF NOT EXISTS alarm_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID REFERENCES buildings(id) ON DELETE CASCADE,
  alarm_type_id INTEGER REFERENCES alarm_types(id),
  severity_id INTEGER REFERENCES severity_levels(id),

  -- Building system alarm identification (for deduplication)
  building_alarm_id VARCHAR(255), -- Unique alarm ID from the building's alarm system

  -- Email metadata
  subject TEXT,
  sender_email VARCHAR(255),
  recipient_email VARCHAR(255),
  message_id VARCHAR(255),

  -- Alarm details
  alarm_time TIMESTAMP WITH TIME ZONE,
  alarm_details TEXT,
  location_details TEXT,

  -- Email content
  body_html TEXT,
  body_plain TEXT,

  -- Webhook data
  webhook_signature VARCHAR(255),
  webhook_timestamp BIGINT,
  webhook_token VARCHAR(255),
  raw_webhook_data JSONB,

  -- Processing status
  status VARCHAR(50) DEFAULT 'received',
  acknowledged_by UUID REFERENCES auth.users(id),
  acknowledged_at TIMESTAMP WITH TIME ZONE,
  resolved_by UUID REFERENCES auth.users(id),
  resolved_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

  -- Deduplication constraint: prevent duplicate alarms per building
  CONSTRAINT unique_building_alarm UNIQUE (building_id, building_alarm_id)
);



-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS for all alarm tables
ALTER TABLE buildings ENABLE ROW LEVEL SECURITY;
ALTER TABLE alarm_notifications ENABLE ROW LEVEL SECURITY;

-- Buildings policies - authenticated users can view all buildings
CREATE POLICY "Authenticated users can view buildings" ON buildings
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage buildings" ON buildings
  FOR ALL USING (auth.role() = 'authenticated');

-- Alarm notifications policies - authenticated users can view and manage all alarms
CREATE POLICY "Authenticated users can view alarm notifications" ON alarm_notifications
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage alarm notifications" ON alarm_notifications
  FOR ALL USING (auth.role() = 'authenticated');

-- Severity levels and alarm types are public reference data
ALTER TABLE severity_levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE alarm_types ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view severity levels" ON severity_levels
  FOR SELECT USING (true);

CREATE POLICY "Anyone can view alarm types" ON alarm_types
  FOR SELECT USING (true);



-- =====================================================
-- TRIGGERS AND FUNCTIONS
-- =====================================================

-- Create an updated_at trigger function
-- Security: SET search_path = '' prevents search path manipulation attacks
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql' SET search_path = '';

-- Create triggers for updated_at columns
CREATE TRIGGER update_buildings_updated_at
  BEFORE UPDATE ON buildings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_notifications_updated_at
  BEFORE UPDATE ON alarm_notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();



-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Buildings indexes
CREATE INDEX IF NOT EXISTS buildings_email_address_idx ON buildings(email_address);
CREATE INDEX IF NOT EXISTS buildings_is_active_idx ON buildings(is_active);

-- Alarm notifications indexes
CREATE INDEX IF NOT EXISTS alarm_notifications_building_id_idx ON alarm_notifications(building_id);
CREATE INDEX IF NOT EXISTS alarm_notifications_alarm_type_id_idx ON alarm_notifications(alarm_type_id);
CREATE INDEX IF NOT EXISTS alarm_notifications_severity_id_idx ON alarm_notifications(severity_id);
CREATE INDEX IF NOT EXISTS alarm_notifications_status_idx ON alarm_notifications(status);
CREATE INDEX IF NOT EXISTS alarm_notifications_alarm_time_idx ON alarm_notifications(alarm_time);
CREATE INDEX IF NOT EXISTS alarm_notifications_created_at_idx ON alarm_notifications(created_at);
CREATE INDEX IF NOT EXISTS alarm_notifications_recipient_email_idx ON alarm_notifications(recipient_email);
CREATE INDEX IF NOT EXISTS alarm_notifications_building_alarm_id_idx ON alarm_notifications(building_alarm_id);



-- =====================================================
-- COMPREHENSIVE BMS SCHEMA EXTENSIONS
-- =====================================================

-- Equipment hierarchy and asset management
CREATE TABLE IF NOT EXISTS equipment (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  parent_equipment_id UUID REFERENCES equipment(id),
  equipment_type VARCHAR(100) NOT NULL, -- 'HVAC', 'Electrical', 'Security', 'Fire Safety', 'Lighting', 'Water'
  category VARCHAR(100), -- 'Chiller', 'Boiler', 'AHU', 'VAV', 'Camera', 'Access Control', etc.
  manufacturer VARCHAR(255),
  model VARCHAR(255),
  serial_number VARCHAR(255),
  installation_date DATE,
  warranty_expiry DATE,
  location JSONB, -- {floor: 1, room: "101", coordinates: {x: 10, y: 20}, zone: "North Wing"}
  specifications JSONB, -- Technical specs, capacity, ratings, etc.
  maintenance_schedule JSONB, -- Scheduled maintenance intervals and procedures
  status VARCHAR(50) DEFAULT 'active', -- 'active', 'maintenance', 'offline', 'decommissioned'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Time-series sensor data (optimized for high-volume data)
CREATE TABLE IF NOT EXISTS sensor_readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  device_id VARCHAR(255) NOT NULL, -- Equipment ID or sensor identifier
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  timestamp TIMESTAMPTZ NOT NULL,
  metric_type VARCHAR(100) NOT NULL, -- 'temperature', 'humidity', 'pressure', 'flow_rate', 'power_consumption', etc.
  value NUMERIC NOT NULL,
  unit VARCHAR(20), -- 'celsius', 'fahrenheit', 'percent', 'psi', 'kw', 'cfm', etc.
  quality_code VARCHAR(10) DEFAULT 'good', -- 'good', 'uncertain', 'bad'
  location JSONB, -- Optional location override
  metadata JSONB, -- Additional context data
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Energy management and monitoring
CREATE TABLE IF NOT EXISTS energy_data (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  equipment_id UUID REFERENCES equipment(id),
  timestamp TIMESTAMPTZ NOT NULL,
  energy_type VARCHAR(50) NOT NULL, -- 'electricity', 'gas', 'water', 'steam', 'chilled_water'
  consumption_value NUMERIC NOT NULL,
  consumption_unit VARCHAR(20) NOT NULL, -- 'kwh', 'therms', 'gallons', 'btu'
  demand_value NUMERIC, -- Peak demand
  demand_unit VARCHAR(20), -- 'kw', 'gpm', etc.
  cost NUMERIC, -- Cost in local currency
  rate_schedule VARCHAR(100), -- Time-of-use rate schedule
  carbon_factor NUMERIC, -- CO2 emissions factor
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Occupancy and space management
CREATE TABLE IF NOT EXISTS occupancy_data (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  zone_id VARCHAR(100), -- Floor, room, or zone identifier
  timestamp TIMESTAMPTZ NOT NULL,
  occupancy_count INTEGER NOT NULL,
  capacity INTEGER, -- Maximum occupancy for the space
  detection_method VARCHAR(50), -- 'people_counter', 'co2_inference', 'wifi_tracking', 'manual'
  confidence_level NUMERIC, -- 0.0 to 1.0
  location JSONB, -- Detailed location information
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Work orders and maintenance management
CREATE TABLE IF NOT EXISTS work_orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  equipment_id UUID REFERENCES equipment(id),
  alarm_id UUID REFERENCES alarm_notifications(id), -- Link to triggering alarm if applicable
  title VARCHAR(255) NOT NULL,
  description TEXT,
  priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
  status VARCHAR(50) DEFAULT 'open', -- 'open', 'assigned', 'in_progress', 'completed', 'cancelled'
  work_type VARCHAR(50), -- 'preventive', 'corrective', 'emergency', 'inspection'
  assigned_to UUID REFERENCES auth.users(id),
  requested_by UUID REFERENCES auth.users(id),
  estimated_hours NUMERIC,
  actual_hours NUMERIC,
  estimated_cost NUMERIC,
  actual_cost NUMERIC,
  scheduled_date TIMESTAMPTZ,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  location JSONB,
  parts_required JSONB, -- Array of parts/materials needed
  procedures JSONB, -- Step-by-step procedures
  safety_requirements JSONB, -- Safety protocols and requirements
  notes TEXT,
  attachments JSONB, -- File references for photos, documents
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User profiles table for storing user display information
-- This table is queryable via PostgREST and stores safe user data
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255),
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  display_name VARCHAR(200),
  phone VARCHAR(20),
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);

-- User roles and permissions for BMS
CREATE TABLE IF NOT EXISTS user_roles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  building_id UUID REFERENCES buildings(id) ON DELETE CASCADE, -- NULL for system-wide roles
  role_name VARCHAR(50) NOT NULL, -- 'facility_manager', 'technician', 'security', 'tenant', 'admin'
  permissions JSONB, -- Detailed permissions object
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, building_id, role_name)
);

-- Call-out tracking (enhanced from existing alarm system)
CREATE TABLE IF NOT EXISTS call_outs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  alarm_id UUID REFERENCES alarm_notifications(id),
  work_order_id UUID REFERENCES work_orders(id),
  technician_id UUID REFERENCES auth.users(id),
  call_out_time TIMESTAMPTZ NOT NULL,
  response_time TIMESTAMPTZ,
  arrival_time TIMESTAMPTZ,
  completion_time TIMESTAMPTZ,
  status VARCHAR(50) DEFAULT 'dispatched', -- 'dispatched', 'en_route', 'on_site', 'completed', 'cancelled'
  escalation_level INTEGER DEFAULT 1,
  travel_time_minutes INTEGER,
  on_site_time_minutes INTEGER,
  mileage NUMERIC,
  notes TEXT,
  resolution_summary TEXT,
  follow_up_required BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ENHANCED INDEXES FOR PERFORMANCE
-- =====================================================

-- Equipment indexes
CREATE INDEX IF NOT EXISTS equipment_building_id_idx ON equipment(building_id);
CREATE INDEX IF NOT EXISTS equipment_type_idx ON equipment(equipment_type);
CREATE INDEX IF NOT EXISTS equipment_status_idx ON equipment(status);
CREATE INDEX IF NOT EXISTS equipment_parent_id_idx ON equipment(parent_equipment_id);

-- Sensor readings indexes (time-series optimized)
CREATE INDEX IF NOT EXISTS sensor_readings_device_timestamp_idx ON sensor_readings(device_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS sensor_readings_building_timestamp_idx ON sensor_readings(building_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS sensor_readings_metric_type_idx ON sensor_readings(metric_type);
CREATE INDEX IF NOT EXISTS sensor_readings_timestamp_idx ON sensor_readings(timestamp DESC);

-- Energy data indexes
CREATE INDEX IF NOT EXISTS energy_data_building_timestamp_idx ON energy_data(building_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS energy_data_equipment_timestamp_idx ON energy_data(equipment_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS energy_data_energy_type_idx ON energy_data(energy_type);

-- Occupancy data indexes
CREATE INDEX IF NOT EXISTS occupancy_data_building_timestamp_idx ON occupancy_data(building_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS occupancy_data_zone_timestamp_idx ON occupancy_data(zone_id, timestamp DESC);

-- Work orders indexes
CREATE INDEX IF NOT EXISTS work_orders_building_id_idx ON work_orders(building_id);
CREATE INDEX IF NOT EXISTS work_orders_equipment_id_idx ON work_orders(equipment_id);
CREATE INDEX IF NOT EXISTS work_orders_status_idx ON work_orders(status);
CREATE INDEX IF NOT EXISTS work_orders_assigned_to_idx ON work_orders(assigned_to);
CREATE INDEX IF NOT EXISTS work_orders_priority_idx ON work_orders(priority);

-- User profiles indexes
CREATE INDEX IF NOT EXISTS user_profiles_user_id_idx ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS user_profiles_email_idx ON user_profiles(email);

-- User roles indexes
CREATE INDEX IF NOT EXISTS user_roles_user_id_idx ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS user_roles_building_id_idx ON user_roles(building_id);
CREATE INDEX IF NOT EXISTS user_roles_role_name_idx ON user_roles(role_name);

-- Call-outs indexes
CREATE INDEX IF NOT EXISTS call_outs_alarm_id_idx ON call_outs(alarm_id);
CREATE INDEX IF NOT EXISTS call_outs_technician_id_idx ON call_outs(technician_id);
CREATE INDEX IF NOT EXISTS call_outs_status_idx ON call_outs(status);

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- =====================================================
-- RLS POLICIES FOR NEW TABLES
-- =====================================================

-- Enable RLS for new tables
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE sensor_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE energy_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE occupancy_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE work_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_outs ENABLE ROW LEVEL SECURITY;

-- Equipment policies
CREATE POLICY "Authenticated users can view equipment" ON equipment
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage equipment" ON equipment
  FOR ALL USING (auth.role() = 'authenticated');

-- Sensor readings policies
CREATE POLICY "Authenticated users can view sensor readings" ON sensor_readings
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert sensor readings" ON sensor_readings
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Energy data policies
CREATE POLICY "Authenticated users can view energy data" ON energy_data
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert energy data" ON energy_data
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Occupancy data policies
CREATE POLICY "Authenticated users can view occupancy data" ON occupancy_data
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert occupancy data" ON occupancy_data
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Work orders policies
CREATE POLICY "Authenticated users can view work orders" ON work_orders
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage work orders" ON work_orders
  FOR ALL USING (auth.role() = 'authenticated');

-- User profiles policies
CREATE POLICY "Users can view their own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can view all profiles" ON user_profiles
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage profiles" ON user_profiles
  FOR ALL USING (auth.role() = 'authenticated');

-- User roles policies
CREATE POLICY "Users can view their own roles" ON user_roles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can manage user roles" ON user_roles
  FOR ALL USING (auth.role() = 'authenticated');

-- Call-outs policies
CREATE POLICY "Authenticated users can view call outs" ON call_outs
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage call outs" ON call_outs
  FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- TRIGGERS FOR NEW TABLES
-- =====================================================

-- Create triggers for updated_at columns
CREATE TRIGGER update_equipment_updated_at
  BEFORE UPDATE ON equipment
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_work_orders_updated_at
  BEFORE UPDATE ON work_orders
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_roles_updated_at
  BEFORE UPDATE ON user_roles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_call_outs_updated_at
  BEFORE UPDATE ON call_outs
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to create user profile when user signs up
-- Security: SET search_path = '' prevents search path manipulation attacks
-- CRITICAL: This function has SECURITY DEFINER privileges
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (user_id, email, display_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email)
  );
  RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER SET search_path = '';

-- Trigger to automatically create user profile on signup
CREATE TRIGGER create_user_profile_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_user_profile();

-- Insert sample buildings
INSERT INTO buildings (name, address, email_address, building_code, contact_phone, contact_email) VALUES
  ('Main Office Building', '123 Business Ave, City, State 12345', '<EMAIL>', 'MOB-001', '555-0123', '<EMAIL>'),
  ('Warehouse A', '456 Industrial Blvd, City, State 12345', '<EMAIL>', 'WHA-001', '555-0124', '<EMAIL>'),
  ('Data Center', '789 Tech Park Dr, City, State 12345', '<EMAIL>', 'DC-001', '555-0125', '<EMAIL>')
ON CONFLICT (email_address) DO NOTHING;
