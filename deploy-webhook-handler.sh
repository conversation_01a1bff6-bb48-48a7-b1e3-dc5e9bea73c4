#!/bin/bash

# Deploy Retell AI Webhook Handler to Supabase
# This script deploys the enhanced webhook handler with custom tool support

echo "🚀 Deploying Retell AI Webhook Handler to Supabase..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    echo "   or visit: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

# Check if the function exists
if [ ! -f "supabase/functions/retell-webhook-handler/index.ts" ]; then
    echo "❌ Webhook handler function not found at supabase/functions/retell-webhook-handler/index.ts"
    exit 1
fi

echo "📋 Function details:"
echo "   Name: retell-webhook-handler"
echo "   Path: supabase/functions/retell-webhook-handler/index.ts"
echo "   Features: Call lifecycle events, custom tools, enhanced analysis"

# Deploy the function
echo "🔄 Deploying function..."
supabase functions deploy retell-webhook-handler --project-ref pdnclfznadtbuxeoszjx

if [ $? -eq 0 ]; then
    echo "✅ Webhook handler deployed successfully!"
    echo ""
    echo "📝 Next steps:"
    echo "1. Set environment variables in Supabase dashboard:"
    echo "   - RETELL_AI_WEBHOOK_SECRET"
    echo "   - SUPABASE_URL (should be auto-set)"
    echo "   - SUPABASE_SERVICE_ROLE_KEY (should be auto-set)"
    echo ""
    echo "2. Update your Retell AI agent webhook URL to:"
    echo "   https://pdnclfznadtbuxeoszjx.supabase.co/functions/v1/retell-webhook-handler"
    echo ""
    echo "3. Test the webhook with the test script:"
    echo "   node test-voice-agent.js"
    echo ""
    echo "🎯 Webhook handler is ready for JSC alarm call-outs!"
else
    echo "❌ Deployment failed. Please check the error messages above."
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Make sure you're logged into Supabase CLI: supabase login"
    echo "2. Verify project reference: supabase projects list"
    echo "3. Check function syntax: deno check supabase/functions/retell-webhook-handler/index.ts"
    exit 1
fi
