import { useState, Suspense, lazy } from 'react'
import { Menu, X } from 'lucide-react'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { LoginForm } from './components/LoginForm'
import { UserProfile } from './components/UserProfile'
import { DevToolsButton } from './components/DevTools'

// Lazy load dashboard components for better performance
const BMSDashboard = lazy(() => import('./components/BMSDashboard'))
const AlarmDashboard = lazy(() => import('./components/AlarmDashboard'))
const BuildingManagement = lazy(() => import('./components/BuildingManagement'))
const EquipmentManagement = lazy(() => import('./components/EquipmentManagement'))
const EnergyDashboard = lazy(() => import('./components/EnergyDashboard'))
const WorkOrderManagement = lazy(() => import('./components/WorkOrderManagement'))
const NetworkDeviceManagement = lazy(() => import('./components/NetworkDeviceManagement'))

// Loading component for Suspense fallback
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      <p className="text-muted-foreground">Loading dashboard...</p>
    </div>
  </div>
)

function AppContent() {
  const { user, loading } = useAuth()
  const [demoMode, setDemoMode] = useState(false)
  const [currentView, setCurrentView] = useState('bms') // 'bms', 'alarms', 'buildings', 'equipment', 'energy', 'workorders', 'network'
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (demoMode || user) {
    return (
      <div className="min-h-screen bg-background">
        {/* Navigation Header */}
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-full mx-auto px-2 sm:px-3 lg:px-4">
            <div className="flex items-center justify-between h-16">
              {/* Left: App Title */}
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">JSC Alarm Call-Out App</h1>
              </div>

              {/* Center: Navigation Buttons (Desktop) */}
              <div className="hidden lg:flex items-center space-x-4">
                <button
                  onClick={() => setCurrentView('bms')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    currentView === 'bms'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  BMS Overview
                </button>

                <button
                  onClick={() => setCurrentView('alarms')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    currentView === 'alarms'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Alarms
                </button>

                <button
                  onClick={() => setCurrentView('buildings')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    currentView === 'buildings'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Buildings
                </button>

                <button
                  onClick={() => setCurrentView('equipment')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    currentView === 'equipment'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Equipment
                </button>

                <button
                  onClick={() => setCurrentView('energy')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    currentView === 'energy'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Energy
                </button>

                <button
                  onClick={() => setCurrentView('workorders')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    currentView === 'workorders'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Work Orders
                </button>

                <button
                  onClick={() => setCurrentView('network')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    currentView === 'network'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Network
                </button>

                {demoMode && (
                  <button
                    onClick={() => setDemoMode(false)}
                    className="px-3 py-2 rounded-md text-sm font-medium text-red-600 hover:text-red-700"
                  >
                    Exit Demo
                  </button>
                )}
              </div>

              {/* Right: Mobile Menu Button + User Profile */}
              <div className="flex items-center space-x-4">
                {/* Mobile menu button */}
                <button
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                  aria-label="Toggle mobile menu"
                >
                  {mobileMenuOpen ? (
                    <X className="h-6 w-6" />
                  ) : (
                    <Menu className="h-6 w-6" />
                  )}
                </button>

                {/* User Profile */}
                <UserProfile />
              </div>
            </div>
          </div>
        </nav>

        {/* Mobile Navigation Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden bg-white border-b shadow-sm">
            <div className="max-w-full mx-auto px-2 sm:px-3 lg:px-4 py-2 space-y-1">
              <button
                onClick={() => {
                  setCurrentView('bms')
                  setMobileMenuOpen(false)
                }}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'bms'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                BMS Overview
              </button>

              <button
                onClick={() => {
                  setCurrentView('alarms')
                  setMobileMenuOpen(false)
                }}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'alarms'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                Alarms
              </button>

              <button
                onClick={() => {
                  setCurrentView('buildings')
                  setMobileMenuOpen(false)
                }}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'buildings'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                Buildings
              </button>

              <button
                onClick={() => {
                  setCurrentView('equipment')
                  setMobileMenuOpen(false)
                }}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'equipment'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                Equipment
              </button>

              <button
                onClick={() => {
                  setCurrentView('energy')
                  setMobileMenuOpen(false)
                }}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'energy'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                Energy
              </button>

              <button
                onClick={() => {
                  setCurrentView('workorders')
                  setMobileMenuOpen(false)
                }}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'workorders'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                Work Orders
              </button>

              <button
                onClick={() => {
                  setCurrentView('network')
                  setMobileMenuOpen(false)
                }}
                className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'network'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                Network
              </button>

              {demoMode && (
                <button
                  onClick={() => {
                    setDemoMode(false)
                    setMobileMenuOpen(false)
                  }}
                  className="block w-full text-left px-3 py-2 rounded-md text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  Exit Demo
                </button>
              )}
            </div>
          </div>
        )}

        {/* Main Content */}
        <main>
          <Suspense fallback={<LoadingSpinner />}>
            {currentView === 'bms' ? (
              <BMSDashboard />
            ) : currentView === 'alarms' ? (
              <AlarmDashboard />
            ) : currentView === 'buildings' ? (
              <BuildingManagement />
            ) : currentView === 'equipment' ? (
              <EquipmentManagement />
            ) : currentView === 'energy' ? (
              <EnergyDashboard />
            ) : currentView === 'workorders' ? (
              <WorkOrderManagement />
            ) : currentView === 'network' ? (
              <NetworkDeviceManagement />
            ) : (
              <BMSDashboard />
            )}
          </Suspense>
        </main>
      </div>
    )
  }

  return <LoginForm onDemoMode={() => setDemoMode(true)} />
}

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-background">
        <AppContent />
        <DevToolsButton />
      </div>
    </AuthProvider>
  )
}

export default App
