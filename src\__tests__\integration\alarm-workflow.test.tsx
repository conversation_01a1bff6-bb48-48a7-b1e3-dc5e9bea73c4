import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider } from '../../contexts/AuthContext'
import AlarmDashboard from '../../components/AlarmDashboard'

// Mock the dependencies
vi.mock('../../lib/supabase')
vi.mock('../../lib/retellAI')
vi.mock('../../lib/shared/escalationService')

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  )
}

describe('Alarm Workflow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should complete full alarm acknowledgment workflow', async () => {
    const user = userEvent.setup()
    
    // Mock alarm data
    const mockAlarms = [
      {
        id: 'alarm-1',
        building_id: 'building-1',
        subject: 'Fire Alarm - Building A',
        status: 'received',
        severity_id: 4,
        created_at: '2024-01-01T10:00:00Z',
        alarm_details: 'Smoke detected in server room'
      }
    ]

    // Mock Supabase responses
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null }),
      eq: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: mockAlarms[0], error: null })
    }
    
    mockSupabase.supabase.from.mockReturnValue(mockChain)
    mockSupabase.hasSupabaseConfig = true

    renderWithProviders(<AlarmDashboard />)

    // Wait for alarms to load
    await waitFor(() => {
      expect(screen.getByText('Fire Alarm - Building A')).toBeInTheDocument()
    })

    // Verify alarm is in received status
    expect(screen.getByText('received')).toBeInTheDocument()

    // Find and click acknowledge button
    const acknowledgeButton = screen.getByRole('button', { name: /acknowledge/i })
    await user.click(acknowledgeButton)

    // Wait for acknowledgment to complete
    await waitFor(() => {
      expect(mockChain.update).toHaveBeenCalled()
    })

    // Verify the update was called with correct parameters
    expect(mockChain.eq).toHaveBeenCalledWith('id', 'alarm-1')
  })

  it('should handle alarm escalation workflow', async () => {
    const user = userEvent.setup()
    
    // Mock critical alarm
    const mockCriticalAlarm = {
      id: 'alarm-critical',
      building_id: 'building-1',
      subject: 'Critical System Failure',
      status: 'received',
      severity_id: 4,
      created_at: '2024-01-01T10:00:00Z'
    }

    // Mock escalation service
    const mockEscalationService = await import('../../lib/shared/escalationService')
    mockEscalationService.triggerEscalation.mockResolvedValue({
      success: true,
      escalationTriggered: true,
      callOutsCreated: 2
    })

    // Mock Supabase
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: [mockCriticalAlarm], error: null }),
      eq: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: mockCriticalAlarm, error: null })
    }
    
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    renderWithProviders(<AlarmDashboard />)

    // Wait for critical alarm to load
    await waitFor(() => {
      expect(screen.getByText('Critical System Failure')).toBeInTheDocument()
    })

    // Find and click escalate button
    const escalateButton = screen.getByRole('button', { name: /escalate/i })
    await user.click(escalateButton)

    // Wait for escalation to complete
    await waitFor(() => {
      expect(mockEscalationService.triggerEscalation).toHaveBeenCalledWith(
        expect.anything(),
        'alarm-critical'
      )
    })
  })

  it('should filter alarms by status and severity', async () => {
    const user = userEvent.setup()
    
    // Mock mixed alarm data
    const mockAlarms = [
      {
        id: 'alarm-1',
        subject: 'Fire Alarm',
        status: 'received',
        severity_id: 4,
        created_at: '2024-01-01T10:00:00Z'
      },
      {
        id: 'alarm-2',
        subject: 'HVAC Warning',
        status: 'acknowledged',
        severity_id: 2,
        created_at: '2024-01-01T11:00:00Z'
      },
      {
        id: 'alarm-3',
        subject: 'Security Alert',
        status: 'resolved',
        severity_id: 3,
        created_at: '2024-01-01T12:00:00Z'
      }
    ]

    // Mock Supabase
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null }),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null })
    }
    
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    renderWithProviders(<AlarmDashboard />)

    // Wait for all alarms to load
    await waitFor(() => {
      expect(screen.getByText('Fire Alarm')).toBeInTheDocument()
      expect(screen.getByText('HVAC Warning')).toBeInTheDocument()
      expect(screen.getByText('Security Alert')).toBeInTheDocument()
    })

    // Filter by status - show only received alarms
    const statusFilter = screen.getByRole('combobox', { name: /status/i })
    await user.click(statusFilter)
    await user.click(screen.getByText('received'))

    // Should only show received alarms
    await waitFor(() => {
      expect(screen.getByText('Fire Alarm')).toBeInTheDocument()
      expect(screen.queryByText('HVAC Warning')).not.toBeInTheDocument()
      expect(screen.queryByText('Security Alert')).not.toBeInTheDocument()
    })

    // Reset filter to show all
    await user.click(statusFilter)
    await user.click(screen.getByText('all'))

    // All alarms should be visible again
    await waitFor(() => {
      expect(screen.getByText('Fire Alarm')).toBeInTheDocument()
      expect(screen.getByText('HVAC Warning')).toBeInTheDocument()
      expect(screen.getByText('Security Alert')).toBeInTheDocument()
    })
  })

  it('should handle error states gracefully', async () => {
    // Mock Supabase error
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ 
        data: null, 
        error: { message: 'Database connection failed' } 
      })
    }
    
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    renderWithProviders(<AlarmDashboard />)

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument()
    })

    // Should show retry option
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
  })

  it('should handle real-time alarm updates', async () => {
    // Mock initial alarm data
    const mockAlarms = [
      {
        id: 'alarm-1',
        subject: 'Initial Alarm',
        status: 'received',
        created_at: '2024-01-01T10:00:00Z'
      }
    ]

    // Mock Supabase with subscription
    const mockSupabase = await import('../../lib/supabase')
    let subscriptionCallback: (payload: any) => void
    
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null }),
      on: vi.fn().mockImplementation((event, callback) => {
        subscriptionCallback = callback
        return {
          subscribe: vi.fn().mockReturnValue({
            unsubscribe: vi.fn()
          })
        }
      })
    }
    
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    renderWithProviders(<AlarmDashboard />)

    // Wait for initial alarm to load
    await waitFor(() => {
      expect(screen.getByText('Initial Alarm')).toBeInTheDocument()
    })

    // Simulate real-time update
    const newAlarm = {
      id: 'alarm-2',
      subject: 'New Real-time Alarm',
      status: 'received',
      created_at: '2024-01-01T11:00:00Z'
    }

    // Trigger subscription callback
    if (subscriptionCallback) {
      subscriptionCallback({
        eventType: 'INSERT',
        new: newAlarm,
        old: null
      })
    }

    // Should show the new alarm
    await waitFor(() => {
      expect(screen.getByText('New Real-time Alarm')).toBeInTheDocument()
    })
  })

  it('should maintain view mode preferences', async () => {
    const user = userEvent.setup()
    
    // Mock localStorage
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue('table'),
      setItem: vi.fn()
    }
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage
    })

    // Mock alarm data
    const mockAlarms = [
      {
        id: 'alarm-1',
        subject: 'Test Alarm',
        status: 'received',
        created_at: '2024-01-01T10:00:00Z'
      }
    ]

    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null })
    }
    
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    renderWithProviders(<AlarmDashboard />)

    // Should start in table view (from localStorage)
    await waitFor(() => {
      expect(screen.getByRole('table')).toBeInTheDocument()
    })

    // Switch to card view
    const viewToggle = screen.getByRole('button', { name: /card view/i })
    await user.click(viewToggle)

    // Should save preference to localStorage
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('alarmViewMode', 'card')
  })
})
