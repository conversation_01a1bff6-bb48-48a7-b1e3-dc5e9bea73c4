import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useAlarms } from '../../hooks/useAlarms'

// Mock performance.now for consistent timing
const mockPerformanceNow = vi.fn()
Object.defineProperty(global, 'performance', {
  value: { now: mockPerformanceNow }
})

// Mock large dataset for performance testing
const generateMockAlarms = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    id: `alarm-${index}`,
    building_id: `building-${index % 10}`,
    subject: `Test Alarm ${index}`,
    status: ['received', 'acknowledged', 'resolved'][index % 3],
    severity_id: (index % 4) + 1,
    created_at: new Date(Date.now() - index * 60000).toISOString(),
    alarm_details: `Details for alarm ${index}`,
    location_details: `Location ${index}`
  }))
}

describe('Performance Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockPerformanceNow.mockReturnValue(0)
  })

  it('should handle large alarm datasets efficiently', async () => {
    const LARGE_DATASET_SIZE = 1000
    const mockAlarms = generateMockAlarms(LARGE_DATASET_SIZE)
    
    // Mock Supabase to return large dataset
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null })
    }
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    // Measure rendering time
    let startTime = 0
    let endTime = 0
    
    mockPerformanceNow
      .mockReturnValueOnce(startTime = 100)
      .mockReturnValueOnce(endTime = 150) // 50ms render time

    const { result } = renderHook(() => useAlarms())

    // Wait for data to load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Verify data loaded correctly
    expect(result.current.alarms).toHaveLength(LARGE_DATASET_SIZE)
    expect(result.current.loading).toBe(false)

    // Performance assertion - should render within 100ms
    const renderTime = endTime - startTime
    expect(renderTime).toBeLessThan(100)
  })

  it('should filter large datasets efficiently', async () => {
    const LARGE_DATASET_SIZE = 5000
    const mockAlarms = generateMockAlarms(LARGE_DATASET_SIZE)
    
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null })
    }
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    const { result } = renderHook(() => useAlarms())

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Measure filtering performance
    const startTime = performance.now()
    
    act(() => {
      const filteredAlarms = result.current.getAlarmsByStatus('received')
      expect(filteredAlarms.length).toBeGreaterThan(0)
    })
    
    const endTime = performance.now()
    const filterTime = endTime - startTime

    // Filtering should be fast even with large datasets
    expect(filterTime).toBeLessThan(10) // 10ms threshold
  })

  it('should memoize expensive calculations', async () => {
    const mockAlarms = generateMockAlarms(100)
    
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null })
    }
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    const { result, rerender } = renderHook(() => useAlarms())

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Get initial stats reference
    const initialStats = result.current.alarmStats

    // Re-render without changing alarms data
    rerender()

    // Stats should be the same object reference (memoized)
    expect(result.current.alarmStats).toBe(initialStats)
  })

  it('should handle rapid state updates efficiently', async () => {
    const mockAlarms = generateMockAlarms(50)
    
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null }),
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockResolvedValue({ data: { id: 'alarm-1', status: 'acknowledged' }, error: null })
    }
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    const { result } = renderHook(() => useAlarms())

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Perform rapid acknowledgments
    const startTime = performance.now()
    
    await act(async () => {
      const promises = []
      for (let i = 0; i < 10; i++) {
        promises.push(result.current.acknowledgeAlarm(`alarm-${i}`))
      }
      await Promise.all(promises)
    })
    
    const endTime = performance.now()
    const batchTime = endTime - startTime

    // Batch operations should complete within reasonable time
    expect(batchTime).toBeLessThan(500) // 500ms for 10 operations
  })

  it('should optimize re-renders with callback memoization', () => {
    const { result, rerender } = renderHook(() => useAlarms())

    // Get initial callback references
    const initialGetByStatus = result.current.getAlarmsByStatus
    const initialGetBySeverity = result.current.getAlarmsBySeverity
    const initialAcknowledge = result.current.acknowledgeAlarm

    // Re-render multiple times
    rerender()
    rerender()
    rerender()

    // Callbacks should maintain same reference (memoized)
    expect(result.current.getAlarmsByStatus).toBe(initialGetByStatus)
    expect(result.current.getAlarmsBySeverity).toBe(initialGetBySeverity)
    expect(result.current.acknowledgeAlarm).toBe(initialAcknowledge)
  })

  it('should handle memory efficiently with large datasets', async () => {
    const VERY_LARGE_DATASET = 10000
    const mockAlarms = generateMockAlarms(VERY_LARGE_DATASET)
    
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null })
    }
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    const { result, unmount } = renderHook(() => useAlarms())

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Verify large dataset loaded
    expect(result.current.alarms).toHaveLength(VERY_LARGE_DATASET)

    // Unmount to test cleanup
    unmount()

    // Memory should be cleaned up (no memory leaks)
    // This is more of a conceptual test - in real scenarios you'd use memory profiling tools
    expect(true).toBe(true) // Placeholder assertion
  })

  it('should debounce rapid filter changes', async () => {
    const mockAlarms = generateMockAlarms(1000)
    
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null })
    }
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    const { result } = renderHook(() => useAlarms())

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Simulate rapid filter changes
    const startTime = performance.now()
    
    act(() => {
      // Multiple rapid filter calls
      result.current.getAlarmsByStatus('received')
      result.current.getAlarmsByStatus('acknowledged')
      result.current.getAlarmsByStatus('resolved')
      result.current.getAlarmsByStatus('received')
    })
    
    const endTime = performance.now()
    const filterTime = endTime - startTime

    // Should handle rapid changes efficiently
    expect(filterTime).toBeLessThan(50) // 50ms threshold
  })

  it('should optimize alarm statistics calculation', async () => {
    const LARGE_DATASET = 2000
    const mockAlarms = generateMockAlarms(LARGE_DATASET)
    
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null })
    }
    mockSupabase.supabase.from.mockReturnValue(mockChain)

    const { result } = renderHook(() => useAlarms())

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100))
    })

    // Measure stats calculation time
    const startTime = performance.now()
    
    const stats = result.current.alarmStats
    
    const endTime = performance.now()
    const statsTime = endTime - startTime

    // Stats calculation should be fast
    expect(statsTime).toBeLessThan(20) // 20ms threshold
    
    // Verify stats are calculated correctly
    expect(stats.total).toBe(LARGE_DATASET)
    expect(stats.byStatus).toBeDefined()
    expect(stats.bySeverity).toBeDefined()
    expect(typeof stats.critical).toBe('number')
    expect(typeof stats.recent).toBe('number')
  })
})
