import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'

const BMSDashboard = () => {
  const { user: _user } = useAuth()
  const [dashboardData, setDashboardData] = useState({
    buildings: [],
    alarms: [],
    equipment: [],
    workOrders: [],
    energyData: [],
    loading: true,
    error: null
  })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setDashboardData(prev => ({ ...prev, loading: true }))

      // Fetch all dashboard data in parallel
      const [
        buildingsResult,
        alarmsResult,
        equipmentResult,
        workOrdersResult,
        energyResult
      ] = await Promise.all([
        // Buildings
        supabase
          .from('buildings')
          .select('*')
          .eq('is_active', true),

        // Recent alarms (last 24 hours)
        supabase
          .from('alarm_notifications')
          .select('*, severity:severity_levels(name, color), building:buildings(name)')
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
          .order('created_at', { ascending: false }),

        // Equipment summary
        supabase
          .from('equipment')
          .select('status, equipment_type, building_id'),

        // Active work orders
        supabase
          .from('work_orders')
          .select('status, priority, building_id, created_at')
          .neq('status', 'completed'),

        // Recent energy data (last 24 hours)
        supabase
          .from('energy_data')
          .select('energy_type, consumption_value, cost, building_id')
          .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      ])

      // Check for errors
      const errors = [
        buildingsResult.error,
        alarmsResult.error,
        equipmentResult.error,
        workOrdersResult.error,
        energyResult.error
      ].filter(Boolean)

      if (errors.length > 0) {
        throw new Error(`Database errors: ${errors.map(e => e.message).join(', ')}`)
      }

      setDashboardData({
        buildings: buildingsResult.data || [],
        alarms: alarmsResult.data || [],
        equipment: equipmentResult.data || [],
        workOrders: workOrdersResult.data || [],
        energyData: energyResult.data || [],
        loading: false,
        error: null
      })

    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setDashboardData(prev => ({
        ...prev,
        loading: false,
        error: err.message
      }))
    }
  }

  const calculateMetrics = () => {
    const { buildings, alarms, equipment, workOrders, energyData } = dashboardData

    // Alarm metrics
    const criticalAlarms = alarms.filter(a => 
      a.severity?.name === 'CRITICAL' && a.status !== 'resolved'
    ).length

    const unacknowledgedAlarms = alarms.filter(a => a.status === 'received').length

    // Equipment metrics
    const equipmentByStatus = equipment.reduce((acc, eq) => {
      acc[eq.status] = (acc[eq.status] || 0) + 1
      return acc
    }, {})

    // Work order metrics
    const criticalWorkOrders = workOrders.filter(wo => wo.priority === 'critical').length
    const overdueWorkOrders = workOrders.filter(wo => 
      wo.scheduled_date && new Date(wo.scheduled_date) < new Date()
    ).length

    // Energy metrics (last 24h)
    const totalEnergyCost = energyData.reduce((sum, data) => sum + (data.cost || 0), 0)
    const energyByType = energyData.reduce((acc, data) => {
      acc[data.energy_type] = (acc[data.energy_type] || 0) + (data.consumption_value || 0)
      return acc
    }, {})

    return {
      buildings: buildings.length,
      criticalAlarms,
      unacknowledgedAlarms,
      totalAlarms: alarms.length,
      activeEquipment: equipmentByStatus.active || 0,
      offlineEquipment: equipmentByStatus.offline || 0,
      maintenanceEquipment: equipmentByStatus.maintenance || 0,
      totalEquipment: equipment.length,
      criticalWorkOrders,
      overdueWorkOrders,
      totalWorkOrders: workOrders.length,
      totalEnergyCost,
      energyByType
    }
  }

  const metrics = calculateMetrics()

  if (dashboardData.loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading BMS dashboard...</p>
        </div>
      </div>
    )
  }

  if (dashboardData.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
        <h3 className="text-red-800 font-medium">Error Loading Dashboard</h3>
        <p className="text-red-600 mt-1">{dashboardData.error}</p>
        <button 
          onClick={fetchDashboardData}
          className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="p-3 sm:p-4 max-w-full mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Building Management System</h1>
        <p className="text-gray-600">Comprehensive overview of all building systems and operations</p>
      </div>

      {/* Critical Alerts Banner */}
      {(metrics.criticalAlarms > 0 || metrics.criticalWorkOrders > 0) && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Critical Issues Require Attention</h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc list-inside space-y-1">
                  {metrics.criticalAlarms > 0 && (
                    <li>{metrics.criticalAlarms} critical alarm{metrics.criticalAlarms !== 1 ? 's' : ''} active</li>
                  )}
                  {metrics.criticalWorkOrders > 0 && (
                    <li>{metrics.criticalWorkOrders} critical work order{metrics.criticalWorkOrders !== 1 ? 's' : ''} pending</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Buildings Overview */}
        <div className="bg-white rounded-lg shadow p-6 border-l-4 border-blue-500">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span className="text-white text-lg">🏢</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Buildings</dt>
                <dd className="text-lg font-medium text-gray-900">{metrics.buildings}</dd>
              </dl>
            </div>
          </div>
        </div>

        {/* Alarms Overview */}
        <div className="bg-white rounded-lg shadow p-6 border-l-4 border-red-500">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                <span className="text-white text-lg">🚨</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Active Alarms</dt>
                <dd className="text-lg font-medium text-gray-900">{metrics.totalAlarms}</dd>
                <dd className="text-sm text-red-600">{metrics.criticalAlarms} critical</dd>
              </dl>
            </div>
          </div>
        </div>

        {/* Equipment Overview */}
        <div className="bg-white rounded-lg shadow p-6 border-l-4 border-green-500">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span className="text-white text-lg">⚙️</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Equipment</dt>
                <dd className="text-lg font-medium text-gray-900">{metrics.totalEquipment}</dd>
                <dd className="text-sm text-green-600">{metrics.activeEquipment} active</dd>
              </dl>
            </div>
          </div>
        </div>

        {/* Work Orders Overview */}
        <div className="bg-white rounded-lg shadow p-6 border-l-4 border-yellow-500">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <span className="text-white text-lg">🔧</span>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Work Orders</dt>
                <dd className="text-lg font-medium text-gray-900">{metrics.totalWorkOrders}</dd>
                <dd className="text-sm text-yellow-600">{metrics.criticalWorkOrders} critical</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Status Grids */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Equipment Status */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Equipment Status</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{metrics.activeEquipment}</div>
                <div className="text-sm text-gray-500">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{metrics.maintenanceEquipment}</div>
                <div className="text-sm text-gray-500">Maintenance</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{metrics.offlineEquipment}</div>
                <div className="text-sm text-gray-500">Offline</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{metrics.totalEquipment}</div>
                <div className="text-sm text-gray-500">Total</div>
              </div>
            </div>
          </div>
        </div>

        {/* Energy Summary */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Energy (24h)</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Total Cost</span>
                <span className="text-lg font-medium text-green-600">
                  ${metrics.totalEnergyCost.toLocaleString()}
                </span>
              </div>
              {Object.entries(metrics.energyByType).map(([type, consumption]) => (
                <div key={type} className="flex justify-between">
                  <span className="text-sm text-gray-500 capitalize">
                    {type.replace('_', ' ')}
                  </span>
                  <span className="text-sm font-medium text-gray-900">
                    {consumption.toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center">
              <div className="text-2xl mb-2">🚨</div>
              <div className="text-sm font-medium">View Alarms</div>
            </button>
            <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center">
              <div className="text-2xl mb-2">⚙️</div>
              <div className="text-sm font-medium">Manage Equipment</div>
            </button>
            <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center">
              <div className="text-2xl mb-2">🔧</div>
              <div className="text-sm font-medium">Work Orders</div>
            </button>
            <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-center">
              <div className="text-2xl mb-2">⚡</div>
              <div className="text-sm font-medium">Energy Dashboard</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BMSDashboard
