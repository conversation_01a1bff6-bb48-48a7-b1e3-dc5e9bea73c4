import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import retellAI from '../lib/retellAI'

/**
 * Call-Out History Component
 * Displays call-out records and their status
 */
const CallOutHistory = () => {
  const { user: _user } = useAuth()
  const [callOuts, setCallOuts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedBuilding, setSelectedBuilding] = useState('all')
  const [buildings, setBuildings] = useState([])
  const [expandedCallOut, setExpandedCallOut] = useState(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [callOutsResult, buildingsResult] = await Promise.all([
        supabase
          .from('call_outs')
          .select(`
            *,
            alarm_notifications(*),
            escalation_contacts(*),
            call_out_attempts(*)
          `)
          .order('created_at', { ascending: false }),
        
        supabase
          .from('buildings')
          .select('*')
          .eq('is_active', true)
          .order('name')
      ])

      if (callOutsResult.error) throw callOutsResult.error
      if (buildingsResult.error) throw buildingsResult.error

      setCallOuts(callOutsResult.data || [])
      setBuildings(buildingsResult.data || [])
      
    } catch (err) {
      console.error('Error fetching call-out history:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'dispatched': 'bg-blue-100 text-blue-800',
      'calling': 'bg-purple-100 text-purple-800',
      'completed': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800',
      'failed': 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const getCallStatusColor = (callStatus) => {
    const colors = {
      'pending': 'bg-gray-100 text-gray-800',
      'calling': 'bg-blue-100 text-blue-800',
      'answered': 'bg-green-100 text-green-800',
      'no_answer': 'bg-yellow-100 text-yellow-800',
      'busy': 'bg-orange-100 text-orange-800',
      'failed': 'bg-red-100 text-red-800',
      'completed': 'bg-green-100 text-green-800'
    }
    return colors[callStatus] || 'bg-gray-100 text-gray-800'
  }

  const formatDuration = (seconds) => {
    if (!seconds) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const handleRetellCallDetails = async (retellCallId) => {
    if (!retellCallId || !retellAI.isConfigured()) return

    try {
      const callDetails = await retellAI.getCall(retellCallId)
      console.log('Retell AI call details:', callDetails)
      // You could show this in a modal or update the UI
    } catch (error) {
      console.error('Error fetching Retell AI call details:', error)
    }
  }

  // Filter call-outs
  const filteredCallOuts = callOuts.filter(callOut => {
    const statusMatch = selectedStatus === 'all' || callOut.status === selectedStatus
    const buildingMatch = selectedBuilding === 'all' || 
      callOut.alarm_notifications?.building_id === selectedBuilding
    return statusMatch && buildingMatch
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Call-Out History</h1>
        <p className="text-gray-600">View alarm call-out records and escalation status</p>
      </div>

      {/* Filters */}
      <div className="flex space-x-4">
        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-2"
        >
          <option value="all">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="dispatched">Dispatched</option>
          <option value="calling">Calling</option>
          <option value="completed">Completed</option>
          <option value="cancelled">Cancelled</option>
          <option value="failed">Failed</option>
        </select>

        <select
          value={selectedBuilding}
          onChange={(e) => setSelectedBuilding(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-2"
        >
          <option value="all">All Buildings</option>
          {buildings.map(building => (
            <option key={building.id} value={building.id}>
              {building.name}
            </option>
          ))}
        </select>

        <button
          onClick={fetchData}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Refresh
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={() => setError(null)}
            className="text-red-600 hover:text-red-800 text-sm mt-2"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Call-Outs List */}
      <div className="space-y-4">
        {filteredCallOuts.map(callOut => (
          <div key={callOut.id} className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(callOut.status)}`}>
                    {callOut.status}
                  </span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCallStatusColor(callOut.call_status)}`}>
                    {callOut.call_status}
                  </span>
                  <span className="text-sm text-gray-500">
                    Level {callOut.escalation_level}
                  </span>
                  {callOut.retry_count > 0 && (
                    <span className="text-sm text-gray-500">
                      Retry {callOut.retry_count}
                    </span>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Contact:</span>
                    <p>{callOut.escalation_contacts?.contact_name || 'Unknown'}</p>
                    <p className="text-gray-500">{callOut.escalation_contacts?.contact_phone}</p>
                  </div>

                  <div>
                    <span className="font-medium text-gray-700">Alarm:</span>
                    <p>{callOut.alarm_notifications?.subject || 'No subject'}</p>
                    <p className="text-gray-500">
                      {new Date(callOut.alarm_notifications?.alarm_time || callOut.created_at).toLocaleString()}
                    </p>
                  </div>

                  <div>
                    <span className="font-medium text-gray-700">Call Details:</span>
                    <p>Duration: {formatDuration(callOut.call_duration_seconds)}</p>
                    {callOut.acknowledged_by_contact && (
                      <p className="text-green-600">✓ Acknowledged</p>
                    )}
                  </div>
                </div>

                {callOut.escalation_reason && (
                  <div className="mt-3 p-3 bg-yellow-50 rounded-md">
                    <span className="font-medium text-yellow-800">Escalation Reason:</span>
                    <p className="text-yellow-700">{callOut.escalation_reason}</p>
                  </div>
                )}

                {callOut.contact_response && (
                  <div className="mt-3 p-3 bg-green-50 rounded-md">
                    <span className="font-medium text-green-800">Contact Response:</span>
                    <p className="text-green-700">{callOut.contact_response}</p>
                  </div>
                )}
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => setExpandedCallOut(expandedCallOut === callOut.id ? null : callOut.id)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {expandedCallOut === callOut.id ? 'Hide Details' : 'Show Details'}
                </button>
                
                {callOut.retell_call_id && retellAI.isConfigured() && (
                  <button
                    onClick={() => handleRetellCallDetails(callOut.retell_call_id)}
                    className="text-purple-600 hover:text-purple-800 text-sm"
                  >
                    Retell Details
                  </button>
                )}
              </div>
            </div>

            {/* Expanded Details */}
            {expandedCallOut === callOut.id && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3">Call Attempts</h4>
                
                {callOut.call_out_attempts && callOut.call_out_attempts.length > 0 ? (
                  <div className="space-y-3">
                    {callOut.call_out_attempts.map((attempt) => (
                      <div key={attempt.id} className="bg-gray-50 rounded-md p-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-medium">Attempt {attempt.attempt_number}</span>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCallStatusColor(attempt.call_status)}`}>
                                {attempt.call_status}
                              </span>
                            </div>
                            
                            <div className="text-sm text-gray-600 space-y-1">
                              {attempt.call_start_time && (
                                <p>Started: {new Date(attempt.call_start_time).toLocaleString()}</p>
                              )}
                              {attempt.call_end_time && (
                                <p>Ended: {new Date(attempt.call_end_time).toLocaleString()}</p>
                              )}
                              {attempt.call_duration_seconds && (
                                <p>Duration: {formatDuration(attempt.call_duration_seconds)}</p>
                              )}
                              {attempt.disconnection_reason && (
                                <p>Reason: {attempt.disconnection_reason}</p>
                              )}
                            </div>
                          </div>
                          
                          {attempt.call_recording_url && (
                            <a
                              href={attempt.call_recording_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                              Recording
                            </a>
                          )}
                        </div>

                        {attempt.call_transcript && (
                          <div className="mt-2 p-2 bg-white rounded border">
                            <span className="font-medium text-gray-700">Transcript:</span>
                            <p className="text-sm text-gray-600 mt-1">{attempt.call_transcript}</p>
                          </div>
                        )}

                        {attempt.error_message && (
                          <div className="mt-2 p-2 bg-red-50 rounded border border-red-200">
                            <span className="font-medium text-red-700">Error:</span>
                            <p className="text-sm text-red-600 mt-1">{attempt.error_message}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No call attempts recorded</p>
                )}

                {/* Raw Data (for debugging) */}
                {callOut.metadata && Object.keys(callOut.metadata).length > 0 && (
                  <div className="mt-4">
                    <h5 className="font-medium text-gray-700 mb-2">Metadata</h5>
                    <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(callOut.metadata, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {filteredCallOuts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No call-out records found.
          </div>
        )}
      </div>
    </div>
  )
}

export default CallOutHistory
