import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>ug, Eye, EyeOff, RotateCcw, AlertTriangle } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Switch } from './ui/switch'
import { Label } from './ui/label'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from './ui/tabs'
import { detectProblematicExtensions, getErrorFilteringStats } from '../lib/errorFilter'
import SentryTestComponent from './SentryTestComponent'

export const DevTools = ({ isOpen, onClose }) => {
  const [extensionInfo, setExtensionInfo] = useState(null)
  const [filteringStats, setFilteringStats] = useState(null)
  const [debugEnabled, setDebugEnabled] = useState(false)
  const [filteringActive, setFilteringActive] = useState(true)

  useEffect(() => {
    if (isOpen) {
      // Refresh data when panel opens
      setExtensionInfo(detectProblematicExtensions())
      setFilteringStats(getErrorFilteringStats())
      setDebugEnabled(!!window.DEBUG_EXTENSION_ERRORS)
      setFilteringActive(typeof window.restoreConsole === 'function')
    }
  }, [isOpen])

  const handleToggleDebug = (enabled) => {
    if (enabled) {
      window.enableExtensionErrorDebugging()
    } else {
      window.disableExtensionErrorDebugging()
    }
    setDebugEnabled(enabled)
  }

  const handleToggleFiltering = (enabled) => {
    if (enabled) {
      // Re-initialize filtering
      window.location.reload()
    } else {
      // Restore original console
      if (window.restoreConsole) {
        window.restoreConsole()
      }
    }
    setFilteringActive(enabled)
  }

  const handleClearConsole = () => {
    console.clear()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bug className="h-5 w-5" />
                Development Tools
              </CardTitle>
              <CardDescription>
                Manage browser extension error filtering, debugging, and Sentry integration
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onClose}>
              ×
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="error-filtering" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="error-filtering">Error Filtering</TabsTrigger>
              <TabsTrigger value="sentry-testing">Sentry Testing</TabsTrigger>
            </TabsList>

            <TabsContent value="error-filtering" className="space-y-6 mt-6">
          {/* Extension Detection */}
          <div className="space-y-3">
            <h3 className="font-medium">Browser Extensions</h3>
            {extensionInfo && (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  {extensionInfo.recommendation}
                </p>
                {extensionInfo.extensions.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {extensionInfo.extensions.map((ext, index) => (
                      <Badge key={index} variant="secondary">
                        {ext}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Error Filtering Controls */}
          <div className="space-y-4">
            <h3 className="font-medium">Error Filtering</h3>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Enable Error Filtering</Label>
                <p className="text-sm text-muted-foreground">
                  Filter out browser extension errors from console
                </p>
              </div>
              <Switch
                checked={filteringActive}
                onCheckedChange={handleToggleFiltering}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Debug Filtered Errors</Label>
                <p className="text-sm text-muted-foreground">
                  Show filtered errors with [FILTERED] prefix
                </p>
              </div>
              <Switch
                checked={debugEnabled}
                onCheckedChange={handleToggleDebug}
                disabled={!filteringActive}
              />
            </div>
          </div>

          {/* Filtering Statistics */}
          {filteringStats && (
            <div className="space-y-3">
              <h3 className="font-medium">Filtering Statistics</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {filteringStats.filteredErrorsCount}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Filtered Errors
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {filteringStats.filteredWarningsCount}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Filtered Warnings
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {filteringStats.filteredRejectionsCount}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Filtered Rejections
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Console Controls */}
          <div className="space-y-3">
            <h3 className="font-medium">Console Controls</h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearConsole}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Clear Console
              </Button>
            </div>
          </div>

          {/* Help & Instructions */}
          <div className="space-y-3">
            <h3 className="font-medium">Console Commands</h3>
            <div className="bg-muted p-3 rounded-md text-sm font-mono space-y-1">
              <div><strong>enableExtensionErrorDebugging()</strong> - Show filtered errors</div>
              <div><strong>disableExtensionErrorDebugging()</strong> - Hide filtered errors</div>
              <div><strong>restoreConsole()</strong> - Disable error filtering</div>
              <div><strong>getErrorFilteringStats()</strong> - View filtering statistics</div>
            </div>
          </div>

          {/* Troubleshooting */}
          <div className="space-y-3">
            <h3 className="font-medium">Troubleshooting</h3>
            <div className="text-sm text-muted-foreground space-y-2">
              <p>
                <strong>Still seeing extension errors?</strong> Some extensions may use different error patterns. 
                You can add custom patterns to the error filter.
              </p>
              <p>
                <strong>Missing application errors?</strong> Disable filtering temporarily or enable debug mode 
                to ensure legitimate errors aren't being filtered.
              </p>
              <p>
                <strong>Performance impact?</strong> Error filtering has minimal performance impact and only 
                runs in development mode.
              </p>
            </div>
          </div>

          {/* Extension Recommendations */}
          <div className="space-y-3">
            <h3 className="font-medium">Extension Recommendations</h3>
            <div className="text-sm text-muted-foreground space-y-2">
              <p>
                <strong>For Development:</strong> Consider disabling non-essential extensions while developing 
                to reduce console noise.
              </p>
              <p>
                <strong>Common Culprits:</strong> Ad blockers, password managers, social media extensions, 
                and developer tools often generate console errors.
              </p>
              <p>
                <strong>Incognito Mode:</strong> Use incognito/private browsing mode to test without extensions.
              </p>
            </div>
          </div>
            </TabsContent>

            <TabsContent value="sentry-testing" className="mt-6">
              <SentryTestComponent />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

// Development-only component that adds a floating dev tools button
export const DevToolsButton = () => {
  const [isOpen, setIsOpen] = useState(false)

  // Only show in development mode
  if (import.meta.env.PROD) return null

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 z-40 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Open Development Tools"
      >
        <Settings className="h-5 w-5" />
      </button>

      <DevTools isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  )
}
