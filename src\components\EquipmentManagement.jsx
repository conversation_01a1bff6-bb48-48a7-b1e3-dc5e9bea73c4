import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useViewMode } from '../hooks/useViewMode'
import NetworkDevicePagination from './network/NetworkDevicePagination'
import ScrollSnapTable from './ui/ScrollSnapTable'

const EquipmentManagement = () => {
  const { user: _user } = useAuth()
  const { viewMode, setViewMode, isLoading: _viewModeLoading } = useViewMode('equipment', 'card')
  const [equipment, setEquipment] = useState([])
  const [buildings, setBuildings] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedBuilding, setSelectedBuilding] = useState('all')
  const [selectedType, setSelectedType] = useState('all')
  const [_showAddModal, setShowAddModal] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(25)

  const equipmentTypes = [
    'HVAC', 'Electrical', 'Security', 'Fire Safety', 'Lighting', 'Water', 'Elevator'
  ]

  const _equipmentCategories = {
    'HVAC': ['Chiller', 'Boiler', 'AHU', 'VAV', 'Heat Pump', 'Fan', 'Damper'],
    'Electrical': ['Panel', 'Transformer', 'Generator', 'UPS', 'Meter'],
    'Security': ['Camera', 'Access Control', 'Intrusion Detector', 'Intercom'],
    'Fire Safety': ['Smoke Detector', 'Sprinkler', 'Fire Panel', 'Emergency Light'],
    'Lighting': ['LED Fixture', 'Control Panel', 'Sensor', 'Emergency Light'],
    'Water': ['Pump', 'Tank', 'Valve', 'Meter', 'Filter'],
    'Elevator': ['Motor', 'Controller', 'Safety System', 'Door System']
  }

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch buildings
      const { data: buildingsData, error: buildingsError } = await supabase
        .from('buildings')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (buildingsError) throw buildingsError
      setBuildings(buildingsData || [])

      // Fetch equipment with building information
      const { data: equipmentData, error: equipmentError } = await supabase
        .from('equipment')
        .select(`
          *,
          building:buildings(name, building_code),
          parent_equipment:equipment!parent_equipment_id(id, category, location)
        `)
        .order('created_at', { ascending: false })

      if (equipmentError) throw equipmentError
      setEquipment(equipmentData || [])

    } catch (err) {
      console.error('Error fetching data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const filteredEquipment = equipment.filter(item => {
    const buildingMatch = selectedBuilding === 'all' || item.building_id === selectedBuilding
    const typeMatch = selectedType === 'all' || item.equipment_type === selectedType
    return buildingMatch && typeMatch
  })

  // Pagination calculations
  const totalPages = Math.ceil(filteredEquipment.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedEquipment = filteredEquipment.slice(startIndex, endIndex)

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [selectedBuilding, selectedType])

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#22c55e'
      case 'maintenance': return '#f59e0b'
      case 'offline': return '#ef4444'
      case 'decommissioned': return '#6b7280'
      default: return '#6b7280'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return '✅'
      case 'maintenance': return '🔧'
      case 'offline': return '❌'
      case 'decommissioned': return '🗑️'
      default: return '❓'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading equipment...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
        <h3 className="text-red-800 font-medium">Error Loading Equipment</h3>
        <p className="text-red-600 mt-1">{error}</p>
      </div>
    )
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-6 space-y-4">
      {/* Header and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        {/* Header Row */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-6">
          {/* Title and Description */}
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Equipment Management</h1>
            <p className="text-gray-600">
              Monitor and manage building equipment and assets
              {equipment.length > 0 && (
                <span className="ml-2 text-sm text-gray-500">({equipment.length} items)</span>
              )}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex-shrink-0 flex flex-col sm:flex-row items-start sm:items-center gap-3">
            {/* View Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('card')}
                className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'card'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                <span className="hidden sm:inline">Cards</span>
              </button>

              <button
                onClick={() => setViewMode('table')}
                className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'table'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
                <span className="hidden sm:inline">Table</span>
              </button>
            </div>

            <button
              onClick={() => setShowAddModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Add Equipment
            </button>
          </div>
        </div>

        {/* Filters Row */}
        <div className="flex flex-col xl:flex-row xl:items-start xl:justify-between gap-4">
          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Building</label>
              <select
                value={selectedBuilding}
                onChange={(e) => setSelectedBuilding(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Buildings</option>
                {buildings.map(building => (
                  <option key={building.id} value={building.id}>
                    {building.name} ({building.building_code})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Equipment Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                {equipmentTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="flex gap-4 text-sm">
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-blue-500">
              <span className="text-gray-500">Total:</span>
              <span className="ml-2 font-bold text-gray-900">{equipment.length}</span>
            </div>
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-green-500">
              <span className="text-gray-500">Active:</span>
              <span className="ml-2 font-bold text-green-600">
                {equipment.filter(e => e.status === 'active').length}
              </span>
            </div>
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-yellow-500">
              <span className="text-gray-500">Maintenance:</span>
              <span className="ml-2 font-bold text-yellow-600">
                {equipment.filter(e => e.status === 'maintenance').length}
              </span>
            </div>
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-red-500">
              <span className="text-gray-500">Offline:</span>
              <span className="ml-2 font-bold text-red-600">
                {equipment.filter(e => e.status === 'offline').length}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Equipment List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Equipment ({filteredEquipment.length})
          </h2>
        </div>

        {filteredEquipment.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No equipment found matching the selected criteria.</p>
          </div>
        ) : viewMode === 'card' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
            {paginatedEquipment.map(item => (
              <EquipmentCard
                key={item.id}
                equipment={item}
                getStatusColor={getStatusColor}
                getStatusIcon={getStatusIcon}
              />
            ))}
          </div>
        ) : (
          <EquipmentTable
            equipment={paginatedEquipment}
            getStatusColor={getStatusColor}
            getStatusIcon={getStatusIcon}
          />
        )}

        {/* Pagination Controls */}
        {filteredEquipment.length > 0 && (
          <div className="border-t border-gray-200 bg-white">
            <NetworkDevicePagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={filteredEquipment.length}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Equipment Card Component
const EquipmentCard = ({ equipment, getStatusColor, getStatusIcon }) => {
  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <span className="text-lg">{getStatusIcon(equipment.status)}</span>
          <div>
            <h3 className="font-medium text-gray-900">{equipment.category || 'Unknown Category'}</h3>
            <p className="text-sm text-gray-500">{equipment.equipment_type}</p>
          </div>
        </div>
        <span
          className="px-2 py-1 text-xs font-medium rounded-full text-white"
          style={{ backgroundColor: getStatusColor(equipment.status) }}
        >
          {equipment.status.toUpperCase()}
        </span>
      </div>

      <div className="space-y-2 text-sm text-gray-600">
        <div>
          <strong>Building:</strong> {equipment.building?.name || 'Unknown'}
        </div>
        {equipment.manufacturer && (
          <div>
            <strong>Manufacturer:</strong> {equipment.manufacturer}
          </div>
        )}
        {equipment.model && (
          <div>
            <strong>Model:</strong> {equipment.model}
          </div>
        )}
        {equipment.location && (
          <div>
            <strong>Location:</strong>
            {equipment.location.floor && ` Floor ${equipment.location.floor}`}
            {equipment.location.room && ` Room ${equipment.location.room}`}
            {equipment.location.zone && ` (${equipment.location.zone})`}
          </div>
        )}
        {equipment.installation_date && (
          <div>
            <strong>Installed:</strong> {new Date(equipment.installation_date).toLocaleDateString()}
          </div>
        )}
      </div>

      <div className="mt-4 flex gap-2">
        <button className="flex-1 px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
          View Details
        </button>
        <button className="flex-1 px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded hover:bg-gray-200">
          Edit
        </button>
      </div>
    </div>
  )
}

// Equipment Table Component
const EquipmentTable = ({ equipment, getStatusColor, getStatusIcon }) => {
  return (
    <ScrollSnapTable className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50 sticky top-0 z-10">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Equipment
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Building
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Type
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Location
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Installed
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {equipment.map((item, index) => (
            <tr key={item.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors`}>
              <td className="px-6 py-1.5 whitespace-nowrap">
                <div className="flex items-center">
                  <span className="mr-2">{getStatusIcon(item.status)}</span>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {item.category || 'Unknown Category'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {item.manufacturer} {item.model}
                    </div>
                  </div>
                </div>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-sm text-gray-900">
                <div>{item.building?.name || 'Unknown'}</div>
                <div className="text-gray-500 text-xs">{item.building?.building_code}</div>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-sm text-gray-900">
                {item.equipment_type}
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap">
                <span
                  className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white"
                  style={{ backgroundColor: getStatusColor(item.status) }}
                >
                  {item.status.toUpperCase()}
                </span>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-sm text-gray-900">
                {item.location && (
                  <div>
                    {item.location.floor && `Floor ${item.location.floor}`}
                    {item.location.room && ` Room ${item.location.room}`}
                    {item.location.zone && (
                      <div className="text-gray-500 text-xs">{item.location.zone}</div>
                    )}
                  </div>
                )}
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-sm text-gray-900">
                {item.installation_date ?
                  new Date(item.installation_date).toLocaleDateString() :
                  'N/A'
                }
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                  <button className="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 border border-blue-300 rounded hover:bg-blue-50">
                    View
                  </button>
                  <button className="text-gray-600 hover:text-gray-900 text-xs px-2 py-1 border border-gray-300 rounded hover:bg-gray-50">
                    Edit
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </ScrollSnapTable>
  )
}

export default EquipmentManagement
