import { useState } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { useAuth } from '../contexts/AuthContext'
import { hasSupabaseConfig } from '../lib/supabase'

export const LoginForm = ({ onDemoMode }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isSignUp, setIsSignUp] = useState(false)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  const { signIn, signUp } = useAuth()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    try {
      const { error } = isSignUp 
        ? await signUp(email, password)
        : await signIn(email, password)

      if (error) {
        setMessage(error.message)
      } else if (isSignUp) {
        setMessage('Check your email for the confirmation link!')
      }
    } catch {
      setMessage('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle>{isSignUp ? 'Sign Up' : 'Sign In'}</CardTitle>
          <CardDescription>
            {isSignUp
              ? 'Create a new account to get started'
              : 'Enter your credentials to access your account'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!hasSupabaseConfig && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <h3 className="text-sm font-medium text-yellow-800 mb-2">
                ⚠️ Supabase Configuration Required
              </h3>
              <p className="text-sm text-yellow-700 mb-2">
                To enable authentication, please:
              </p>
              <ol className="text-sm text-yellow-700 list-decimal list-inside space-y-1">
                <li>Create a Supabase project at <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="underline">supabase.com</a></li>
                <li>Update <code className="bg-yellow-100 px-1 rounded">.env.local</code> with your credentials</li>
                <li>Restart the development server</li>
              </ol>
              <div className="mt-3 pt-3 border-t border-yellow-200">
                <Button
                  onClick={onDemoMode}
                  variant="outline"
                  size="sm"
                  className="w-full bg-yellow-50 border-yellow-300 text-yellow-800 hover:bg-yellow-100"
                >
                  🚀 View Demo (No Supabase Required)
                </Button>
              </div>
            </div>
          )}

          {hasSupabaseConfig && (
            <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <h3 className="text-sm font-medium text-green-800 mb-2">
                ✅ Supabase Connected
              </h3>
              <p className="text-sm text-green-700">
                Your application is connected to Supabase and ready for authentication!
              </p>
            </div>
          )}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email
            </label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium">
              Password
            </label>
            <Input
              id="password"
              type="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          {message && (
            <div className={`text-sm p-3 rounded-md ${
              message.includes('error') || message.includes('Invalid') 
                ? 'bg-destructive/10 text-destructive' 
                : 'bg-green-50 text-green-700'
            }`}>
              {message}
            </div>
          )}

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? 'Loading...' : (isSignUp ? 'Sign Up' : 'Sign In')}
          </Button>

          <Button
            type="button"
            variant="ghost"
            className="w-full"
            onClick={() => setIsSignUp(!isSignUp)}
          >
            {isSignUp
              ? 'Already have an account? Sign In'
              : "Don't have an account? Sign Up"
            }
          </Button>
        </form>
      </CardContent>
    </Card>
    </div>
  )
}
