import React, { useState, useEffect } from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './ui/CopyButton'

/**
 * NetworkDeviceCard component for displaying device information with clipboard actions
 */
const NetworkDeviceCard = ({ 
  device, 
  onEdit, 
  onToggleStatus, 
  onDelete,
  showNotification 
}) => {
  const [decryptedCredentials, setDecryptedCredentials] = useState({
    station_password: null,
    windows_password: null,
    platform_password: null,
    passphrase: null
  })
  const [showPasswords, setShowPasswords] = useState({
    station: false,
    windows: false,
    platform: false,
    passphrase: false
  })

  // Decrypt passwords when component mounts or device changes
  useEffect(() => {
    const decryptPasswords = async () => {
      if (!device) return

      const decrypted = {}
      
      try {
        const { decryptPassword } = await import('../utils/passwordEncryption.js')

        if (device.station_password_encrypted) {
          decrypted.station_password = decryptPassword(device.station_password_encrypted)
        }
        if (device.windows_password_encrypted) {
          decrypted.windows_password = decryptPassword(device.windows_password_encrypted)
        }
        if (device.platform_password_encrypted) {
          decrypted.platform_password = decryptPassword(device.platform_password_encrypted)
        }
        if (device.passphrase_encrypted) {
          decrypted.passphrase = decryptPassword(device.passphrase_encrypted)
        }
      } catch (error) {
        console.error('Error decrypting passwords:', error)
      }

      setDecryptedCredentials(decrypted)
    }

    decryptPasswords()
  }, [device])

  const handleCopySuccess = (message) => {
    showNotification?.(message, 'success')
  }

  const handleCopyError = (message) => {
    showNotification?.(message, 'error')
  }

  const togglePasswordVisibility = (type) => {
    setShowPasswords(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  const renderCredentialField = (label, username, password, type) => {
    if (!username && !password) return null

    return (
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-700">{label}</h4>
        
        {username && (
          <div className="flex items-center justify-between bg-gray-50 rounded-md p-2">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <span className="text-xs text-gray-500 uppercase tracking-wide">User:</span>
              <span className="text-sm text-gray-900 truncate">{username}</span>
            </div>
            <CopyButton
              text={username}
              label="Username"
              isSensitive={false}
              onSuccess={handleCopySuccess}
              onError={handleCopyError}
              size="xs"
              variant="ghost"
            />
          </div>
        )}
        
        {password && (
          <div className="flex items-center justify-between bg-gray-50 rounded-md p-2">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <span className="text-xs text-gray-500 uppercase tracking-wide">Pass:</span>
              <span className="text-sm text-gray-900 font-mono">
                {showPasswords[type] ? password : '••••••••'}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <button
                type="button"
                onClick={() => togglePasswordVisibility(type)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title={showPasswords[type] ? 'Hide password' : 'Show password'}
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {showPasswords[type] ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  )}
                </svg>
              </button>
              <CopyButton
                text={password}
                label="Password"
                isSensitive={true}
                onSuccess={handleCopySuccess}
                onError={handleCopyError}
                size="xs"
                variant="ghost"
              />
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {device.station_name}
          </h3>
          <div className="flex items-center space-x-2 mt-1">
            <span className="text-sm text-gray-500">{device.device_type}</span>
            {device.host_id && (
              <>
                <span className="text-gray-300">•</span>
                <span className="text-sm text-gray-500">{device.host_id}</span>
              </>
            )}
          </div>
          {device.software_version && (
            <div className="text-xs text-gray-400 mt-1">v{device.software_version}</div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            device.is_active
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {device.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Network Information */}
      <div className="space-y-3 mb-4">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Network Configuration</h4>
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div>
              <span className="text-gray-500">IP Address:</span>
              <div className="font-mono text-gray-900">{device.ip_address || 'Not set'}</div>
            </div>
            <div>
              <span className="text-gray-500">Subnet:</span>
              <div className="font-mono text-gray-900">{device.subnet_mask || 'Not set'}</div>
            </div>
            {device.gateway && (
              <div className="col-span-2">
                <span className="text-gray-500">Gateway:</span>
                <div className="font-mono text-gray-900">{device.gateway}</div>
              </div>
            )}
          </div>
        </div>

        {device.building && (
          <div>
            <span className="text-sm text-gray-500">Building:</span>
            <div className="text-sm text-gray-900">{device.building.name}</div>
            {device.building.building_code && (
              <div className="text-xs text-gray-500">{device.building.building_code}</div>
            )}
          </div>
        )}
      </div>

      {/* Credentials Section */}
      <div className="space-y-4 mb-4">
        {renderCredentialField(
          'Station Credentials',
          device.station_username,
          decryptedCredentials.station_password,
          'station'
        )}
        
        {renderCredentialField(
          'Windows Credentials',
          device.windows_username,
          decryptedCredentials.windows_password,
          'windows'
        )}
        
        {renderCredentialField(
          'Platform Credentials',
          device.platform_username,
          decryptedCredentials.platform_password,
          'platform'
        )}

        {decryptedCredentials.passphrase && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Security Passphrase</h4>
            <div className="flex items-center justify-between bg-gray-50 rounded-md p-2">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <span className="text-xs text-gray-500 uppercase tracking-wide">Key:</span>
                <span className="text-sm text-gray-900 font-mono">
                  {showPasswords.passphrase ? decryptedCredentials.passphrase : '••••••••'}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('passphrase')}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded"
                  title={showPasswords.passphrase ? 'Hide passphrase' : 'Show passphrase'}
                >
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    {showPasswords.passphrase ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    )}
                  </svg>
                </button>
                <CopyButton
                  text={decryptedCredentials.passphrase}
                  label="Passphrase"
                  isSensitive={true}
                  onSuccess={handleCopySuccess}
                  onError={handleCopyError}
                  size="xs"
                  variant="ghost"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-2 pt-4 border-t border-gray-200">
        <button
          onClick={() => onEdit(device)}
          className="px-3 py-1.5 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
        >
          Edit
        </button>
        <button
          onClick={() => onToggleStatus(device.id, device.is_active)}
          className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
            device.is_active 
              ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50' 
              : 'text-green-600 hover:text-green-800 hover:bg-green-50'
          }`}
        >
          {device.is_active ? 'Deactivate' : 'Activate'}
        </button>
        <button
          onClick={() => onDelete(device)}
          className="px-3 py-1.5 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
        >
          Delete
        </button>
      </div>
    </div>
  )
}

export default NetworkDeviceCard
