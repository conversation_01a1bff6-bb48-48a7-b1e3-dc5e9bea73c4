import React from 'react'
import { useNetworkDeviceState } from '../hooks/useNetworkDeviceState'
import { createNetworkDeviceHandlers } from '../utils/networkDeviceHandlers'
import NetworkDeviceCard from './network/NetworkDeviceCard'
import NetworkDeviceHeader from './network/NetworkDeviceHeader'
import NetworkDeviceFilters from './network/NetworkDeviceFilters'
import NetworkDeviceTable from './network/NetworkDeviceTable'
import NetworkDevicePagination from './network/NetworkDevicePagination'
import NetworkDeviceFormModal from './network/NetworkDeviceFormModal'
import NetworkImportModal from './network/NetworkImportModal'
import NetworkDeleteConfirmModal from './network/NetworkDeleteConfirmModal'
import NetworkNotification from './network/NetworkNotification'
import NetworkEmptyState from './network/NetworkEmptyState'
import NetworkDeviceSpreadsheetView from './network/NetworkDeviceSpreadsheetView'
import NetworkDevicePaginationControls from './network/NetworkDevicePaginationControls'

const NetworkDeviceManagement = () => {
  // Use the extracted state hook
  const state = useNetworkDeviceState()
  
  // Create handlers using the factory function
  const handlers = createNetworkDeviceHandlers(state, {
    setFormData: state.setFormData,
    setFormErrors: state.setFormErrors,
    setFormWarnings: state.setFormWarnings,
    setEditingDevice: state.setEditingDevice,
    setShowForm: state.setShowForm,
    setIsSubmitting: state.setIsSubmitting,
    setShowDeleteConfirm: state.setShowDeleteConfirm,
    setShowImportModal: state.setShowImportModal,
    setImportFile: state.setImportFile,
    setImportPreview: state.setImportPreview,
    setImportErrors: state.setImportErrors,
    setIsImporting: state.setIsImporting,
    setShowPasswords: state.setShowPasswords,
    showNotification: state.showNotification
  })

  const {
    // Loading states
    devicesLoading,
    buildingsLoading,
    
    // Error states  
    devicesError,
    buildingsError,
    
    // Data
    filteredDevices,
    currentPageDevices,
    buildings,
    
    // UI State
    searchTerm,
    selectedBuilding,
    selectedDeviceType,
    viewMode,
    notification,
    
    // Form state
    showForm,
    editingDevice,
    formData,
    formErrors,
    formWarnings,
    showPasswords,
    isSubmitting,
    
    // Import state
    showImportModal,
    importPreview,
    importErrors,
    isImporting,
    
    // Delete confirmation state
    showDeleteConfirm,
    
    // Pagination
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    itemsPerPage,
    
    // Actions
    setSearchTerm,
    setSelectedBuilding,
    setSelectedDeviceType,
    setViewMode,
    setCurrentPage,
    setItemsPerPage
  } = state

  if (devicesLoading || buildingsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (devicesError || buildingsError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Data</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{devicesError || buildingsError}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <NetworkDeviceHeader
        onAddDevice={() => handlers.handleOpenForm()}
        onImport={() => handlers.handleOpenImportModal()}
        onExport={handlers.handleExportDevices}
        deviceCount={filteredDevices.length}
      />

      {/* Filters */}
      <NetworkDeviceFilters
        searchTerm={searchTerm}
        selectedBuilding={selectedBuilding}
        selectedDeviceType={selectedDeviceType}
        viewMode={viewMode}
        buildings={buildings}
        onSearchChange={setSearchTerm}
        onBuildingChange={setSelectedBuilding}
        onDeviceTypeChange={setSelectedDeviceType}
        onViewModeChange={setViewMode}
      />

      {/* Main Content */}
      {filteredDevices.length === 0 ? (
        <NetworkEmptyState onAddDevice={() => handlers.handleOpenForm()} />
      ) : (
        <>
          {viewMode === 'cards' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {currentPageDevices.map(device => (
                <NetworkDeviceCard
                  key={device.id}
                  device={device}
                  onEdit={handlers.handleOpenForm}
                  onDelete={(device) => handlers.handleSetDeleteConfirm(device)}
                  onToggleStatus={handlers.handleToggleStatus}
                />
              ))}
            </div>
          ) : viewMode === 'table' ? (
            <NetworkDeviceTable
              devices={currentPageDevices}
              onEdit={handlers.handleOpenForm}
              onDelete={(device) => handlers.handleSetDeleteConfirm(device)}
              onToggleStatus={handlers.handleToggleStatus}
              showNotification={state.showNotification}
            />
          ) : (
            <NetworkDeviceSpreadsheetView
              currentPageDevices={currentPageDevices}
              onEdit={handlers.handleOpenForm}
              onToggleStatus={handlers.handleToggleStatus}
              onDelete={(device) => handlers.handleSetDeleteConfirm(device)}
              showNotification={state.showNotification}
            />
          )}

          {/* Pagination */}
          {viewMode === 'spreadsheet' ? (
            <NetworkDevicePaginationControls
              currentPage={currentPage}
              totalPages={totalPages}
              startIndex={startIndex}
              endIndex={endIndex}
              totalItems={filteredDevices.length}
              onPageChange={setCurrentPage}
            />
          ) : (
            <NetworkDevicePagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={filteredDevices.length}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
            />
          )}
        </>
      )}

      {/* Modals */}
      <NetworkDeviceFormModal
        showForm={showForm}
        editingDevice={editingDevice}
        formData={formData}
        formErrors={formErrors}
        formWarnings={formWarnings}
        showPasswords={showPasswords}
        buildings={buildings}
        isSubmitting={isSubmitting}
        onClose={handlers.handleCloseForm}
        onSubmit={handlers.handleSubmit}
        onInputChange={handlers.handleInputChange}
        onTogglePassword={handlers.handleTogglePassword}
        getFieldStyling={handlers.getFieldStyling}
      />

      <NetworkImportModal
        showModal={showImportModal}
        onClose={handlers.handleCloseImportModal}
        importFile={state.importFile}
        importPreview={importPreview}
        importErrors={importErrors}
        isImporting={isImporting}
        onFileChange={handlers.handleImportFile}
        onConfirmImport={handlers.handleConfirmImport}
        onDownloadTemplate={handlers.handleDownloadTemplate}
      />

      <NetworkDeleteConfirmModal
        showDeleteConfirm={showDeleteConfirm}
        onClose={() => handlers.handleSetDeleteConfirm(null)}
        onConfirm={handlers.handleDelete}
      />      {/* Notification */}
      {notification && (
        <NetworkNotification
          message={notification.message}
          type={notification.type}
          onClose={() => state.setNotification(null)}
        />
      )}
    </div>
  )
}

export default NetworkDeviceManagement

      {/* Filters */}
      <NetworkDeviceFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedBuilding={selectedBuilding}
        setSelectedBuilding={setSelectedBuilding}
        selectedDeviceType={selectedDeviceType}
        setSelectedDeviceType={setSelectedDeviceType}
        buildings={buildings}
        filteredDevicesCount={filteredDevices.length}
        totalDevicesCount={devices.length}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        setCurrentPage={setCurrentPage}
      />

      {/* Devices Display */}
      {filteredDevices.length === 0 ? (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No network devices found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedBuilding || selectedDeviceType
                ? 'Try adjusting your filters or search terms.'
                : 'Get started by adding your first network device.'
              }
            </p>
            {!searchTerm && !selectedBuilding && !selectedDeviceType && (
              <button
                onClick={() => handleOpenForm()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add Network Device
              </button>
            )}
          </div>
        </div>
      ) : viewMode === 'cards' ? (
        /* Card View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDevices.map(device => (
            <NetworkDeviceCard
              key={device.id}
              device={device}
              onEdit={handleOpenForm}
              onToggleStatus={handleToggleStatus}
              onDelete={setShowDeleteConfirm}
              showNotification={showNotification}
            />
          ))}
        </div>      ) : (
        /* Enhanced Spreadsheet-Style Table View */
        <div className="bg-white shadow-lg rounded-lg overflow-hidden border-2 border-gray-300 mb-6">
          {/* Table Header with Results Count */}
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
            <div className="text-sm text-gray-700">
              Showing {startIndex + 1}-{Math.min(endIndex, filteredDevices.length)} of {filteredDevices.length} devices
            </div>
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-700">Per page:</label>
              <select
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value))
                  setCurrentPage(1) // Reset to first page when changing items per page
                }}
                className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
              </select>
            </div>
          </div>

          {/* Table Container with Fixed Height */}
          <div className="overflow-auto max-h-[600px] relative">
            <table className="min-w-full border-collapse table-fixed">
              <thead className="bg-gradient-to-b from-gray-50 to-gray-100 border-b-2 border-gray-400 sticky top-0 z-10">
                <tr>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-36 bg-gray-100">
                    <div className="truncate">Station Name</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                    <div className="truncate">Device Type</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-24 bg-gray-100">
                    <div className="truncate">Host ID</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                    <div className="truncate">IP Address</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                    <div className="truncate">Subnet Mask</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                    <div className="truncate">Gateway</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-32 bg-gray-100">
                    <div className="truncate">Building</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                    <div className="truncate">Station User</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                    <div className="truncate">Windows User</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-28 bg-gray-100">
                    <div className="truncate">Platform User</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-24 bg-gray-100">
                    <div className="truncate">Version</div>
                  </th>
                  <th className="px-2 py-3 text-left text-xs font-bold text-gray-800 uppercase tracking-wide border-r-2 border-gray-300 w-20 bg-gray-100">
                    <div className="truncate">Status</div>
                  </th>
                  <th className="px-2 py-3 text-center text-xs font-bold text-gray-800 uppercase tracking-wide w-32 bg-gray-100">
                    <div className="truncate">Actions</div>
                  </th>
                </tr>
              </thead>              <tbody className="bg-white divide-y-2 divide-gray-300">
                {currentPageDevices.map((device, index) => (
                  <tr key={device.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors border-b border-gray-300`}>
                    <td className="px-2 py-2 text-sm text-gray-900 border-r-2 border-gray-300 font-medium truncate" title={device.station_name || '-'}>
                      {device.station_name || '-'}
                    </td>
                    
                    <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300 truncate" title={device.device_type || '-'}>
                      {device.device_type || '-'}
                    </td>
                    
                    <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300 font-mono truncate" title={device.host_id || '-'}>
                      {device.host_id || '-'}
                    </td>
                    
                    <td className="px-2 py-2 text-sm text-gray-900 border-r-2 border-gray-300 font-mono">
                      <div className="flex items-center space-x-1">
                        <span className="truncate" title={device.ip_address}>{device.ip_address || '-'}</span>
                        {device.ip_address && (
                          <CopyButton
                            text={device.ip_address}
                            label="IP Address"
                            onSuccess={(msg) => showNotification(msg, 'success')}
                            onError={(msg) => showNotification(msg, 'error')}
                            size="xs"
                            variant="ghost"
                          />
                        )}
                      </div>
                    </td>
                    
                    <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300 font-mono truncate" title={device.subnet_mask || '-'}>
                      {device.subnet_mask || '-'}
                    </td>
                    
                    <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300 font-mono">
                      <div className="flex items-center space-x-1">
                        <span className="truncate" title={device.gateway}>{device.gateway || '-'}</span>
                        {device.gateway && (
                          <CopyButton
                            text={device.gateway}
                            label="Gateway"
                            onSuccess={(msg) => showNotification(msg, 'success')}
                            onError={(msg) => showNotification(msg, 'error')}
                            size="xs"
                            variant="ghost"
                          />
                        )}
                      </div>
                    </td>
                    
                    <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300 truncate">
                      <div title={`${device.building?.name || '-'}${device.building?.building_code ? ` (${device.building.building_code})` : ''}`}>
                        <div className="font-medium truncate">{device.building?.name || '-'}</div>
                        {device.building?.building_code && (
                          <div className="text-xs text-gray-500 truncate">{device.building.building_code}</div>
                        )}
                      </div>
                    </td>
                      <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300">
                      <NetworkCredentialCell
                        username={device.station_username}
                        device={device}
                        passwordField="station_password_encrypted"
                        showNotification={showNotification}
                      />
                    </td>
                      <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300">
                      <NetworkCredentialCell
                        username={device.windows_username}
                        device={device}
                        passwordField="windows_password_encrypted"
                        showNotification={showNotification}
                      />
                    </td>
                      <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300">
                      <NetworkCredentialCell
                        username={device.platform_username}
                        device={device}
                        passwordField="platform_password_encrypted"
                        showNotification={showNotification}
                      />
                    </td>
                    
                    <td className="px-2 py-2 text-sm text-gray-700 border-r-2 border-gray-300 truncate" title={device.software_version || '-'}>
                      {device.software_version || '-'}
                    </td>
                    
                    <td className="px-2 py-2 border-r-2 border-gray-300">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        device.is_active
                          ? 'bg-green-100 text-green-800 border border-green-200'
                          : 'bg-red-100 text-red-800 border border-red-200'
                      }`}>
                        {device.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>                    
                    <td className="px-2 py-2 text-center bg-gray-50">
                      <div className="flex justify-center space-x-1">
                        <button
                          onClick={() => handleOpenForm(device)}
                          className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors border border-transparent hover:border-blue-200"
                          title="Edit Device"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleToggleStatus(device.id, device.is_active)}
                          className={`p-1.5 rounded transition-colors border border-transparent ${
                            device.is_active 
                              ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-100 hover:border-orange-200' 
                              : 'text-green-600 hover:text-green-800 hover:bg-green-100 hover:border-green-200'
                          }`}
                          title={device.is_active ? 'Deactivate Device' : 'Activate Device'}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {device.is_active ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            )}
                          </svg>
                        </button>
                        <button
                          onClick={() => setShowDeleteConfirm(device)}
                          className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors border border-transparent hover:border-red-200"
                          title="Delete Device"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}              </tbody>
            </table>
          </div>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-700">
                <span>
                  Showing {startIndex + 1} to {Math.min(endIndex, filteredDevices.length)} of {filteredDevices.length} results
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Previous Page Button */}
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>

                {/* Page Numbers */}
                <div className="flex items-center space-x-1">
                  {/* First page */}
                  {currentPage > 3 && (
                    <>
                      <button
                        onClick={() => setCurrentPage(1)}
                        className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                      >
                        1
                      </button>
                      {currentPage > 4 && <span className="text-gray-500">...</span>}
                    </>
                  )}

                  {/* Previous pages */}
                  {[...Array(2)].map((_, i) => {
                    const pageNum = currentPage - 2 + i;
                    if (pageNum > 0 && pageNum < currentPage) {
                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                        >
                          {pageNum}
                        </button>
                      );
                    }
                    return null;
                  })}

                  {/* Current page */}
                  <button
                    className="px-3 py-1 rounded-md text-sm font-medium bg-blue-600 text-white border border-blue-600"
                    disabled
                  >
                    {currentPage}
                  </button>

                  {/* Next pages */}
                  {[...Array(2)].map((_, i) => {
                    const pageNum = currentPage + 1 + i;
                    if (pageNum <= totalPages) {
                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                        >
                          {pageNum}
                        </button>
                      );
                    }
                    return null;
                  })}

                  {/* Last page */}
                  {currentPage < totalPages - 2 && (
                    <>
                      {currentPage < totalPages - 3 && <span className="text-gray-500">...</span>}
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                      >
                        {totalPages}
                      </button>
                    </>
                  )}
                </div>

                {/* Next Page Button */}
                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Device Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  {editingDevice ? 'Edit Network Device' : 'Add Network Device'}
                </h2>
                <button
                  onClick={handleCloseForm}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Building *
                    </label>
                    <select
                      value={formData.building_id}
                      onChange={(e) => handleInputChange('building_id', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.building_id ? 'border-red-300' : 'border-gray-300'
                      }`}
                      required
                    >
                      <option value="">Select Building</option>
                      {buildings.map(building => (
                        <option key={building.id} value={building.id}>
                          {building.name}
                        </option>
                      ))}
                    </select>
                    {formErrors.building_id && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.building_id}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Device Type
                    </label>
                    <select
                      value={formData.device_type}
                      onChange={(e) => handleInputChange('device_type', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.device_type ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select Device Type</option>
                      {DEVICE_TYPES.map(type => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                    </select>
                    {formErrors.device_type && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.device_type}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Station Name *
                    </label>
                    <input
                      type="text"
                      value={formData.station_name}
                      onChange={(e) => handleInputChange('station_name', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.station_name ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="e.g., MAIN-ROUTER-01"
                      required
                    />
                    {formErrors.station_name && (
                      <p className="mt-1 text-sm text-red-600">{formErrors.station_name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Host ID
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.host_id}
                        onChange={(e) => handleInputChange('host_id', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          getFieldStyling('host_id').className
                        } ${formWarnings.host_id ? 'pr-10' : ''}`}
                        placeholder="e.g., RTR-001-MAIN"
                      />
                      {formWarnings.host_id && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <svg className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                        </div>
                      )}
                    </div>
                    {renderFieldMessage('host_id')}
                  </div>
                </div>

                {/* Network Configuration */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Network Configuration</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        IP Address
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          value={formData.ip_address}
                          onChange={(e) => handleInputChange('ip_address', e.target.value)}
                          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            getFieldStyling('ip_address').className
                          } ${formWarnings.ip_address ? 'pr-10' : ''}`}
                          placeholder="*************"
                        />
                        {formWarnings.ip_address && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                          </div>
                        )}
                      </div>
                      {renderFieldMessage('ip_address')}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Subnet Mask
                      </label>
                      <select
                        value={formData.subnet_mask}
                        onChange={(e) => handleInputChange('subnet_mask', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.subnet_mask ? 'border-red-300' : 'border-gray-300'
                        }`}
                      >
                        <option value="">Select Subnet Mask</option>
                        {COMMON_SUBNET_MASKS.map(mask => (
                          <option key={mask} value={mask}>
                            {mask}
                          </option>
                        ))}
                      </select>
                      {formErrors.subnet_mask && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.subnet_mask}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Gateway
                      </label>
                      <input
                        type="text"
                        value={formData.gateway}
                        onChange={(e) => handleInputChange('gateway', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.gateway ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="192.168.1.1"
                      />
                      {formErrors.gateway && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.gateway}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Software Version
                      </label>
                      <input
                        type="text"
                        value={formData.software_version}
                        onChange={(e) => handleInputChange('software_version', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="v2.1.3"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Internal DNS Server 1
                      </label>
                      <input
                        type="text"
                        value={formData.internal_dns_server_1}
                        onChange={(e) => handleInputChange('internal_dns_server_1', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.internal_dns_server_1 ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="8.8.8.8"
                      />
                      {formErrors.internal_dns_server_1 && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.internal_dns_server_1}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Internal DNS Server 2
                      </label>
                      <input
                        type="text"
                        value={formData.internal_dns_server_2}
                        onChange={(e) => handleInputChange('internal_dns_server_2', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.internal_dns_server_2 ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="8.8.4.4"
                      />
                      {formErrors.internal_dns_server_2 && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.internal_dns_server_2}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Authentication Credentials */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Authentication Credentials</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Station Username
                      </label>
                      <input
                        type="text"
                        value={formData.station_username}
                        onChange={(e) => handleInputChange('station_username', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.station_username ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="admin"
                      />
                      {formErrors.station_username && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.station_username}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Station Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPasswords.station ? 'text' : 'password'}
                          value={formData.station_password}
                          onChange={(e) => handleInputChange('station_password', e.target.value)}
                          className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            formErrors.station_password ? 'border-red-300' : 'border-gray-300'
                          }`}
                          placeholder={editingDevice ? 'Leave blank to keep current password' : 'Enter password'}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPasswords(prev => ({ ...prev, station: !prev.station }))}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            {showPasswords.station ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            )}
                          </svg>
                        </button>
                      </div>
                      {formErrors.station_password && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.station_password}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Windows Username
                      </label>
                      <input
                        type="text"
                        value={formData.windows_username}
                        onChange={(e) => handleInputChange('windows_username', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.windows_username ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="domain\\user"
                      />
                      {formErrors.windows_username && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.windows_username}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Windows Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPasswords.windows ? 'text' : 'password'}
                          value={formData.windows_password}
                          onChange={(e) => handleInputChange('windows_password', e.target.value)}
                          className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            formErrors.windows_password ? 'border-red-300' : 'border-gray-300'
                          }`}
                          placeholder={editingDevice ? 'Leave blank to keep current password' : 'Enter password'}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPasswords(prev => ({ ...prev, windows: !prev.windows }))}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            {showPasswords.windows ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            )}
                          </svg>
                        </button>
                      </div>
                      {formErrors.windows_password && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.windows_password}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Platform Username
                      </label>
                      <input
                        type="text"
                        value={formData.platform_username}
                        onChange={(e) => handleInputChange('platform_username', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.platform_username ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="platform_user"
                      />
                      {formErrors.platform_username && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.platform_username}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Platform Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPasswords.platform ? 'text' : 'password'}
                          value={formData.platform_password}
                          onChange={(e) => handleInputChange('platform_password', e.target.value)}
                          className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            formErrors.platform_password ? 'border-red-300' : 'border-gray-300'
                          }`}
                          placeholder={editingDevice ? 'Leave blank to keep current password' : 'Enter password'}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPasswords(prev => ({ ...prev, platform: !prev.platform }))}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            {showPasswords.platform ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            )}
                          </svg>
                        </button>
                      </div>
                      {formErrors.platform_password && (
                        <p className="mt-1 text-sm text-red-600">{formErrors.platform_password}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Security Passphrase */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Security Passphrase</h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Passphrase
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.passphrase ? 'text' : 'password'}
                        value={formData.passphrase}
                        onChange={(e) => handleInputChange('passphrase', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          getFieldStyling('passphrase').className
                        } ${formWarnings.passphrase || showPasswords.passphrase ? 'pr-10' : 'pr-10'}`}
                        placeholder={editingDevice ? 'Leave blank to keep current passphrase' : 'Enter security passphrase or encryption key'}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center">
                        {formWarnings.passphrase && !showPasswords.passphrase && (
                          <div className="pr-1">
                            <svg className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                          </div>
                        )}
                        <button
                          type="button"
                          onClick={() => setShowPasswords(prev => ({ ...prev, passphrase: !prev.passphrase }))}
                          className="pr-3 flex items-center"
                        >
                          <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            {showPasswords.passphrase ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            )}
                          </svg>
                        </button>
                      </div>
                    </div>
                    {renderFieldMessage('passphrase')}
                    <p className="mt-1 text-sm text-gray-500">
                      Security passphrase for encryption keys, certificates, or other secure access credentials
                    </p>
                  </div>
                </div>

                {/* Additional Information */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes
                    </label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Additional notes about this device..."
                    />
                  </div>

                  <div className="mt-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.is_active}
                        onChange={(e) => handleInputChange('is_active', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Device is active</span>
                    </label>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-6 border-t">
                  <button
                    type="button"
                    onClick={handleCloseForm}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isSubmitting ? 'Saving...' : editingDevice ? 'Update Device' : 'Create Device'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center mb-4">                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">Delete Network Device</h3>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-600">
                  Are you sure you want to delete <strong>{showDeleteConfirm.station_name}</strong>?
                  This action cannot be undone.
                </p>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDelete(showDeleteConfirm.id)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Delete Device
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Import Network Devices</h2>
                <button
                  onClick={() => {
                    setShowImportModal(false)
                    setImportFile(null)
                    setImportPreview(null)
                    setImportErrors([])
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
                {/* File Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select CSV File
                  </label>
                  <input
                    type="file"
                    accept=".csv"
                    onChange={handleImportFile}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Upload a CSV file with network device information.
                    <button
                      onClick={handleDownloadTemplate}
                      className="text-blue-600 hover:text-blue-800 ml-1"
                    >
                      Download template
                    </button>
                  </p>
                </div>

                {/* Import Errors */}
                {importErrors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <h3 className="text-red-800 font-medium mb-2">Import Errors</h3>
                    <ul className="text-red-600 text-sm space-y-1 max-h-40 overflow-y-auto">
                      {importErrors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Debug Information (Development Only) */}
                {process.env.NODE_ENV === 'development' && importPreview && importPreview.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h3 className="text-blue-800 font-medium mb-2">Debug Information</h3>
                    <div className="text-xs text-blue-700 space-y-2">
                      <div>
                        <strong>Total devices to import:</strong> {importPreview.length}
                      </div>
                      <div>
                        <strong>Sample device data:</strong>
                        <pre className="mt-1 bg-blue-100 p-2 rounded text-xs overflow-x-auto">
                          {JSON.stringify(importPreview[0], null, 2)}
                        </pre>
                      </div>
                      <div>
                        <strong>Required fields check:</strong>
                        <ul className="mt-1 space-y-1">
                          {['building_id', 'station_name'].map(field => (
                            <li key={field} className="flex items-center">
                              <span className={`w-2 h-2 rounded-full mr-2 ${
                                importPreview[0] && importPreview[0][field] ? 'bg-green-500' : 'bg-red-500'
                              }`}></span>
                              {field}: {importPreview[0] && importPreview[0][field] ? '✓' : '✗'} (Required)
                            </li>
                          ))}
                          {['device_type', 'host_id', 'ip_address', 'subnet_mask'].map(field => (
                            <li key={field} className="flex items-center">
                              <span className={`w-2 h-2 rounded-full mr-2 ${
                                importPreview[0] && importPreview[0][field] ? 'bg-blue-500' : 'bg-gray-300'
                              }`}></span>
                              {field}: {importPreview[0] && importPreview[0][field] ? '✓' : '○'} (Optional)
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {/* Import Preview */}
                {importPreview && importPreview.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Preview ({importPreview.length} devices)
                    </h3>
                    <div className="bg-gray-50 border border-gray-200 rounded-md p-4 max-h-64 overflow-y-auto">
                      <div className="space-y-2">
                        {importPreview.slice(0, 5).map((device, index) => (
                          <div key={index} className="text-sm">
                            <span className="font-medium">{device.station_name}</span>
                            <span className="text-gray-500 ml-2">
                              {device.device_type} • {device.ip_address}
                            </span>
                          </div>
                        ))}
                        {importPreview.length > 5 && (
                          <div className="text-sm text-gray-500">
                            ... and {importPreview.length - 5} more devices
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end space-x-4 pt-6 border-t">
                  <button
                    onClick={() => {
                      setShowImportModal(false)
                      setImportFile(null)
                      setImportPreview(null)
                      setImportErrors([])
                    }}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleConfirmImport}
                    disabled={!importPreview || importPreview.length === 0 || isImporting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isImporting ? 'Importing...' : `Import ${importPreview?.length || 0} Devices`}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default NetworkDeviceManagement
