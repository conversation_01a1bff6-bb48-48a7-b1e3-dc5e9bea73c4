// filepath: c:\Users\<USER>\Documents\GitHub\jsc-alarm-call-out-app\src\components\NetworkDeviceManagement.jsx
import React from 'react'
import { useNetworkDeviceState } from '../hooks/useNetworkDeviceState'
import { createNetworkDeviceHandlers } from '../utils/networkDeviceHandlers'
import NetworkDeviceCard from './network/NetworkDeviceCard'
import NetworkDeviceHeader from './network/NetworkDeviceHeader'
import NetworkDeviceFilters from './network/NetworkDeviceFilters'
import NetworkDeviceTable from './network/NetworkDeviceTable'
import NetworkDevicePagination from './network/NetworkDevicePagination'
import NetworkDeviceFormModal from './network/NetworkDeviceFormModal'
import NetworkImportModal from './network/NetworkImportModal'
import NetworkDeleteConfirmModal from './network/NetworkDeleteConfirmModal'
import NetworkNotification from './network/NetworkNotification'
import NetworkEmptyState from './network/NetworkEmptyState'
import NetworkDeviceSpreadsheet<PERSON>iew from './network/NetworkDeviceSpreadsheetView'
import NetworkDevicePaginationControls from './network/NetworkDevicePaginationControls'

const NetworkDeviceManagement = () => {
  // Use the extracted state hook
  const state = useNetworkDeviceState()
  
  // Create handlers using the factory function
  const handlers = createNetworkDeviceHandlers(state, {
    setFormData: state.setFormData,
    setFormErrors: state.setFormErrors,
    setFormWarnings: state.setFormWarnings,
    setEditingDevice: state.setEditingDevice,
    setShowForm: state.setShowForm,
    setIsSubmitting: state.setIsSubmitting,
    setShowDeleteConfirm: state.setShowDeleteConfirm,
    setShowImportModal: state.setShowImportModal,
    setImportFile: state.setImportFile,
    setImportPreview: state.setImportPreview,
    setImportErrors: state.setImportErrors,
    setIsImporting: state.setIsImporting,
    setShowPasswords: state.setShowPasswords,
    showNotification: state.showNotification
  })

  const {
    // Loading states
    devicesLoading,
    buildingsLoading,
    
    // Error states  
    devicesError,
    buildingsError,
    
    // Data
    filteredDevices,
    currentPageDevices,
    buildings,
    
    // UI State
    searchTerm,
    selectedBuilding,
    selectedDeviceType,
    viewMode,
    notification,
    
    // Form state
    showForm,
    editingDevice,
    formData,
    formErrors,
    formWarnings,
    showPasswords,
    isSubmitting,
    
    // Import state
    showImportModal,
    importPreview,
    importErrors,
    isImporting,
    
    // Delete confirmation state
    showDeleteConfirm,
    
    // Pagination
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    itemsPerPage,
    
    // Actions
    setSearchTerm,
    setSelectedBuilding,
    setSelectedDeviceType,
    setViewMode,
    setCurrentPage,
    setItemsPerPage
  } = state

  if (devicesLoading || buildingsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (devicesError || buildingsError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Data</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{devicesError || buildingsError}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <NetworkDeviceHeader
        onAddDevice={() => handlers.handleOpenForm()}
        onImport={() => handlers.handleOpenImportModal()}
        onExport={handlers.handleExportDevices}
        deviceCount={filteredDevices.length}
      />

      {/* Filters */}
      <NetworkDeviceFilters
        searchTerm={searchTerm}
        selectedBuilding={selectedBuilding}
        selectedDeviceType={selectedDeviceType}
        viewMode={viewMode}
        buildings={buildings}
        onSearchChange={setSearchTerm}
        onBuildingChange={setSelectedBuilding}
        onDeviceTypeChange={setSelectedDeviceType}
        onViewModeChange={setViewMode}
      />

      {/* Main Content */}
      {filteredDevices.length === 0 ? (
        <NetworkEmptyState onAddDevice={() => handlers.handleOpenForm()} />
      ) : (
        <>
          {viewMode === 'cards' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {currentPageDevices.map(device => (
                <NetworkDeviceCard
                  key={device.id}
                  device={device}
                  onEdit={handlers.handleOpenForm}
                  onDelete={(device) => handlers.handleSetDeleteConfirm(device)}
                  onToggleStatus={handlers.handleToggleStatus}
                />
              ))}
            </div>
          ) : viewMode === 'table' ? (
            <NetworkDeviceTable
              devices={currentPageDevices}
              onEdit={handlers.handleOpenForm}
              onDelete={(device) => handlers.handleSetDeleteConfirm(device)}
              onToggleStatus={handlers.handleToggleStatus}
              showNotification={state.showNotification}
            />
          ) : (
            <NetworkDeviceSpreadsheetView
              currentPageDevices={currentPageDevices}
              onEdit={handlers.handleOpenForm}
              onToggleStatus={handlers.handleToggleStatus}
              onDelete={(device) => handlers.handleSetDeleteConfirm(device)}
              showNotification={state.showNotification}
            />
          )}

          {/* Pagination */}
          {viewMode === 'spreadsheet' ? (
            <NetworkDevicePaginationControls
              currentPage={currentPage}
              totalPages={totalPages}
              startIndex={startIndex}
              endIndex={endIndex}
              totalItems={filteredDevices.length}
              onPageChange={setCurrentPage}
            />
          ) : (
            <NetworkDevicePagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={filteredDevices.length}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
            />
          )}
        </>
      )}

      {/* Modals */}
      <NetworkDeviceFormModal
        showForm={showForm}
        editingDevice={editingDevice}
        formData={formData}
        formErrors={formErrors}
        formWarnings={formWarnings}
        showPasswords={showPasswords}
        buildings={buildings}
        isSubmitting={isSubmitting}
        onClose={handlers.handleCloseForm}
        onSubmit={handlers.handleSubmit}
        onInputChange={handlers.handleInputChange}
        onTogglePassword={handlers.handleTogglePassword}
        getFieldStyling={handlers.getFieldStyling}
      />

      <NetworkImportModal
        showModal={showImportModal}
        onClose={handlers.handleCloseImportModal}
        importFile={state.importFile}
        importPreview={importPreview}
        importErrors={importErrors}
        isImporting={isImporting}
        onFileChange={handlers.handleImportFile}
        onConfirmImport={handlers.handleConfirmImport}
        onDownloadTemplate={handlers.handleDownloadTemplate}
      />

      <NetworkDeleteConfirmModal
        showDeleteConfirm={showDeleteConfirm}
        onClose={() => handlers.handleSetDeleteConfirm(null)}
        onConfirm={handlers.handleDelete}
      />

      {/* Notification */}
      {notification && (
        <NetworkNotification
          message={notification.message}
          type={notification.type}
          onClose={() => state.setNotification(null)}
        />
      )}
    </div>
  )
}

export default NetworkDeviceManagement
