/**
 * Sentry-integrated Error Boundary for JSC Alarm Call-Out App
 * Provides graceful error handling with automatic Sentry reporting
 */

import React from 'react'
import * as Sentry from '@sentry/react'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'

/**
 * Custom Error Boundary with Sentry integration and user-friendly UI
 */
class SentryErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    }
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Capture the error with Sentry
    Sentry.withScope((scope) => {
      // Add component context
      scope.setContext('errorBoundary', {
        componentStack: errorInfo.componentStack,
        errorBoundary: this.constructor.name,
      })
      
      // Add props context (excluding sensitive data)
      if (this.props.context) {
        scope.setContext('component', this.props.context)
      }
      
      // Set error level
      scope.setLevel('error')
      
      // Capture the error and get event ID for user feedback
      const eventId = Sentry.captureException(error)
      
      this.setState({
        error,
        errorInfo,
        eventId,
      })
    })

    // Log to console in development
    if (import.meta.env.MODE === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo)
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    })
  }

  handleReportFeedback = () => {
    if (this.state.eventId) {
      Sentry.showReportDialog({ eventId: this.state.eventId })
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom error UI for the alarm system
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="mb-4">
              <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <h1 className="text-xl font-semibold text-gray-900 mb-2">
                Something went wrong
              </h1>
              <p className="text-gray-600 mb-4">
                The alarm management system encountered an unexpected error. 
                Our team has been notified and is working to resolve the issue.
              </p>
            </div>

            {/* Error details for development */}
            {import.meta.env.MODE === 'development' && this.state.error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-left">
                <h3 className="text-sm font-medium text-red-800 mb-2">
                  Development Error Details:
                </h3>
                <pre className="text-xs text-red-700 overflow-auto max-h-32">
                  {this.state.error.toString()}
                </pre>
              </div>
            )}

            {/* Action buttons */}
            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </button>

              <button
                onClick={() => window.location.href = '/'}
                className="w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                <Home className="h-4 w-4 mr-2" />
                Return to Dashboard
              </button>

              {/* Feedback button for production */}
              {import.meta.env.MODE === 'production' && this.state.eventId && (
                <button
                  onClick={this.handleReportFeedback}
                  className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Report Issue
                </button>
              )}
            </div>

            {/* Event ID for support */}
            {this.state.eventId && (
              <div className="mt-4 p-2 bg-gray-100 rounded text-xs text-gray-600">
                Error ID: {this.state.eventId}
              </div>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Higher-order component to wrap components with Sentry error boundary
 * @param {React.Component} Component - Component to wrap
 * @param {Object} options - Error boundary options
 */
export function withSentryErrorBoundary(Component, options = {}) {
  const WrappedComponent = (props) => (
    <SentryErrorBoundary context={options.context}>
      <Component {...props} />
    </SentryErrorBoundary>
  )

  WrappedComponent.displayName = `withSentryErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Hook to manually capture errors within components
 */
export function useSentryErrorHandler() {
  return React.useCallback((error, context = {}) => {
    Sentry.withScope((scope) => {
      // Add context
      Object.keys(context).forEach(key => {
        scope.setExtra(key, context[key])
      })
      
      scope.setLevel('error')
      Sentry.captureException(error)
    })
  }, [])
}

/**
 * Sentry-wrapped Error Boundary using Sentry's built-in component
 * This provides additional Sentry-specific features
 */
export const SentryErrorBoundaryWrapper = Sentry.withErrorBoundary(
  ({ children }) => children,
  {
    fallback: ({ resetError }) => (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Application Error
          </h1>
          <p className="text-gray-600 mb-4">
            The alarm system encountered an error. Please try refreshing the page.
          </p>
          <button
            onClick={resetError}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset Application
          </button>
        </div>
      </div>
    ),
    beforeCapture: (scope, error, errorInfo) => {
      scope.setContext('errorBoundary', {
        componentStack: errorInfo.componentStack,
      })
      scope.setLevel('error')
    },
  }
)

export default SentryErrorBoundary
