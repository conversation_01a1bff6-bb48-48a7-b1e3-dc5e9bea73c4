/**
 * Sentry Integration Test Component
 * Used to verify Sentry error tracking and performance monitoring
 */

import React, { useState } from 'react'
import { Alert<PERSON>riangle, Bug, Zap, Clock, User } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { captureError, captureMessage, setSentryAlarmContext, setSentryBuildingContext, Sentry } from '../lib/sentry'
import { useSentryErrorHandler } from './SentryErrorBoundary'

export const SentryTestComponent = () => {
  const [testResults, setTestResults] = useState([])
  const sentryErrorHandler = useSentryErrorHandler()

  const addTestResult = (test, success, message) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  // Test 1: Basic Error Capture
  const testBasicError = () => {
    try {
      throw new Error('Test error from JSC Alarm Call-Out App - Sentry integration test')
    } catch (error) {
      captureError(error, {
        test_type: 'basic_error',
        component: 'SentryTestComponent',
        user_action: 'manual_test'
      })
      addTestResult('Basic Error', true, 'Error captured successfully')
    }
  }

  // Test 2: Custom Message
  const testCustomMessage = () => {
    captureMessage('Sentry integration test message from JSC Alarm Call-Out App', 'info', {
      test_type: 'custom_message',
      component: 'SentryTestComponent',
      timestamp: new Date().toISOString()
    })
    addTestResult('Custom Message', true, 'Message captured successfully')
  }

  // Test 3: Alarm Context
  const testAlarmContext = () => {
    setSentryAlarmContext({
      building_id: 'test-building-001',
      alarm_type: 'FIRE_ALARM',
      severity: 'CRITICAL'
    })
    
    captureMessage('Test alarm context - Fire alarm in test building', 'warning', {
      test_type: 'alarm_context',
      alarm_id: 'test-alarm-123'
    })
    
    addTestResult('Alarm Context', true, 'Alarm context set and message captured')
  }

  // Test 4: Building Context
  const testBuildingContext = () => {
    setSentryBuildingContext({
      building_id: 'test-building-001',
      building_type: 'COMMERCIAL_OFFICE'
    })
    
    captureMessage('Test building context - Commercial office building', 'info', {
      test_type: 'building_context',
      building_name: 'Test Office Building'
    })
    
    addTestResult('Building Context', true, 'Building context set and message captured')
  }

  // Test 5: Performance Transaction
  const testPerformanceTransaction = () => {
    // Use the newer startSpan API with proper span handling
    Sentry.startSpan({
      name: 'test-alarm-processing',
      op: 'alarm.process',
      attributes: {
        'test.type': 'performance_test',
        'component': 'SentryTestComponent'
      }
    }, (span) => {
      // Simulate some work
      setTimeout(() => {
        // Set attributes on the span (correct API)
        if (span) {
          span.setAttributes({
            'alarms_processed': 5,
            'processing_time_ms': 150,
            'test_completed': true
          })
        }

        addTestResult('Performance Transaction', true, 'Span completed and sent')
      }, 100)
    })
  }

  // Test 6: Error with Hook
  const testErrorWithHook = () => {
    const testError = new Error('Test error using Sentry hook')
    sentryErrorHandler(testError, {
      test_type: 'hook_error',
      component: 'SentryTestComponent',
      hook_used: 'useSentryErrorHandler'
    })
    addTestResult('Error Hook', true, 'Error captured using hook')
  }

  // Test 7: Breadcrumb
  const testBreadcrumb = () => {
    Sentry.addBreadcrumb({
      message: 'User clicked Sentry test button',
      category: 'user.interaction',
      level: 'info',
      data: {
        component: 'SentryTestComponent',
        action: 'test_breadcrumb'
      }
    })
    
    // Capture an error to see the breadcrumb
    captureError(new Error('Test error with breadcrumb'), {
      test_type: 'breadcrumb_test'
    })
    
    addTestResult('Breadcrumb', true, 'Breadcrumb added and error captured')
  }

  // Test 8: User Context
  const testUserContext = () => {
    Sentry.setUser({
      id: 'test-user-123',
      email: '<EMAIL>',
      username: 'test-user'
    })
    
    captureMessage('Test message with user context', 'info', {
      test_type: 'user_context'
    })
    
    addTestResult('User Context', true, 'User context set and message captured')
  }

  // Run all tests
  const runAllTests = () => {
    setTestResults([])
    
    setTimeout(() => testBasicError(), 100)
    setTimeout(() => testCustomMessage(), 200)
    setTimeout(() => testAlarmContext(), 300)
    setTimeout(() => testBuildingContext(), 400)
    setTimeout(() => testPerformanceTransaction(), 500)
    setTimeout(() => testErrorWithHook(), 600)
    setTimeout(() => testBreadcrumb(), 700)
    setTimeout(() => testUserContext(), 800)
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Sentry Integration Test
        </CardTitle>
        <CardDescription>
          Test Sentry error tracking and performance monitoring for the JSC Alarm Call-Out App.
          Check your Sentry dashboard to verify events are being captured.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Test Buttons */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <Button onClick={testBasicError} variant="outline" size="sm">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Basic Error
          </Button>
          
          <Button onClick={testCustomMessage} variant="outline" size="sm">
            <Zap className="h-4 w-4 mr-2" />
            Message
          </Button>
          
          <Button onClick={testAlarmContext} variant="outline" size="sm">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Alarm Context
          </Button>
          
          <Button onClick={testBuildingContext} variant="outline" size="sm">
            <Clock className="h-4 w-4 mr-2" />
            Building Context
          </Button>
          
          <Button onClick={testPerformanceTransaction} variant="outline" size="sm">
            <Zap className="h-4 w-4 mr-2" />
            Performance
          </Button>
          
          <Button onClick={testErrorWithHook} variant="outline" size="sm">
            <Bug className="h-4 w-4 mr-2" />
            Error Hook
          </Button>
          
          <Button onClick={testBreadcrumb} variant="outline" size="sm">
            <Clock className="h-4 w-4 mr-2" />
            Breadcrumb
          </Button>
          
          <Button onClick={testUserContext} variant="outline" size="sm">
            <User className="h-4 w-4 mr-2" />
            User Context
          </Button>
        </div>

        {/* Run All Tests Button */}
        <div className="flex justify-center">
          <Button onClick={runAllTests} className="bg-blue-600 hover:bg-blue-700">
            Run All Tests
          </Button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Test Results</h3>
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-2 rounded text-sm ${
                    result.success 
                      ? 'bg-green-50 text-green-800 border border-green-200' 
                      : 'bg-red-50 text-red-800 border border-red-200'
                  }`}
                >
                  <span className="font-medium">{result.test}</span>
                  <span className="text-xs">{result.timestamp}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Instructions:</h4>
          <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
            <li>Click individual test buttons or "Run All Tests"</li>
            <li>Check your Sentry dashboard at: <code>https://s-tier-building-automation-3b.sentry.io</code></li>
            <li>Look for events in the "alarm-call-out" project</li>
            <li>Verify that errors, messages, and performance data are being captured</li>
            <li>Check that user context and custom contexts are attached to events</li>
          </ol>
        </div>

        {/* Sentry Project Info */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 mb-2">Sentry Project Info:</h4>
          <div className="text-sm text-gray-700 space-y-1">
            <div><strong>Organization:</strong> s-tier-building-automation-3b</div>
            <div><strong>Project:</strong> alarm-call-out</div>
            <div><strong>Environment:</strong> {import.meta.env.MODE}</div>
            <div><strong>Release:</strong> {import.meta.env.VITE_SENTRY_RELEASE || 'jsc-alarm-app@dev'}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SentryTestComponent
