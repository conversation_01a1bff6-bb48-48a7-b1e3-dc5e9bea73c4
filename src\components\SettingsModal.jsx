import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Save, X } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Switch } from './ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Separator } from './ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs'
import { useUserProfile } from '../hooks/useUserProfile'

export const SettingsModal = ({ open, onOpenChange }) => {
  const { 
    preferences, 
    notificationPreferences, 
    loading, 
    updatePreferences, 
    updateNotificationPreferences,
    logActivity 
  } = useUserProfile()
  
  const [prefsData, setPrefsData] = useState({
    default_view: 'bms',
    theme: 'light',
    timezone: 'UTC',
    date_format: 'MM/DD/YYYY',
    time_format: '12h',
    temperature_unit: 'fahrenheit',
    measurement_unit: 'imperial',
    auto_refresh_interval: 30,
    show_tooltips: true,
    compact_mode: false
  })

  const [notificationData, setNotificationData] = useState({
    email_enabled: true,
    email_critical: true,
    email_high: true,
    email_medium: false,
    email_low: false,
    email_digest: true,
    email_digest_frequency: 'daily',
    in_app_enabled: true,
    in_app_sound: true,
    in_app_desktop: true,
    sms_enabled: false,
    sms_critical: false,
    sms_high: false,
    phone_number: '',
    quiet_hours_enabled: false,
    quiet_hours_start: '22:00',
    quiet_hours_end: '06:00',
    weekend_notifications: true
  })

  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState('')

  // Initialize form data when preferences load
  useEffect(() => {
    if (preferences) {
      setPrefsData({
        default_view: preferences.default_view || 'bms',
        theme: preferences.theme || 'light',
        timezone: preferences.timezone || 'UTC',
        date_format: preferences.date_format || 'MM/DD/YYYY',
        time_format: preferences.time_format || '12h',
        temperature_unit: preferences.temperature_unit || 'fahrenheit',
        measurement_unit: preferences.measurement_unit || 'imperial',
        auto_refresh_interval: preferences.auto_refresh_interval || 30,
        show_tooltips: preferences.show_tooltips ?? true,
        compact_mode: preferences.compact_mode ?? false
      })
    }
  }, [preferences])

  useEffect(() => {
    if (notificationPreferences) {
      setNotificationData({
        email_enabled: notificationPreferences.email_enabled ?? true,
        email_critical: notificationPreferences.email_critical ?? true,
        email_high: notificationPreferences.email_high ?? true,
        email_medium: notificationPreferences.email_medium ?? false,
        email_low: notificationPreferences.email_low ?? false,
        email_digest: notificationPreferences.email_digest ?? true,
        email_digest_frequency: notificationPreferences.email_digest_frequency || 'daily',
        in_app_enabled: notificationPreferences.in_app_enabled ?? true,
        in_app_sound: notificationPreferences.in_app_sound ?? true,
        in_app_desktop: notificationPreferences.in_app_desktop ?? true,
        sms_enabled: notificationPreferences.sms_enabled ?? false,
        sms_critical: notificationPreferences.sms_critical ?? false,
        sms_high: notificationPreferences.sms_high ?? false,
        phone_number: notificationPreferences.phone_number || '',
        quiet_hours_enabled: notificationPreferences.quiet_hours_enabled ?? false,
        quiet_hours_start: notificationPreferences.quiet_hours_start || '22:00',
        quiet_hours_end: notificationPreferences.quiet_hours_end || '06:00',
        weekend_notifications: notificationPreferences.weekend_notifications ?? true
      })
    }
  }, [notificationPreferences])

  const handlePreferenceChange = (field, value) => {
    setPrefsData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNotificationChange = (field, value) => {
    setNotificationData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    setMessage('')

    try {
      // Save preferences
      const { error: prefsError } = await updatePreferences(prefsData)
      if (prefsError) throw prefsError

      // Save notification preferences
      const { error: notifError } = await updateNotificationPreferences(notificationData)
      if (notifError) throw notifError

      setMessage('Settings saved successfully!')
      await logActivity('settings_update', 'User updated their settings and preferences')
      setTimeout(() => setMessage(''), 3000)
    } catch (err) {
      setMessage(`Error: ${err.message}`)
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading settings...</span>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Settings & Preferences
          </DialogTitle>
          <DialogDescription>
            Customize your dashboard, notifications, and system preferences.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="display">Display</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-4">
            <h4 className="font-medium">Dashboard Preferences</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="default_view">Default View</Label>
                <Select value={prefsData.default_view} onValueChange={(value) => handlePreferenceChange('default_view', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select default view" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bms">BMS Overview</SelectItem>
                    <SelectItem value="alarms">Alarms</SelectItem>
                    <SelectItem value="buildings">Buildings</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                    <SelectItem value="energy">Energy</SelectItem>
                    <SelectItem value="workorders">Work Orders</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="auto_refresh">Auto Refresh (seconds)</Label>
                <Select value={prefsData.auto_refresh_interval.toString()} onValueChange={(value) => handlePreferenceChange('auto_refresh_interval', parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select refresh interval" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 seconds</SelectItem>
                    <SelectItem value="30">30 seconds</SelectItem>
                    <SelectItem value="60">1 minute</SelectItem>
                    <SelectItem value="300">5 minutes</SelectItem>
                    <SelectItem value="0">Manual only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Show Tooltips</Label>
                  <p className="text-sm text-muted-foreground">Display helpful tooltips throughout the interface</p>
                </div>
                <Switch
                  checked={prefsData.show_tooltips}
                  onCheckedChange={(checked) => handlePreferenceChange('show_tooltips', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Compact Mode</Label>
                  <p className="text-sm text-muted-foreground">Use a more compact layout to show more information</p>
                </div>
                <Switch
                  checked={prefsData.compact_mode}
                  onCheckedChange={(checked) => handlePreferenceChange('compact_mode', checked)}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Notification Preferences
            </h4>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                </div>
                <Switch
                  checked={notificationData.email_enabled}
                  onCheckedChange={(checked) => handleNotificationChange('email_enabled', checked)}
                />
              </div>

              {notificationData.email_enabled && (
                <div className="ml-4 space-y-3 border-l-2 border-gray-200 pl-4">
                  <div className="flex items-center justify-between">
                    <Label>Critical Alarms</Label>
                    <Switch
                      checked={notificationData.email_critical}
                      onCheckedChange={(checked) => handleNotificationChange('email_critical', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>High Priority Alarms</Label>
                    <Switch
                      checked={notificationData.email_high}
                      onCheckedChange={(checked) => handleNotificationChange('email_high', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>Medium Priority Alarms</Label>
                    <Switch
                      checked={notificationData.email_medium}
                      onCheckedChange={(checked) => handleNotificationChange('email_medium', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>Low Priority Alarms</Label>
                    <Switch
                      checked={notificationData.email_low}
                      onCheckedChange={(checked) => handleNotificationChange('email_low', checked)}
                    />
                  </div>
                </div>
              )}

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>In-App Notifications</Label>
                  <p className="text-sm text-muted-foreground">Show notifications within the application</p>
                </div>
                <Switch
                  checked={notificationData.in_app_enabled}
                  onCheckedChange={(checked) => handleNotificationChange('in_app_enabled', checked)}
                />
              </div>

              {notificationData.in_app_enabled && (
                <div className="ml-4 space-y-3 border-l-2 border-gray-200 pl-4">
                  <div className="flex items-center justify-between">
                    <Label>Sound Alerts</Label>
                    <Switch
                      checked={notificationData.in_app_sound}
                      onCheckedChange={(checked) => handleNotificationChange('in_app_sound', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>Desktop Notifications</Label>
                    <Switch
                      checked={notificationData.in_app_desktop}
                      onCheckedChange={(checked) => handleNotificationChange('in_app_desktop', checked)}
                    />
                  </div>
                </div>
              )}

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Quiet Hours</Label>
                  <p className="text-sm text-muted-foreground">Reduce notifications during specified hours</p>
                </div>
                <Switch
                  checked={notificationData.quiet_hours_enabled}
                  onCheckedChange={(checked) => handleNotificationChange('quiet_hours_enabled', checked)}
                />
              </div>

              {notificationData.quiet_hours_enabled && (
                <div className="ml-4 grid grid-cols-2 gap-4 border-l-2 border-gray-200 pl-4">
                  <div className="space-y-2">
                    <Label>Start Time</Label>
                    <Input
                      type="time"
                      value={notificationData.quiet_hours_start}
                      onChange={(e) => handleNotificationChange('quiet_hours_start', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>End Time</Label>
                    <Input
                      type="time"
                      value={notificationData.quiet_hours_end}
                      onChange={(e) => handleNotificationChange('quiet_hours_end', e.target.value)}
                    />
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="display" className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Display Preferences
            </h4>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Theme</Label>
                <Select value={prefsData.theme} onValueChange={(value) => handlePreferenceChange('theme', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Timezone</Label>
                <Select value={prefsData.timezone} onValueChange={(value) => handlePreferenceChange('timezone', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UTC">UTC</SelectItem>
                    <SelectItem value="America/New_York">Eastern Time</SelectItem>
                    <SelectItem value="America/Chicago">Central Time</SelectItem>
                    <SelectItem value="America/Denver">Mountain Time</SelectItem>
                    <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Date Format</Label>
                <Select value={prefsData.date_format} onValueChange={(value) => handlePreferenceChange('date_format', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                    <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                    <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Time Format</Label>
                <Select value={prefsData.time_format} onValueChange={(value) => handlePreferenceChange('time_format', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select time format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="12h">12 Hour</SelectItem>
                    <SelectItem value="24h">24 Hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Temperature Unit</Label>
                <Select value={prefsData.temperature_unit} onValueChange={(value) => handlePreferenceChange('temperature_unit', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select temperature unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fahrenheit">Fahrenheit (°F)</SelectItem>
                    <SelectItem value="celsius">Celsius (°C)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Measurement Unit</Label>
                <Select value={prefsData.measurement_unit} onValueChange={(value) => handlePreferenceChange('measurement_unit', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select measurement unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="imperial">Imperial (ft, lb, etc.)</SelectItem>
                    <SelectItem value="metric">Metric (m, kg, etc.)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Security Settings
            </h4>

            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h5 className="font-medium mb-2">Password</h5>
                <p className="text-sm text-muted-foreground mb-3">
                  Change your account password to keep your account secure.
                </p>
                <Button variant="outline" size="sm">
                  Change Password
                </Button>
              </div>

              <div className="border rounded-lg p-4">
                <h5 className="font-medium mb-2">Two-Factor Authentication</h5>
                <p className="text-sm text-muted-foreground mb-3">
                  Add an extra layer of security to your account.
                </p>
                <Button variant="outline" size="sm">
                  Setup 2FA
                </Button>
              </div>

              <div className="border rounded-lg p-4">
                <h5 className="font-medium mb-2">Active Sessions</h5>
                <p className="text-sm text-muted-foreground mb-3">
                  View and manage your active login sessions.
                </p>
                <Button variant="outline" size="sm">
                  Manage Sessions
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {message && (
          <div className={`text-sm p-3 rounded-md ${
            message.includes('Error') 
              ? 'bg-destructive/10 text-destructive' 
              : 'bg-green-50 text-green-700'
          }`}>
            {message}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
