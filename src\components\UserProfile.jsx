import { useState } from 'react'
import { User, <PERSON><PERSON><PERSON>, LogOut } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu'
import { useAuth } from '../contexts/AuthContext'
import { UserInfoModal } from './UserInfoModal'
import { SettingsModal } from './SettingsModal'

export const UserProfile = () => {
  const { user, signOut } = useAuth()
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [showUserInfo, setShowUserInfo] = useState(false)
  const [showSettings, setShowSettings] = useState(false)

  // Get user display name - fallback to email or demo user
  const getUserDisplayName = () => {
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name
    }
    if (user?.email) {
      return user.email.split('@')[0] // Use part before @ as name
    }
    return 'Demo User' // Fallback for demo mode
  }

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    const name = getUserDisplayName()
    if (name === 'Demo User') {
      return 'DU'
    }
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('')
  }

  const handleLogout = async () => {
    setIsLoggingOut(true)
    try {
      await signOut()
      // The auth context will handle the state change
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoggingOut(false)
    }
  }

  const handleUserInfo = () => {
    setShowUserInfo(true)
  }

  const handleSettings = () => {
    setShowSettings(true)
  }

  return (
    <>
      <div className="flex items-center space-x-3">
      {/* User name display */}
      <div className="hidden sm:block text-right">
        <p className="text-sm font-medium text-gray-900">
          Logged in as
        </p>
        <p className="text-sm text-gray-600">
          {getUserDisplayName()}
        </p>
      </div>

      {/* Profile dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            className="flex items-center space-x-2 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 hover:ring-2 hover:ring-blue-300"
            aria-label="User menu"
          >
            <Avatar className="h-8 w-8 cursor-pointer">
              <AvatarImage 
                src={user?.user_metadata?.avatar_url} 
                alt={getUserDisplayName()}
              />
              <AvatarFallback className="bg-blue-100 text-blue-700 text-sm font-medium">
                {getUserInitials()}
              </AvatarFallback>
            </Avatar>
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent 
          className="w-56" 
          align="end" 
          forceMount
        >
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {getUserDisplayName()}
              </p>
              {user?.email && (
                <p className="text-xs leading-none text-muted-foreground">
                  {user.email}
                </p>
              )}
            </div>
          </DropdownMenuLabel>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={handleUserInfo}
            className="cursor-pointer"
          >
            <User className="mr-2 h-4 w-4" />
            <span>User Info</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={handleSettings}
            className="cursor-pointer"
          >
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="cursor-pointer text-red-600 focus:text-red-600"
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>{isLoggingOut ? 'Signing out...' : 'Logout'}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    {/* Modals */}
    <UserInfoModal
      open={showUserInfo}
      onOpenChange={setShowUserInfo}
    />
    <SettingsModal
      open={showSettings}
      onOpenChange={setShowSettings}
    />
  </>
  )
}
