import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import NetworkDevicePagination from './network/NetworkDevicePagination'
import ScrollSnapTable from './ui/ScrollSnapTable'

const WorkOrderManagement = () => {
  const { user: _user } = useAuth()
  const [workOrders, setWorkOrders] = useState([])
  const [buildings, setBuildings] = useState([])
  const [_equipment, setEquipment] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedPriority, setSelectedPriority] = useState('all')
  const [selectedBuilding, setSelectedBuilding] = useState('all')
  const [_showCreateModal, setShowCreateModal] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(25)

  const statuses = ['open', 'assigned', 'in_progress', 'completed', 'cancelled']
  const priorities = ['low', 'medium', 'high', 'critical']
  const _workTypes = ['preventive', 'corrective', 'emergency', 'inspection']

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch buildings
      const { data: buildingsData, error: buildingsError } = await supabase
        .from('buildings')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (buildingsError) throw buildingsError
      setBuildings(buildingsData || [])

      // Fetch equipment
      const { data: equipmentData, error: equipmentError } = await supabase
        .from('equipment')
        .select('id, category, equipment_type, building_id, location')
        .eq('status', 'active')
        .order('category')

      if (equipmentError) throw equipmentError
      setEquipment(equipmentData || [])

      // Fetch work orders with related data (excluding user joins for now)
      const { data: workOrdersData, error: workOrdersError } = await supabase
        .from('work_orders')
        .select(`
          *,
          building:buildings(name, building_code),
          equipment:equipment(category, equipment_type, location),
          alarm:alarm_notifications(subject, severity_id)
        `)
        .order('created_at', { ascending: false })

      if (workOrdersError) throw workOrdersError

      // Get unique user IDs from work orders
      const userIds = new Set()
      workOrdersData?.forEach(order => {
        if (order.assigned_to) userIds.add(order.assigned_to)
        if (order.requested_by) userIds.add(order.requested_by)
      })

      // Fetch user profiles for the user IDs
      const userMap = new Map()
      if (userIds.size > 0) {
        const { data: userProfiles, error: userProfilesError } = await supabase
          .from('user_profiles')
          .select('user_id, email, display_name, first_name, last_name')
          .in('user_id', Array.from(userIds))

        if (userProfilesError) {
          console.warn('Could not fetch user profiles:', userProfilesError)
        } else {
          userProfiles?.forEach(profile => {
            userMap.set(profile.user_id, profile)
          })
        }
      }

      // Merge user data with work orders
      const workOrdersWithUsers = workOrdersData?.map(order => ({
        ...order,
        assigned_user: order.assigned_to ? userMap.get(order.assigned_to) : null,
        requested_user: order.requested_by ? userMap.get(order.requested_by) : null
      })) || []

      setWorkOrders(workOrdersWithUsers)

    } catch (err) {
      console.error('Error fetching data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const filteredWorkOrders = workOrders.filter(order => {
    const statusMatch = selectedStatus === 'all' || order.status === selectedStatus
    const priorityMatch = selectedPriority === 'all' || order.priority === selectedPriority
    const buildingMatch = selectedBuilding === 'all' || order.building_id === selectedBuilding
    return statusMatch && priorityMatch && buildingMatch
  })

  // Pagination calculations
  const totalPages = Math.ceil(filteredWorkOrders.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedWorkOrders = filteredWorkOrders.slice(startIndex, endIndex)

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [selectedStatus, selectedPriority, selectedBuilding])

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return '#6b7280'
      case 'assigned': return '#3b82f6'
      case 'in_progress': return '#f59e0b'
      case 'completed': return '#22c55e'
      case 'cancelled': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'low': return '#22c55e'
      case 'medium': return '#f59e0b'
      case 'high': return '#ef4444'
      case 'critical': return '#dc2626'
      default: return '#6b7280'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open': return '📋'
      case 'assigned': return '👤'
      case 'in_progress': return '🔧'
      case 'completed': return '✅'
      case 'cancelled': return '❌'
      default: return '❓'
    }
  }

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'low': return '🟢'
      case 'medium': return '🟡'
      case 'high': return '🟠'
      case 'critical': return '🔴'
      default: return '⚪'
    }
  }

  const formatUserName = (user) => {
    if (!user) return null

    // Try display_name first, then first_name + last_name, then email
    if (user.display_name) return user.display_name
    if (user.first_name && user.last_name) return `${user.first_name} ${user.last_name}`
    if (user.first_name) return user.first_name
    return user.email
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading work orders...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
        <h3 className="text-red-800 font-medium">Error Loading Work Orders</h3>
        <p className="text-red-600 mt-1">{error}</p>
      </div>
    )
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-6 space-y-4">
      {/* Header and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        {/* Header Row */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-6">
          {/* Title and Description */}
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Work Order Management</h1>
            <p className="text-gray-600">
              Track and manage maintenance work orders
              {workOrders.length > 0 && (
                <span className="ml-2 text-sm text-gray-500">({workOrders.length} orders)</span>
              )}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex-shrink-0">
            <button
              onClick={() => setShowCreateModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Create Work Order
            </button>
          </div>
        </div>

        {/* Filters Row */}
        <div className="flex flex-col xl:flex-row xl:items-start xl:justify-between gap-4">
          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Statuses</option>
                {statuses.map(status => (
                  <option key={status} value={status}>
                    {getStatusIcon(status)} {status.replace('_', ' ').toUpperCase()}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Priorities</option>
                {priorities.map(priority => (
                  <option key={priority} value={priority}>
                    {getPriorityIcon(priority)} {priority.toUpperCase()}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Building</label>
              <select
                value={selectedBuilding}
                onChange={(e) => setSelectedBuilding(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Buildings</option>
                {buildings.map(building => (
                  <option key={building.id} value={building.id}>
                    {building.name} ({building.building_code})
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="flex gap-4 text-sm">
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-blue-500">
              <span className="text-gray-500">Total:</span>
              <span className="ml-2 font-bold text-gray-900">{workOrders.length}</span>
            </div>
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-yellow-500">
              <span className="text-gray-500">In Progress:</span>
              <span className="ml-2 font-bold text-yellow-600">
                {workOrders.filter(wo => wo.status === 'in_progress').length}
              </span>
            </div>
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-red-500">
              <span className="text-gray-500">Critical:</span>
              <span className="ml-2 font-bold text-red-600">
                {workOrders.filter(wo => wo.priority === 'critical' && wo.status !== 'completed').length}
              </span>
            </div>
            <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-green-500">
              <span className="text-gray-500">Completed:</span>
              <span className="ml-2 font-bold text-green-600">
                {workOrders.filter(wo => wo.status === 'completed').length}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Work Orders List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            Work Orders ({filteredWorkOrders.length})
          </h2>
        </div>

        {filteredWorkOrders.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No work orders found matching the selected criteria.</p>
          </div>
        ) : (
          <WorkOrderTable
            workOrders={paginatedWorkOrders}
            getStatusColor={getStatusColor}
            getPriorityColor={getPriorityColor}
            getStatusIcon={getStatusIcon}
            getPriorityIcon={getPriorityIcon}
            formatUserName={formatUserName}
          />
        )}

        {/* Pagination Controls */}
        {filteredWorkOrders.length > 0 && (
          <div className="border-t border-gray-200 bg-white">
            <NetworkDevicePagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={filteredWorkOrders.length}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
            />
          </div>
        )}
      </div>
    </div>
  )
}



// Work Order Table Component
const WorkOrderTable = ({ workOrders, getStatusColor, getPriorityColor, getStatusIcon, getPriorityIcon, formatUserName }) => {
  return (
    <ScrollSnapTable className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50 sticky top-0 z-10">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Work Order
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Building
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Priority
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Assigned To
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Created
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {workOrders.map((order, index) => (
            <tr key={order.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors`}>
              <td className="px-6 py-1.5 whitespace-nowrap">
                <div className="flex items-center">
                  <span className="mr-2">{getStatusIcon(order.status)}</span>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{order.title}</div>
                    <div className="text-sm text-gray-500">{order.work_type?.toUpperCase()}</div>
                    {order.equipment && (
                      <div className="text-xs text-gray-400">
                        {order.equipment.category} - {order.equipment.equipment_type}
                      </div>
                    )}
                  </div>
                </div>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-sm text-gray-900">
                <div>{order.building?.name || 'Unknown'}</div>
                <div className="text-gray-500 text-xs">{order.building?.building_code}</div>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap">
                <span
                  className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white"
                  style={{ backgroundColor: getPriorityColor(order.priority) }}
                >
                  {getPriorityIcon(order.priority)} {order.priority.toUpperCase()}
                </span>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap">
                <span
                  className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white"
                  style={{ backgroundColor: getStatusColor(order.status) }}
                >
                  {order.status.replace('_', ' ').toUpperCase()}
                </span>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-sm text-gray-900">
                {order.assigned_user ? (
                  <div>{formatUserName(order.assigned_user)}</div>
                ) : (
                  <span className="text-gray-400">Unassigned</span>
                )}
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-sm text-gray-900">
                <div>{new Date(order.created_at).toLocaleDateString()}</div>
                <div className="text-gray-500 text-xs">
                  {new Date(order.created_at).toLocaleTimeString()}
                </div>
              </td>

              <td className="px-6 py-1.5 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                  <button className="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 border border-blue-300 rounded hover:bg-blue-50">
                    View
                  </button>
                  <button className="text-gray-600 hover:text-gray-900 text-xs px-2 py-1 border border-gray-300 rounded hover:bg-gray-50">
                    Edit
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </ScrollSnapTable>
  )
}

export default WorkOrderManagement
