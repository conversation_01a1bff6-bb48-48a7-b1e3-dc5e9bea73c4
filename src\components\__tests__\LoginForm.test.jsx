import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LoginForm } from '../LoginForm'

// Mock the AuthContext
const mockSignIn = vi.fn()
const mockSignUp = vi.fn()

vi.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    signIn: mockSignIn,
    signUp: mockSignUp
  })
}))

// Mock hasSupabaseConfig
vi.mock('../../lib/supabase', () => ({
  hasSupabaseConfig: false
}))

describe('LoginForm', () => {
  const mockOnDemoMode = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders login form by default', () => {
    render(<LoginForm onDemoMode={mockOnDemoMode} />)

    expect(screen.getByText('Enter your credentials to access your account')).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('switches to sign up mode when toggle is clicked', async () => {
    const user = userEvent.setup()
    render(<LoginForm onDemoMode={mockOnDemoMode} />)

    const toggleButton = screen.getByText(/don't have an account\? sign up/i)
    await user.click(toggleButton)

    expect(screen.getByText('Create a new account to get started')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument()
  })

  it('calls signIn when form is submitted in sign in mode', async () => {
    const user = userEvent.setup()
    mockSignIn.mockResolvedValue({ error: null })
    
    render(<LoginForm onDemoMode={mockOnDemoMode} />)
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    await user.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })
  })

  it('calls signUp when form is submitted in sign up mode', async () => {
    const user = userEvent.setup()
    mockSignUp.mockResolvedValue({ error: null })

    render(<LoginForm onDemoMode={mockOnDemoMode} />)

    // Switch to sign up mode
    await user.click(screen.getByText(/don't have an account\? sign up/i))

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    await user.click(screen.getByRole('button', { name: /sign up/i }))

    await waitFor(() => {
      expect(mockSignUp).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })
  })

  it('displays error message when authentication fails', async () => {
    const user = userEvent.setup()
    const errorMessage = 'Invalid credentials'
    mockSignIn.mockResolvedValue({ error: { message: errorMessage } })
    
    render(<LoginForm onDemoMode={mockOnDemoMode} />)
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'wrongpassword')
    await user.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('displays success message after sign up', async () => {
    const user = userEvent.setup()
    mockSignUp.mockResolvedValue({ error: null })

    render(<LoginForm onDemoMode={mockOnDemoMode} />)

    // Switch to sign up mode
    await user.click(screen.getByText(/don't have an account\? sign up/i))

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    await user.click(screen.getByRole('button', { name: /sign up/i }))

    await waitFor(() => {
      expect(screen.getByText(/check your email for the confirmation link/i)).toBeInTheDocument()
    })
  })

  it('calls onDemoMode when demo button is clicked', async () => {
    const user = userEvent.setup()
    render(<LoginForm onDemoMode={mockOnDemoMode} />)

    const demoButton = screen.getByText(/🚀 view demo \(no supabase required\)/i)
    await user.click(demoButton)

    expect(mockOnDemoMode).toHaveBeenCalled()
  })

  it('disables submit button while loading', async () => {
    const user = userEvent.setup()
    // Mock a slow response
    mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ error: null }), 100)))
    
    render(<LoginForm onDemoMode={mockOnDemoMode} />)
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')
    
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)
    
    // Button should be disabled while loading
    expect(submitButton).toBeDisabled()
    expect(screen.getByText(/loading/i)).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<LoginForm onDemoMode={mockOnDemoMode} />)
    
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)
    
    // Form should not submit without required fields
    expect(mockSignIn).not.toHaveBeenCalled()
  })
})
