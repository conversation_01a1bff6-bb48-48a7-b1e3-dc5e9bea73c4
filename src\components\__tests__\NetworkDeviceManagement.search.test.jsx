import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import NetworkDeviceManagement from '../NetworkDeviceManagement'
import { useNetworkDevices } from '../../hooks/useNetworkDevices'
import { useBuildings } from '../../hooks/useBuildings'
import { useAuth } from '../../contexts/AuthContext'

// Mock the hooks
vi.mock('../../hooks/useNetworkDevices')
vi.mock('../../hooks/useBuildings')
vi.mock('../../contexts/AuthContext')

// Mock the child components to avoid complex rendering
vi.mock('../NetworkDeviceCard', () => ({
  default: ({ device }) => <div data-testid={`device-card-${device.id}`}>{device.station_name}</div>
}))

vi.mock('../ui/CopyButton', () => ({
  default: ({ text }) => <button>{text}</button>
}))

describe('NetworkDeviceManagement - Search Functionality', () => {
  const mockDevices = [
    {
      id: '1',
      station_name: 'Router-01',
      host_id: 'RTR-001',
      ip_address: '***********',
      device_type: 'BACnet router',
      building_id: 'building-1',
      building: { name: 'Main Office', id: 'building-1' },
      is_active: true
    },
    {
      id: '2',
      station_name: null, // Test null station_name
      host_id: 'SW-002',
      ip_address: '***********',
      device_type: 'Switch',
      building_id: 'building-1',
      building: { name: 'Main Office', id: 'building-1' },
      is_active: true
    },
    {
      id: '3',
      station_name: 'Access-Point-03',
      host_id: null, // Test null host_id
      ip_address: '***********',
      device_type: 'Access Point',
      building_id: 'building-1',
      building: { name: 'Main Office', id: 'building-1' },
      is_active: true
    },
    {
      id: '4',
      station_name: 'Server-04',
      host_id: 'SRV-004',
      ip_address: null, // Test null ip_address
      device_type: null, // Test null device_type
      building_id: 'building-1',
      building: null, // Test null building
      is_active: true
    }
  ]

  const mockBuildings = [
    { id: 'building-1', name: 'Main Office', building_code: 'MO', is_active: true }
  ]

  beforeEach(() => {
    // Mock useAuth
    useAuth.mockReturnValue({
      user: { id: 'user-1', email: '<EMAIL>' }
    })

    // Mock useBuildings
    useBuildings.mockReturnValue({
      buildings: mockBuildings,
      loading: false,
      error: null
    })

    // Mock useNetworkDevices
    useNetworkDevices.mockReturnValue({
      devices: mockDevices,
      loading: false,
      error: null,
      createDevice: vi.fn(),
      updateDevice: vi.fn(),
      deleteDevice: vi.fn(),
      toggleDeviceStatus: vi.fn(),
      isHostIdUnique: vi.fn(),
      isIpAddressUnique: vi.fn(),
      validateDeviceBatch: vi.fn(),
      searchDevices: vi.fn()
    })
  })

  it('should handle search input without crashing when device properties are null', async () => {
    render(<NetworkDeviceManagement />)

    // Find the search input
    const searchInput = screen.getByPlaceholderText('Search devices...')
    expect(searchInput).toBeInTheDocument()

    // Type in the search input - this should not crash
    fireEvent.change(searchInput, { target: { value: 'router' } })

    // Wait for any state updates
    await waitFor(() => {
      expect(searchInput.value).toBe('router')
    })

    // The component should still be rendered without errors
    expect(screen.getByText('Network Device Management')).toBeInTheDocument()
  })

  it('should filter devices correctly when searching with null values present', async () => {
    render(<NetworkDeviceManagement />)

    const searchInput = screen.getByPlaceholderText('Search devices...')

    // Search for "router" - should match Router-01 by station_name
    fireEvent.change(searchInput, { target: { value: 'router' } })
    
    await waitFor(() => {
      // Should show Router-01 in the table
      expect(screen.getByText('Router-01')).toBeInTheDocument()
    })

    // Search for "SW-002" - should match device with null station_name by host_id
    fireEvent.change(searchInput, { target: { value: 'SW-002' } })
    
    await waitFor(() => {
      // Should show the device with SW-002 host_id
      expect(screen.getByText('SW-002')).toBeInTheDocument()
    })

    // Search for "***********" - should match by IP address
    fireEvent.change(searchInput, { target: { value: '***********' } })
    
    await waitFor(() => {
      // Should show Access-Point-03
      expect(screen.getByText('Access-Point-03')).toBeInTheDocument()
    })
  })

  it('should not crash when searching with empty search term', async () => {
    render(<NetworkDeviceManagement />)

    const searchInput = screen.getByPlaceholderText('Search devices...')

    // Clear search input
    fireEvent.change(searchInput, { target: { value: '' } })

    await waitFor(() => {
      expect(searchInput.value).toBe('')
    })

    // Should show all devices when search is empty
    expect(screen.getByText('Router-01')).toBeInTheDocument()
    expect(screen.getByText('Access-Point-03')).toBeInTheDocument()
    expect(screen.getByText('Server-04')).toBeInTheDocument()
  })
})
