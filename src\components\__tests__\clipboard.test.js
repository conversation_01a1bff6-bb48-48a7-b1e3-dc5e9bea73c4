/**
 * Tests for clipboard functionality
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { copyToClipboard, secureCopyToClipboard } from '../../utils/clipboard'

// Mock the clipboard API
const mockClipboard = {
  writeText: vi.fn()
}

// Mock navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: mockClipboard,
  writable: true
})

// Mock window.isSecureContext
Object.defineProperty(window, 'isSecureContext', {
  value: true,
  writable: true
})

describe('Clipboard Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('copyToClipboard', () => {
    it('should copy text to clipboard successfully', async () => {
      mockClipboard.writeText.mockResolvedValue()
      
      const result = await copyToClipboard('test-username', 'Username')
      
      expect(mockClipboard.writeText).toHaveBeenCalledWith('test-username')
      expect(result.success).toBe(true)
      expect(result.message).toBe('Username copied to clipboard')
    })

    it('should handle empty text', async () => {
      const result = await copyToClipboard('', 'Username')
      
      expect(mockClipboard.writeText).not.toHaveBeenCalled()
      expect(result.success).toBe(false)
      expect(result.message).toBe('No username to copy')
    })

    it('should handle null text', async () => {
      const result = await copyToClipboard(null, 'Password')
      
      expect(mockClipboard.writeText).not.toHaveBeenCalled()
      expect(result.success).toBe(false)
      expect(result.message).toBe('No password to copy')
    })

    it('should handle clipboard API errors', async () => {
      mockClipboard.writeText.mockRejectedValue(new Error('Clipboard error'))
      
      const result = await copyToClipboard('test-text', 'Text')
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('Failed to copy text')
    })
  })

  describe('secureCopyToClipboard', () => {
    it('should copy sensitive data without logging content', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      mockClipboard.writeText.mockResolvedValue()
      
      const result = await secureCopyToClipboard('secret-password', 'Password', true)
      
      expect(mockClipboard.writeText).toHaveBeenCalledWith('secret-password')
      expect(result.success).toBe(true)
      expect(result.message).toBe('Password copied to clipboard')
      expect(consoleSpy).toHaveBeenCalledWith('Sensitive password copied to clipboard')
      
      consoleSpy.mockRestore()
    })

    it('should copy non-sensitive data with partial logging', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      mockClipboard.writeText.mockResolvedValue()
      
      const result = await secureCopyToClipboard('admin-username', 'Username', false)
      
      expect(mockClipboard.writeText).toHaveBeenCalledWith('admin-username')
      expect(result.success).toBe(true)
      expect(result.message).toBe('Username copied to clipboard')
      expect(consoleSpy).toHaveBeenCalledWith('Username copied to clipboard:', 'admin-user...')
      
      consoleSpy.mockRestore()
    })
  })

  describe('Fallback behavior', () => {
    beforeEach(() => {
      // Mock non-secure context
      Object.defineProperty(window, 'isSecureContext', {
        value: false,
        writable: true
      })
      
      // Mock document.execCommand
      document.execCommand = vi.fn().mockReturnValue(true)
      document.createElement = vi.fn().mockReturnValue({
        value: '',
        style: {},
        focus: vi.fn(),
        select: vi.fn()
      })
      document.body.appendChild = vi.fn()
      document.body.removeChild = vi.fn()
    })

    it('should use fallback method when clipboard API is not available', async () => {
      navigator.clipboard = undefined
      
      const result = await copyToClipboard('fallback-test', 'Text')
      
      expect(document.execCommand).toHaveBeenCalledWith('copy')
      expect(result.success).toBe(true)
      expect(result.message).toBe('Text copied to clipboard')
    })
  })
})
