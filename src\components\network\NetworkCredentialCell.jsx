import React, { useState, useEffect } from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../ui/CopyButton'

/**
 * NetworkCredentialCell component for displaying username/password credentials in table cells
 * Used in spreadsheet-style table view with compact display and copy functionality
 */
const NetworkCredentialCell = ({ 
  username, 
  device, 
  passwordField, 
  showNotification 
}) => {
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  // Decrypt password when component mounts or device changes
  useEffect(() => {
    const decryptPassword = async () => {
      if (!device || !device[passwordField]) {
        setPassword('')
        return
      }

      try {
        const { decryptPassword: decrypt } = await import('../../utils/passwordEncryption.js')
        const decrypted = decrypt(device[passwordField])
        setPassword(decrypted)
      } catch (error) {
        console.error('Error decrypting password:', error)
        setPassword('')
      }
    }

    decryptPassword()
  }, [device, passwordField])

  const handleCopySuccess = (message) => {
    showNotification(message, 'success')
  }

  const handleCopyError = (message) => {
    showNotification(message, 'error')
  }

  if (!username && !password) {
    return <div className="text-gray-400 text-xs">-</div>
  }

  return (
    <div className="space-y-0.5 min-h-[1.75rem]">
      {username && (
        <div className="flex items-center space-x-1">
          <span className="text-xs text-gray-700 truncate max-w-[70px] font-mono" title={username}>
            {username}
          </span>
          <CopyButton
            text={username}
            label="Username"
            onSuccess={handleCopySuccess}
            onError={handleCopyError}
            size="xs"
            variant="ghost"
          />
        </div>
      )}
      {password && (
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setShowPassword(!showPassword)}
            className="text-xs text-gray-500 hover:text-gray-700 font-mono truncate max-w-[70px] bg-gray-100 px-1 py-0.5 rounded"
            title={showPassword ? password : 'Click to reveal password'}
          >
            {showPassword ? password : '••••••••'}
          </button>
          <CopyButton
            text={password}
            label="Password"
            isSensitive={true}
            onSuccess={handleCopySuccess}
            onError={handleCopyError}
            size="xs"
            variant="ghost"
          />
        </div>
      )}
    </div>
  )
}

export default NetworkCredentialCell
