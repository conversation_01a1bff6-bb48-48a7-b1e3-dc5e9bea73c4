import React from 'react'
import { DEVICE_TYPES, COMMON_SUBNET_MASKS } from '../../lib/networkDeviceValidation'

/**
 * NetworkDeviceForm component for creating and editing network devices
 */
const NetworkDeviceForm = ({
  formData,
  formErrors,
  formWarnings,
  buildings,
  editingDevice,
  isSubmitting,
  showPasswords,
  onInputChange,
  onTogglePassword,
  onSubmit,
  onClose,
  getFieldStyling,
  renderFieldMessage
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {editingDevice ? 'Edit Network Device' : 'Add Network Device'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={onSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Building *
                </label>
                <select
                  value={formData.building_id}
                  onChange={(e) => onInputChange('building_id', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    formErrors.building_id ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required
                >
                  <option value="">Select Building</option>
                  {buildings.map(building => (
                    <option key={building.id} value={building.id}>
                      {building.name}
                    </option>
                  ))}
                </select>
                {formErrors.building_id && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.building_id}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Device Type
                </label>
                <select
                  value={formData.device_type}
                  onChange={(e) => onInputChange('device_type', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    formErrors.device_type ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select Device Type</option>
                  {DEVICE_TYPES.map(type => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
                {formErrors.device_type && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.device_type}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Station Name *
                </label>
                <input
                  type="text"
                  value={formData.station_name}
                  onChange={(e) => onInputChange('station_name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    formErrors.station_name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="e.g., MAIN-ROUTER-01"
                  required
                />
                {formErrors.station_name && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.station_name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Host ID
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.host_id}
                    onChange={(e) => onInputChange('host_id', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      getFieldStyling('host_id').className
                    } ${formWarnings.host_id ? 'pr-10' : ''}`}
                    placeholder="e.g., RTR-001-MAIN"
                  />
                  {formWarnings.host_id && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <svg className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  )}
                </div>
                {renderFieldMessage('host_id')}
              </div>
            </div>

            {/* Network Configuration */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Network Configuration</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    IP Address
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.ip_address}
                      onChange={(e) => onInputChange('ip_address', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        getFieldStyling('ip_address').className
                      } ${formWarnings.ip_address ? 'pr-10' : ''}`}
                      placeholder="*************"
                    />
                    {formWarnings.ip_address && (
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  {renderFieldMessage('ip_address')}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subnet Mask
                  </label>
                  <select
                    value={formData.subnet_mask}
                    onChange={(e) => onInputChange('subnet_mask', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.subnet_mask ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Subnet Mask</option>
                    {COMMON_SUBNET_MASKS.map(mask => (
                      <option key={mask} value={mask}>
                        {mask}
                      </option>
                    ))}
                  </select>
                  {formErrors.subnet_mask && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.subnet_mask}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Gateway
                  </label>
                  <input
                    type="text"
                    value={formData.gateway}
                    onChange={(e) => onInputChange('gateway', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.gateway ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="192.168.1.1"
                  />
                  {formErrors.gateway && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.gateway}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Software Version
                  </label>
                  <input
                    type="text"
                    value={formData.software_version}
                    onChange={(e) => onInputChange('software_version', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="v2.1.3"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Internal DNS Server 1
                  </label>
                  <input
                    type="text"
                    value={formData.internal_dns_server_1}
                    onChange={(e) => onInputChange('internal_dns_server_1', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.internal_dns_server_1 ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="8.8.8.8"
                  />
                  {formErrors.internal_dns_server_1 && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.internal_dns_server_1}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Internal DNS Server 2
                  </label>
                  <input
                    type="text"
                    value={formData.internal_dns_server_2}
                    onChange={(e) => onInputChange('internal_dns_server_2', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.internal_dns_server_2 ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="8.8.4.4"
                  />
                  {formErrors.internal_dns_server_2 && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.internal_dns_server_2}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Authentication Credentials */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Authentication Credentials</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Station Username
                  </label>
                  <input
                    type="text"
                    value={formData.station_username}
                    onChange={(e) => onInputChange('station_username', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.station_username ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="admin"
                  />
                  {formErrors.station_username && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.station_username}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Station Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.station ? 'text' : 'password'}
                      value={formData.station_password}
                      onChange={(e) => onInputChange('station_password', e.target.value)}
                      className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.station_password ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder={editingDevice ? 'Leave blank to keep current password' : 'Enter password'}
                    />
                    <button
                      type="button"
                      onClick={() => onTogglePassword('station')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        {showPasswords.station ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        )}
                      </svg>
                    </button>
                  </div>
                  {formErrors.station_password && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.station_password}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Windows Username
                  </label>
                  <input
                    type="text"
                    value={formData.windows_username}
                    onChange={(e) => onInputChange('windows_username', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.windows_username ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="domain\\user"
                  />
                  {formErrors.windows_username && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.windows_username}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Windows Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.windows ? 'text' : 'password'}
                      value={formData.windows_password}
                      onChange={(e) => onInputChange('windows_password', e.target.value)}
                      className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.windows_password ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder={editingDevice ? 'Leave blank to keep current password' : 'Enter password'}
                    />
                    <button
                      type="button"
                      onClick={() => onTogglePassword('windows')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        {showPasswords.windows ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        )}
                      </svg>
                    </button>
                  </div>
                  {formErrors.windows_password && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.windows_password}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Platform Username
                  </label>
                  <input
                    type="text"
                    value={formData.platform_username}
                    onChange={(e) => onInputChange('platform_username', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.platform_username ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="platform_user"
                  />
                  {formErrors.platform_username && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.platform_username}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Platform Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.platform ? 'text' : 'password'}
                      value={formData.platform_password}
                      onChange={(e) => onInputChange('platform_password', e.target.value)}
                      className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        formErrors.platform_password ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder={editingDevice ? 'Leave blank to keep current password' : 'Enter password'}
                    />
                    <button
                      type="button"
                      onClick={() => onTogglePassword('platform')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        {showPasswords.platform ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        )}
                      </svg>
                    </button>
                  </div>
                  {formErrors.platform_password && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.platform_password}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Security Passphrase */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Security Passphrase</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Passphrase
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.passphrase ? 'text' : 'password'}
                    value={formData.passphrase}
                    onChange={(e) => onInputChange('passphrase', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      getFieldStyling('passphrase').className
                    } ${formWarnings.passphrase || showPasswords.passphrase ? 'pr-10' : 'pr-10'}`}
                    placeholder={editingDevice ? 'Leave blank to keep current passphrase' : 'Enter security passphrase or encryption key'}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center">
                    {formWarnings.passphrase && !showPasswords.passphrase && (
                      <div className="pr-1">
                        <svg className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={() => onTogglePassword('passphrase')}
                      className="pr-3 flex items-center"
                    >
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        {showPasswords.passphrase ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        )}
                      </svg>
                    </button>
                  </div>
                </div>
                {renderFieldMessage('passphrase')}
                <p className="mt-1 text-sm text-gray-500">
                  Security passphrase for encryption keys, certificates, or other secure access credentials
                </p>
              </div>
            </div>

            {/* Additional Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => onInputChange('notes', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Additional notes about this device..."
                />
              </div>

              <div className="mt-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => onInputChange('is_active', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Device is active</span>
                </label>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting 
                  ? (editingDevice ? 'Updating...' : 'Creating...') 
                  : (editingDevice ? 'Update Device' : 'Create Device')
                }
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default NetworkDeviceForm
