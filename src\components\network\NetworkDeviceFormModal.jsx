import React from 'react'
import { DEVICE_TYPES, COMMON_SUBNET_MASKS } from '../../lib/networkDeviceValidation'

const NetworkDeviceFormModal = ({
  showForm,
  editingDevice,
  formData,
  formErrors,
  formWarnings,
  showPasswords,
  buildings,
  isSubmitting,
  onClose,
  onSubmit,
  onInputChange,
  onTogglePassword,
  getFieldStyling
}) => {
  if (!showForm) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {editingDevice ? 'Edit Network Device' : 'Add New Network Device'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={onSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Building */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Building *
                  </label>
                  <select
                    value={formData.building_id}
                    onChange={(e) => onInputChange('building_id', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      getFieldStyling('building_id').className
                    }`}
                    required
                  >
                    <option value="">Select a building...</option>
                    {buildings.map(building => (
                      <option key={building.id} value={building.id}>
                        {building.name} {building.building_code && `(${building.building_code})`}
                      </option>
                    ))}
                  </select>
                  {formErrors.building_id && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.building_id}</p>
                  )}
                </div>

                {/* Station Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Station Name *
                  </label>
                  <input
                    type="text"
                    value={formData.station_name}
                    onChange={(e) => onInputChange('station_name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      getFieldStyling('station_name').className
                    }`}
                    placeholder="Enter station name"
                    required
                  />
                  {formErrors.station_name && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.station_name}</p>
                  )}
                  {formWarnings.station_name && (
                    <p className="mt-1 text-sm text-yellow-600">{formWarnings.station_name}</p>
                  )}
                </div>

                {/* Device Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Device Type
                  </label>
                  <select
                    value={formData.device_type}
                    onChange={(e) => onInputChange('device_type', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      getFieldStyling('device_type').className
                    }`}
                  >
                    <option value="">Select device type...</option>
                    {DEVICE_TYPES.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                  {formErrors.device_type && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.device_type}</p>
                  )}
                </div>

                {/* Host ID */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Host ID
                  </label>
                  <input
                    type="text"
                    value={formData.host_id}
                    onChange={(e) => onInputChange('host_id', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono ${
                      getFieldStyling('host_id').className
                    }`}
                    placeholder="Enter host ID"
                  />
                  {formErrors.host_id && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.host_id}</p>
                  )}
                  {formWarnings.host_id && (
                    <p className="mt-1 text-sm text-yellow-600">{formWarnings.host_id}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Network Configuration */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Network Configuration</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* IP Address */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    IP Address
                  </label>
                  <input
                    type="text"
                    value={formData.ip_address}
                    onChange={(e) => onInputChange('ip_address', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono ${
                      getFieldStyling('ip_address').className
                    }`}
                    placeholder="*************"
                  />
                  {formErrors.ip_address && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.ip_address}</p>
                  )}
                  {formWarnings.ip_address && (
                    <p className="mt-1 text-sm text-yellow-600">{formWarnings.ip_address}</p>
                  )}
                </div>

                {/* Subnet Mask */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subnet Mask
                  </label>
                  <select
                    value={formData.subnet_mask}
                    onChange={(e) => onInputChange('subnet_mask', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                  >
                    {COMMON_SUBNET_MASKS.map(mask => (
                      <option key={mask} value={mask}>{mask}</option>
                    ))}
                  </select>
                </div>

                {/* Gateway */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Gateway
                  </label>
                  <input
                    type="text"
                    value={formData.gateway}
                    onChange={(e) => onInputChange('gateway', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono ${
                      getFieldStyling('gateway').className
                    }`}
                    placeholder="192.168.1.1"
                  />
                  {formErrors.gateway && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.gateway}</p>
                  )}
                </div>

                {/* DNS Servers */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Internal DNS Server 1
                  </label>
                  <input
                    type="text"
                    value={formData.internal_dns_server_1}
                    onChange={(e) => onInputChange('internal_dns_server_1', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono ${
                      getFieldStyling('internal_dns_server_1').className
                    }`}
                    placeholder="192.168.1.10"
                  />
                  {formErrors.internal_dns_server_1 && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.internal_dns_server_1}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Internal DNS Server 2
                  </label>
                  <input
                    type="text"
                    value={formData.internal_dns_server_2}
                    onChange={(e) => onInputChange('internal_dns_server_2', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono ${
                      getFieldStyling('internal_dns_server_2').className
                    }`}
                    placeholder="192.168.1.11"
                  />
                  {formErrors.internal_dns_server_2 && (
                    <p className="mt-1 text-sm text-red-600">{formErrors.internal_dns_server_2}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Credentials */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Credentials</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Station Credentials */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-800">Station Credentials</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username
                    </label>
                    <input
                      type="text"
                      value={formData.station_username}
                      onChange={(e) => onInputChange('station_username', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter username"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.station ? 'text' : 'password'}
                        value={formData.station_password}
                        onChange={(e) => onInputChange('station_password', e.target.value)}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter password"
                      />
                      <button
                        type="button"
                        onClick={() => onTogglePassword('station')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.station ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Windows Credentials */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-800">Windows Credentials</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username
                    </label>
                    <input
                      type="text"
                      value={formData.windows_username}
                      onChange={(e) => onInputChange('windows_username', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter username"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.windows ? 'text' : 'password'}
                        value={formData.windows_password}
                        onChange={(e) => onInputChange('windows_password', e.target.value)}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter password"
                      />
                      <button
                        type="button"
                        onClick={() => onTogglePassword('windows')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.windows ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Platform Credentials */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-800">Platform Credentials</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username
                    </label>
                    <input
                      type="text"
                      value={formData.platform_username}
                      onChange={(e) => onInputChange('platform_username', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter username"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.platform ? 'text' : 'password'}
                        value={formData.platform_password}
                        onChange={(e) => onInputChange('platform_password', e.target.value)}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter password"
                      />
                      <button
                        type="button"
                        onClick={() => onTogglePassword('platform')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.platform ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Passphrase */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-800">Security</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Passphrase
                    </label>
                    <div className="relative">
                      <input
                        type={showPasswords.passphrase ? 'text' : 'password'}
                        value={formData.passphrase}
                        onChange={(e) => onInputChange('passphrase', e.target.value)}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter passphrase"
                      />
                      <button
                        type="button"
                        onClick={() => onTogglePassword('passphrase')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.passphrase ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
              
              <div className="space-y-4">
                {/* Software Version */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Software Version
                  </label>
                  <input
                    type="text"
                    value={formData.software_version}
                    onChange={(e) => onInputChange('software_version', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter software version"
                  />
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => onInputChange('notes', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Additional notes about this device..."
                  />
                </div>

                {/* Active Status */}
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.is_active}
                      onChange={(e) => onInputChange('is_active', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">Device is active</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? 'Saving...' : editingDevice ? 'Update Device' : 'Create Device'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default NetworkDeviceFormModal
