import React from 'react'

/**
 * NetworkDeviceHeader component for page title and main action buttons
 */
const NetworkDeviceHeader = ({
  onAddDevice,
  onImport,
  onExport,
  deviceCount
}) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Network Device Management</h1>
          <p className="text-gray-600">
            Manage LAN devices and network configuration for buildings
            {deviceCount > 0 && (
              <span className="ml-2 text-sm text-gray-500">({deviceCount} devices)</span>
            )}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <div className="flex gap-2">
            <button
              onClick={() => {
                // Create and download template
                const headers = ['station_name', 'device_type', 'ip_address', 'subnet_mask', 'host_id', 'gateway', 'dns_primary', 'dns_secondary', 'building_id', 'station_username', 'station_password', 'windows_username', 'windows_password', 'platform_username', 'platform_password', 'security_passphrase', 'software_version', 'notes']
                const csvContent = headers.join(',') + '\n'
                const blob = new Blob([csvContent], { type: 'text/csv' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = 'network_devices_template.csv'
                a.click()
                window.URL.revokeObjectURL(url)
              }}
              className="px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors text-sm border border-gray-300"
            >
              Download Template
            </button>
            <button
              onClick={onImport}
              className="px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors text-sm border border-gray-300"
            >
              Import CSV
            </button>
            <button
              onClick={onExport}
              disabled={deviceCount === 0}
              className="px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm border border-gray-300"
            >
              Export CSV
            </button>
          </div>
          <button
            onClick={onAddDevice}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Add Network Device
          </button>
        </div>
      </div>
    </div>
  )
}

export default NetworkDeviceHeader
