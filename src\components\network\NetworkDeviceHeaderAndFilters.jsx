import React from 'react'
import { DEVICE_TYPES } from '../../lib/networkDeviceValidation'
import NetworkDeviceSummaryCards from './NetworkDeviceSummaryCards'

/**
 * NetworkDeviceHeaderAndFilters component for unified page header with filters
 */
const NetworkDeviceHeaderAndFilters = ({
  onAddDevice,
  onImport,
  onExport,
  deviceCount,
  devices,
  searchTerm,
  selectedBuilding,
  selectedDeviceType,
  buildings,
  onSearchChange,
  onBuildingChange,
  onDeviceTypeChange
}) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* Header Row */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-6">
        {/* Title and Description */}
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Network Device Management</h1>
          <p className="text-gray-600">
            Manage LAN devices and network configuration for buildings
            {deviceCount > 0 && (
              <span className="ml-2 text-sm text-gray-500">({deviceCount} devices)</span>
            )}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex-shrink-0 flex flex-col sm:flex-row gap-2">
          <div className="flex gap-2">
            <button
              onClick={() => {
                // Create and download template
                const headers = ['station_name', 'device_type', 'ip_address', 'subnet_mask', 'host_id', 'gateway', 'dns_primary', 'dns_secondary', 'building_id', 'station_username', 'station_password', 'windows_username', 'windows_password', 'platform_username', 'platform_password', 'security_passphrase', 'software_version', 'notes']
                const csvContent = headers.join(',') + '\n'
                const blob = new Blob([csvContent], { type: 'text/csv' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = 'network_devices_template.csv'
                a.click()
                window.URL.revokeObjectURL(url)
              }}
              className="px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors text-sm border border-gray-300"
            >
              Download Template
            </button>
            <button
              onClick={onImport}
              className="px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors text-sm border border-gray-300"
            >
              Import CSV
            </button>
            <button
              onClick={onExport}
              disabled={deviceCount === 0}
              className="px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm border border-gray-300"
            >
              Export CSV
            </button>
          </div>
          <button
            onClick={onAddDevice}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Add Device
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <NetworkDeviceSummaryCards devices={devices || []} />

      {/* Filters Row */}
      <div className="flex flex-wrap gap-4">
        {/* Search */}
        <div className="w-80">
          <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <div className="relative">
            <input
              type="text"
              placeholder="Search devices by name, IP, or type..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Building Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Building</label>
          <select
            value={selectedBuilding}
            onChange={(e) => onBuildingChange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Buildings</option>
            {buildings.map(building => (
              <option key={building.id} value={building.id}>
                {building.name} {building.building_code ? `(${building.building_code})` : ''}
              </option>
            ))}
          </select>
        </div>

        {/* Device Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Device Type</label>
          <select
            value={selectedDeviceType}
            onChange={(e) => onDeviceTypeChange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            {DEVICE_TYPES.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}

export default NetworkDeviceHeaderAndFilters
