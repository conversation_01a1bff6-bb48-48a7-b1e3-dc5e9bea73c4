import React from 'react'

const NetworkDevicePaginationControls = ({
  currentPage,
  totalPages,
  startIndex,
  endIndex,
  totalItems,
  onPageChange
}) => {
  if (totalPages <= 1) return null

  return (
    <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 flex items-center justify-between">
      <div className="flex items-center text-sm text-gray-700">
        <span>
          Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} results
        </span>
      </div>
      
      <div className="flex items-center space-x-2">
        {/* Previous Page Button */}
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
            currentPage === 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
          }`}
        >
          Previous
        </button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-1">
          {/* First page */}
          {currentPage > 3 && (
            <>
              <button
                onClick={() => onPageChange(1)}
                className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
              >
                1
              </button>
              {currentPage > 4 && <span className="text-gray-500">...</span>}
            </>
          )}

          {/* Previous pages */}
          {[...Array(2)].map((_, i) => {
            const pageNum = currentPage - 2 + i;
            if (pageNum > 0 && pageNum < currentPage) {
              return (
                <button
                  key={pageNum}
                  onClick={() => onPageChange(pageNum)}
                  className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                >
                  {pageNum}
                </button>
              );
            }
            return null;
          })}

          {/* Current page */}
          <button
            className="px-3 py-1 rounded-md text-sm font-medium bg-blue-600 text-white border border-blue-600"
            disabled
          >
            {currentPage}
          </button>

          {/* Next pages */}
          {[...Array(2)].map((_, i) => {
            const pageNum = currentPage + 1 + i;
            if (pageNum <= totalPages) {
              return (
                <button
                  key={pageNum}
                  onClick={() => onPageChange(pageNum)}
                  className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                >
                  {pageNum}
                </button>
              );
            }
            return null;
          })}

          {/* Last page */}
          {currentPage < totalPages - 2 && (
            <>
              {currentPage < totalPages - 3 && <span className="text-gray-500">...</span>}
              <button
                onClick={() => onPageChange(totalPages)}
                className="px-3 py-1 rounded-md text-sm font-medium bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
              >
                {totalPages}
              </button>
            </>
          )}
        </div>

        {/* Next Page Button */}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
            currentPage === totalPages
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
          }`}
        >
          Next
        </button>
      </div>
    </div>
  )
}

export default NetworkDevicePaginationControls
