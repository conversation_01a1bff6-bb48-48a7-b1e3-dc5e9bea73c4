import React from 'react'
import <PERSON><PERSON><PERSON><PERSON>on from '../ui/CopyButton'
import NetworkCredentialCell from './NetworkCredentialCell'

const NetworkDeviceSpreadsheetView = ({
  currentPageDevices,
  onEdit,
  onToggleStatus,
  onDelete,
  showNotification
}) => {
  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 sticky left-0 bg-gray-50 z-10 min-w-[200px]">
                Station Name
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[120px]">
                Device Type
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[100px]">
                Host ID
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[130px]">
                IP Address
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[120px]">
                Subnet Mask
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[130px]">
                Gateway
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[150px]">
                Building
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[150px]">
                Station Credentials
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[150px]">
                Windows Credentials
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[150px]">
                Platform Credentials
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[120px]">
                Software Version
              </th>
              <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r-2 border-gray-300 min-w-[80px]">
                Status
              </th>
              <th className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 sticky right-0 z-10 min-w-[120px]">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentPageDevices.map((device, index) => (
              <tr key={device.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors`}>
                <td className={`px-2 py-1.5 text-sm font-medium text-gray-900 border-r-2 border-gray-300 sticky left-0 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} truncate`} title={device.station_name || '-'}>
                  {device.station_name || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 truncate" title={device.device_type || '-'}>
                  {device.device_type || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 font-mono truncate" title={device.host_id || '-'}>
                  {device.host_id || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-900 border-r-2 border-gray-300 font-mono">
                  <div className="flex items-center space-x-1">
                    <span className="truncate" title={device.ip_address}>{device.ip_address || '-'}</span>
                    {device.ip_address && (
                      <CopyButton
                        text={device.ip_address}
                        label="IP Address"
                        onSuccess={(msg) => showNotification(msg, 'success')}
                        onError={(msg) => showNotification(msg, 'error')}
                        size="xs"
                        variant="ghost"
                      />
                    )}
                  </div>
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 font-mono truncate" title={device.subnet_mask || '-'}>
                  {device.subnet_mask || '-'}
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 font-mono">
                  <div className="flex items-center space-x-1">
                    <span className="truncate" title={device.gateway}>{device.gateway || '-'}</span>
                    {device.gateway && (
                      <CopyButton
                        text={device.gateway}
                        label="Gateway"
                        onSuccess={(msg) => showNotification(msg, 'success')}
                        onError={(msg) => showNotification(msg, 'error')}
                        size="xs"
                        variant="ghost"
                      />
                    )}
                  </div>
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 truncate">
                  <div title={`${device.building?.name || '-'}${device.building?.building_code ? ` (${device.building.building_code})` : ''}`}>
                    <div className="font-medium truncate">{device.building?.name || '-'}</div>
                    {device.building?.building_code && (
                      <div className="text-xs text-gray-500 truncate">{device.building.building_code}</div>
                    )}
                  </div>
                </td>

                <td className="px-2 py-1 text-sm text-gray-700 border-r-2 border-gray-300">
                  <NetworkCredentialCell
                    username={device.station_username}
                    device={device}
                    passwordField="station_password_encrypted"
                    showNotification={showNotification}
                  />
                </td>

                <td className="px-2 py-1 text-sm text-gray-700 border-r-2 border-gray-300">
                  <NetworkCredentialCell
                    username={device.windows_username}
                    device={device}
                    passwordField="windows_password_encrypted"
                    showNotification={showNotification}
                  />
                </td>

                <td className="px-2 py-1 text-sm text-gray-700 border-r-2 border-gray-300">
                  <NetworkCredentialCell
                    username={device.platform_username}
                    device={device}
                    passwordField="platform_password_encrypted"
                    showNotification={showNotification}
                  />
                </td>

                <td className="px-2 py-1.5 text-sm text-gray-700 border-r-2 border-gray-300 truncate" title={device.software_version || '-'}>
                  {device.software_version || '-'}
                </td>

                <td className="px-2 py-1.5 border-r-2 border-gray-300">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    device.is_active
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-red-100 text-red-800 border border-red-200'
                  }`}>
                    {device.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>

                <td className={`px-2 py-1.5 text-center bg-gray-50 sticky right-0`}>
                  <div className="flex justify-center space-x-1">
                    <button
                      onClick={() => onEdit(device)}
                      className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors border border-transparent hover:border-blue-200"
                      title="Edit Device"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => onToggleStatus(device.id, device.is_active)}
                      className={`p-1.5 rounded transition-colors border border-transparent ${
                        device.is_active
                          ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-100 hover:border-orange-200'
                          : 'text-green-600 hover:text-green-800 hover:bg-green-100 hover:border-green-200'
                      }`}
                      title={device.is_active ? 'Deactivate Device' : 'Activate Device'}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {device.is_active ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        )}
                      </svg>
                    </button>
                    <button
                      onClick={() => onDelete(device)}
                      className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors border border-transparent hover:border-red-200"
                      title="Delete Device"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default NetworkDeviceSpreadsheetView
