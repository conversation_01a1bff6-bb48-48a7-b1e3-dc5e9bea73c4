import React from 'react'
import { validatePasswordStrength, validatePassphraseStrength, isPrivateIP, isValidIPv4 } from '../../lib/networkDeviceValidation'

/**
 * NetworkDeviceSummaryCards component for displaying network device statistics
 */
const NetworkDeviceSummaryCards = ({ devices = [] }) => {
  // Calculate summary statistics
  const calculateStats = () => {
    const stats = {
      total: devices.length,
      active: 0,
      inactive: 0,
      passwordWarnings: 0,
      networkIssues: 0,
      configurationIssues: 0
    }

    const ipAddresses = new Set()
    const hostIds = new Set()

    devices.forEach(device => {
      // Active/Inactive count
      if (device.is_active) {
        stats.active++
      } else {
        stats.inactive++
      }

      // Password warnings - check for missing credentials or incomplete authentication
      let hasPasswordWarning = false

      // Check for username without password scenarios
      if (device.station_username && !device.station_password_encrypted) {
        hasPasswordWarning = true // Station username without password
      }

      if (device.windows_username && !device.windows_password_encrypted) {
        hasPasswordWarning = true // Windows username without password
      }

      if (device.platform_username && !device.platform_password_encrypted) {
        hasPasswordWarning = true // Platform username without password
      }

      // For network devices, check if they have any authentication configured
      // This is especially important for routers, switches, and access points
      const authRequiredDeviceTypes = ['Router', 'Switch', 'Access Point', 'Firewall', 'Gateway']
      if (authRequiredDeviceTypes.includes(device.device_type)) {
        const hasAnyAuth = device.station_username || device.windows_username || device.platform_username || device.passphrase_encrypted
        if (!hasAnyAuth) {
          hasPasswordWarning = true // Network device without any authentication
        }
      }

      if (hasPasswordWarning) {
        stats.passwordWarnings++
      }

      // Network issues - IP conflicts, invalid IPs, non-private IPs, missing network config
      let hasNetworkIssue = false

      if (device.ip_address) {
        // Check for IP conflicts within the same building
        const ipKey = `${device.building_id}-${device.ip_address}`
        if (ipAddresses.has(ipKey)) {
          hasNetworkIssue = true // Duplicate IP in same building
        } else {
          ipAddresses.add(ipKey)
        }

        // Check if IP is valid and private
        if (!isValidIPv4(device.ip_address)) {
          hasNetworkIssue = true // Invalid IP format
        } else if (!isPrivateIP(device.ip_address)) {
          hasNetworkIssue = true // Public IP (potential security issue)
        }

        // Check for invalid subnet mask if IP is present
        if (device.subnet_mask && device.subnet_mask.trim() !== '') {
          // Basic subnet mask validation
          const validSubnets = ['*************', '***********', '*********', '***************', '***************', '***************', '***************', '***************', '***************']
          if (!validSubnets.includes(device.subnet_mask)) {
            hasNetworkIssue = true // Invalid subnet mask
          }
        }
      }

      // Check for host ID conflicts within the same building
      if (device.host_id && device.building_id) {
        const hostKey = `${device.building_id}-${device.host_id}`
        if (hostIds.has(hostKey)) {
          hasNetworkIssue = true // Duplicate host ID in same building
        } else {
          hostIds.add(hostKey)
        }
      }

      if (hasNetworkIssue) {
        stats.networkIssues++
      }

      // Configuration issues - missing critical configuration
      let hasConfigIssue = false

      // Check for missing station name (required field)
      if (!device.station_name || device.station_name.trim() === '') {
        hasConfigIssue = true // Missing station name
      }

      // Check for missing building assignment (required field)
      if (!device.building_id) {
        hasConfigIssue = true // Missing building assignment
      }

      // Check for missing device type (recommended for network devices)
      if (!device.device_type || device.device_type.trim() === '') {
        hasConfigIssue = true // Missing device type
      }

      // Check for network devices that should have IP configuration
      const ipRequiredDeviceTypes = ['Router', 'Switch', 'Access Point', 'Firewall', 'Gateway', 'Server']
      if (ipRequiredDeviceTypes.includes(device.device_type)) {
        if (!device.ip_address || device.ip_address.trim() === '') {
          hasConfigIssue = true // Network device without IP address
        }
      }

      if (hasConfigIssue) {
        stats.configurationIssues++
      }
    })

    return stats
  }

  const stats = calculateStats()

  const cards = [
    {
      title: 'Total Devices',
      value: stats.total,
      subtitle: `${stats.active} Active, ${stats.inactive} Inactive`,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
        </svg>
      ),
      color: 'blue',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Password Warnings',
      value: stats.passwordWarnings,
      subtitle: stats.passwordWarnings === 0 ? 'All credentials secure' : 'Devices need attention',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      ),
      color: stats.passwordWarnings > 0 ? 'yellow' : 'green',
      bgColor: stats.passwordWarnings > 0 ? 'bg-yellow-50' : 'bg-green-50',
      textColor: stats.passwordWarnings > 0 ? 'text-yellow-600' : 'text-green-600',
      borderColor: stats.passwordWarnings > 0 ? 'border-yellow-200' : 'border-green-200'
    },
    {
      title: 'Network Issues',
      value: stats.networkIssues,
      subtitle: stats.networkIssues === 0 ? 'No conflicts detected' : 'IP/Host conflicts found',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
      color: stats.networkIssues > 0 ? 'red' : 'green',
      bgColor: stats.networkIssues > 0 ? 'bg-red-50' : 'bg-green-50',
      textColor: stats.networkIssues > 0 ? 'text-red-600' : 'text-green-600',
      borderColor: stats.networkIssues > 0 ? 'border-red-200' : 'border-green-200'
    },
    {
      title: 'Configuration',
      value: stats.total - stats.configurationIssues,
      subtitle: stats.configurationIssues === 0 ? 'All devices configured' : `${stats.configurationIssues} need setup`,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      color: stats.configurationIssues > 0 ? 'orange' : 'green',
      bgColor: stats.configurationIssues > 0 ? 'bg-orange-50' : 'bg-green-50',
      textColor: stats.configurationIssues > 0 ? 'text-orange-600' : 'text-green-600',
      borderColor: stats.configurationIssues > 0 ? 'border-orange-200' : 'border-green-200'
    }
  ]

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {cards.map((card, index) => (
        <div
          key={index}
          className={`${card.bgColor} ${card.borderColor} border rounded-lg p-4 hover:shadow-md transition-shadow`}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <div className={`${card.textColor} flex-shrink-0`}>
                  {card.icon}
                </div>
                <h3 className="text-sm font-medium text-gray-900 truncate">
                  {card.title}
                </h3>
              </div>
              <div className="space-y-1">
                <p className={`text-2xl font-bold ${card.textColor}`}>
                  {card.value}
                </p>
                <p className="text-xs text-gray-600">
                  {card.subtitle}
                </p>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default NetworkDeviceSummaryCards
