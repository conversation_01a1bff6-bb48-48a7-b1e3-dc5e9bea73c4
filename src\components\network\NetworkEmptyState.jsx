import React from 'react'

const NetworkEmptyState = ({
  searchTerm,
  selectedBuilding,
  selectedDeviceType,
  onAddDevice
}) => {
  const hasFilters = searchTerm || selectedBuilding || selectedDeviceType

  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No network devices found</h3>
        <p className="text-gray-600 mb-4">
          {hasFilters
            ? 'Try adjusting your filters or search terms.'
            : 'Get started by adding your first network device.'
          }
        </p>
        {!hasFilters && (
          <button
            onClick={onAddDevice}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Network Device
          </button>
        )}
      </div>
    </div>
  )
}

export default NetworkEmptyState
