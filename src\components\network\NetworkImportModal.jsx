import React from 'react'

const NetworkImportModal = ({
  showModal,
  onClose,
  importFile,
  importPreview,
  importErrors,
  isImporting,
  onFileChange,
  onConfirmImport,
  onDownloadTemplate
}) => {
  if (!showModal) return null

  const handleClose = () => {
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Import Network Devices</h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-6">
            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select CSV File
              </label>
              <input
                type="file"
                accept=".csv"
                onChange={onFileChange}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p className="mt-1 text-sm text-gray-500">
                Upload a CSV file with network device information.
                <button
                  onClick={onDownloadTemplate}
                  className="text-blue-600 hover:text-blue-800 ml-1"
                >
                  Download template
                </button>
              </p>
            </div>

            {/* Import Errors */}
            {importErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <h3 className="text-red-800 font-medium mb-2">Import Errors</h3>
                <ul className="text-red-600 text-sm space-y-1 max-h-40 overflow-y-auto">
                  {importErrors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Debug Information (Development Only) */}
            {process.env.NODE_ENV === 'development' && importPreview && importPreview.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="text-blue-800 font-medium mb-2">Debug Information</h3>
                <div className="text-xs text-blue-700 space-y-2">
                  <div>
                    <strong>Total devices to import:</strong> {importPreview.length}
                  </div>
                  <div>
                    <strong>Sample device data:</strong>
                    <pre className="mt-1 bg-blue-100 p-2 rounded text-xs overflow-x-auto">
                      {JSON.stringify(importPreview[0], null, 2)}
                    </pre>
                  </div>
                  <div>
                    <strong>Required fields check:</strong>
                    <ul className="mt-1 space-y-1">
                      {['building_id', 'station_name'].map(field => (
                        <li key={field} className="flex items-center">
                          <span className={`w-2 h-2 rounded-full mr-2 ${
                            importPreview[0] && importPreview[0][field] ? 'bg-green-500' : 'bg-red-500'
                          }`}></span>
                          {field}: {importPreview[0] && importPreview[0][field] ? '✓' : '✗'} (Required)
                        </li>
                      ))}
                      {['device_type', 'host_id', 'ip_address', 'subnet_mask'].map(field => (
                        <li key={field} className="flex items-center">
                          <span className={`w-2 h-2 rounded-full mr-2 ${
                            importPreview[0] && importPreview[0][field] ? 'bg-blue-500' : 'bg-gray-300'
                          }`}></span>
                          {field}: {importPreview[0] && importPreview[0][field] ? '✓' : '○'} (Optional)
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Import Preview */}
            {importPreview && importPreview.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Preview ({importPreview.length} devices)
                </h3>
                <div className="bg-gray-50 border border-gray-200 rounded-md p-4 max-h-64 overflow-y-auto">
                  <div className="space-y-2">
                    {importPreview.slice(0, 5).map((device, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium">{device.station_name}</span>
                        <span className="text-gray-500 ml-2">
                          {device.device_type} • {device.ip_address}
                        </span>
                      </div>
                    ))}
                    {importPreview.length > 5 && (
                      <div className="text-sm text-gray-500">
                        ... and {importPreview.length - 5} more devices
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              <button
                onClick={handleClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={onConfirmImport}
                disabled={!importPreview || importPreview.length === 0 || isImporting}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isImporting ? 'Importing...' : `Import ${importPreview?.length || 0} Devices`}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NetworkImportModal
