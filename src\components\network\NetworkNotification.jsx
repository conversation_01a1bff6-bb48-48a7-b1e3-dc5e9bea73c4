import React from 'react'

const NetworkNotification = ({ notification }) => {
  if (!notification) return null

  return (
    <div className={`mb-4 p-4 rounded-md ${
      notification.type === 'error' 
        ? 'bg-red-50 border border-red-200 text-red-700'
        : notification.type === 'info'
        ? 'bg-blue-50 border border-blue-200 text-blue-700'
        : 'bg-green-50 border border-green-200 text-green-700'
    }`}>
      {notification.message}
    </div>
  )
}

export default NetworkNotification
