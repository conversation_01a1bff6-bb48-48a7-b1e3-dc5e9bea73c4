import React, { useState } from 'react'
import { secureCopyToClipboard } from '../../utils/clipboard'

/**
 * CopyButton component for secure clipboard operations
 */
const CopyButton = ({ 
  text, 
  label = 'Text',
  isSensitive = false,
  onSuccess,
  onError,
  className = '',
  size = 'sm',
  variant = 'ghost',
  showLabel = false,
  disabled = false
}) => {
  const [copied, setCopied] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleCopy = async (e) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (disabled || isLoading || !text) return
    
    setIsLoading(true)
    
    try {
      const result = await secureCopyToClipboard(text, label, isSensitive)
      
      if (result.success) {
        setCopied(true)
        onSuccess?.(result.message)
        
        // Reset copied state after 2 seconds
        setTimeout(() => setCopied(false), 2000)
      } else {
        onError?.(result.message)
      }
    } catch (error) {
      onError?.(`Failed to copy ${label.toLowerCase()}`)
    } finally {
      setIsLoading(false)
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return 'h-6 w-6 p-1'
      case 'sm':
        return 'h-8 w-8 p-1.5'
      case 'md':
        return 'h-10 w-10 p-2'
      case 'lg':
        return 'h-12 w-12 p-2.5'
      default:
        return 'h-8 w-8 p-1.5'
    }
  }

  const getVariantClasses = () => {
    switch (variant) {
      case 'ghost':
        return 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
      case 'outline':
        return 'border border-gray-300 text-gray-600 hover:bg-gray-50'
      case 'solid':
        return 'bg-blue-600 text-white hover:bg-blue-700'
      case 'success':
        return copied ? 'bg-green-600 text-white' : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
      default:
        return 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
    }
  }

  const baseClasses = `
    inline-flex items-center justify-center
    rounded-md transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${getSizeClasses()}
    ${getVariantClasses()}
    ${className}
  `.trim().replace(/\s+/g, ' ')

  return (
    <button
      type="button"
      onClick={handleCopy}
      disabled={disabled || isLoading || !text}
      className={baseClasses}
      title={copied ? `${label} copied!` : `Copy ${label}`}
      aria-label={copied ? `${label} copied to clipboard` : `Copy ${label} to clipboard`}
    >
      {isLoading ? (
        <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      ) : copied ? (
        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      ) : (
        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      )}
      
      {showLabel && (
        <span className="ml-2 text-sm">
          {copied ? 'Copied!' : `Copy ${label}`}
        </span>
      )}
    </button>
  )
}

export default CopyButton
