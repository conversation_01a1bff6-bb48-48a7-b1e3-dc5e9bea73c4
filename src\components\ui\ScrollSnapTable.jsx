import React, { useRef, useEffect } from 'react'

/**
 * ScrollSnapTable - A wrapper component that provides smooth row-based scrolling
 * with scroll-snap functionality for table elements
 */
const ScrollSnapTable = ({ 
  children, 
  className = '', 
  rowHeight = 'auto',
  visibleRows = 5,
  ...props 
}) => {
  const tableContainerRef = useRef(null)

  useEffect(() => {
    const container = tableContainerRef.current
    if (!container) return

    // Calculate row height dynamically if not provided
    const calculateRowHeight = () => {
      const firstRow = container.querySelector('tbody tr')
      if (firstRow) {
        const rect = firstRow.getBoundingClientRect()
        return rect.height
      }
      return 48 // fallback height
    }

    // Calculate header height
    const calculateHeaderHeight = () => {
      const header = container.querySelector('thead')
      if (header) {
        const rect = header.getBoundingClientRect()
        return rect.height
      }
      return 0 // no header
    }

    // Set up scroll snap behavior
    const setupScrollSnap = () => {
      const calculatedRowHeight = rowHeight === 'auto' ? calculateRowHeight() : rowHeight
      const headerHeight = calculateHeaderHeight()
      const dataRowsHeight = calculatedRowHeight * visibleRows
      const containerHeight = headerHeight + dataRowsHeight

      // Set container height to show header + exactly 5 data rows
      container.style.height = `${containerHeight}px`

      // Apply scroll snap styles
      container.style.scrollSnapType = 'y mandatory'
      container.style.overflowY = 'auto'
      container.style.scrollBehavior = 'smooth'

      // Critical: Set scroll padding to account for sticky header height
      // This ensures rows snap to positions below the header, not behind it
      container.style.scrollPaddingTop = `${headerHeight + 2}px` // +2px for visual separation

      // Get table and tbody elements
      const table = container.querySelector('table')
      const tbody = container.querySelector('tbody')

      if (table && tbody) {
        // Ensure table has proper positioning
        table.style.position = 'relative'
        tbody.style.position = 'relative'

        // Add scroll snap align to each row
        const rows = container.querySelectorAll('tbody tr')
        rows.forEach((row, index) => {
          row.style.scrollSnapAlign = 'start'
          row.style.scrollSnapStop = 'always'

          // Ensure rows have proper positioning for snap alignment
          row.style.position = 'relative'

          // Add visual separation for the first row
          if (index === 0) {
            row.style.borderTop = '2px solid #e5e7eb'
          }
        })

        // Add a scroll event listener to ensure proper snapping
        const handleScroll = () => {
          // This helps ensure smooth snapping behavior
          const scrollTop = container.scrollTop
          const adjustedScrollTop = scrollTop + headerHeight

          // Find the row that should be at the top
          const targetRowIndex = Math.round(adjustedScrollTop / calculatedRowHeight)
          const targetScrollPosition = Math.max(0, targetRowIndex * calculatedRowHeight - headerHeight)

          // Only adjust if we're significantly off
          if (Math.abs(container.scrollTop - targetScrollPosition) > 5) {
            container.scrollTo({
              top: targetScrollPosition,
              behavior: 'smooth'
            })
          }
        }

        // Remove existing scroll listener if any
        container.removeEventListener('scroll', container._snapScrollHandler)

        // Add new scroll listener with debouncing
        let scrollTimeout
        container._snapScrollHandler = () => {
          clearTimeout(scrollTimeout)
          scrollTimeout = setTimeout(handleScroll, 150)
        }

        container.addEventListener('scroll', container._snapScrollHandler, { passive: true })
      }
    }

    // Initial setup
    setupScrollSnap()

    // Recalculate on window resize
    const handleResize = () => {
      setupScrollSnap()
    }

    window.addEventListener('resize', handleResize)
    
    // Recalculate when content changes
    const observer = new MutationObserver(() => {
      setTimeout(setupScrollSnap, 100) // Small delay to ensure DOM is updated
    })
    
    observer.observe(container, { 
      childList: true, 
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    })

    return () => {
      window.removeEventListener('resize', handleResize)
      observer.disconnect()
      // Clean up scroll event listener
      if (container && container._snapScrollHandler) {
        container.removeEventListener('scroll', container._snapScrollHandler)
      }
    }
  }, [rowHeight, visibleRows])

  return (
    <div 
      ref={tableContainerRef}
      className={`scroll-snap-table-container ${className}`}
      style={{
        position: 'relative',
        borderRadius: '0.5rem',
        border: '1px solid #e5e7eb',
        backgroundColor: 'white',
        // Ensure smooth scrolling
        scrollBehavior: 'smooth',
        // Hide scrollbar on webkit browsers for cleaner look
        WebkitScrollbar: {
          width: '6px'
        },
        WebkitScrollbarTrack: {
          background: '#f1f1f1',
          borderRadius: '3px'
        },
        WebkitScrollbarThumb: {
          background: '#c1c1c1',
          borderRadius: '3px'
        },
        WebkitScrollbarThumbHover: {
          background: '#a8a8a8'
        }
      }}
      {...props}
    >
      <style>{`
        .scroll-snap-table-container {
          /* Custom scrollbar styles */
          scrollbar-width: thin;
          scrollbar-color: #c1c1c1 #f1f1f1;
        }

        .scroll-snap-table-container::-webkit-scrollbar {
          width: 6px;
        }

        .scroll-snap-table-container::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        .scroll-snap-table-container::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        .scroll-snap-table-container::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Ensure table takes full width */
        .scroll-snap-table-container table {
          width: 100%;
          border-collapse: collapse;
        }

        /* Sticky header styling */
        .scroll-snap-table-container thead {
          position: sticky;
          top: 0;
          z-index: 20;
          background: white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Ensure header cells have proper background */
        .scroll-snap-table-container thead th {
          background: inherit;
          border-bottom: 2px solid #e5e7eb;
        }

        /* Table body positioning for proper scroll snap */
        .scroll-snap-table-container tbody {
          position: relative;
        }

        /* Smooth transitions for scroll snap */
        .scroll-snap-table-container tbody tr {
          transition: transform 0.2s ease-in-out;
        }

        /* Ensure proper spacing between header and first row */
        .scroll-snap-table-container tbody tr:first-child {
          border-top: 2px solid #e5e7eb;
        }

        /* Enhanced visual separation for better UX */
        .scroll-snap-table-container tbody tr:hover {
          background-color: #f8fafc;
        }

        /* Focus styles for accessibility */
        .scroll-snap-table-container:focus-within {
          outline: 2px solid #3b82f6;
          outline-offset: 2px;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
          .scroll-snap-table-container {
            border-radius: 0.375rem;
          }
        }
      `}</style>
      {children}
    </div>
  )
}

export default ScrollSnapTable
