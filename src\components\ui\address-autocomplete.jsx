import React, { useRef, useEffect } from 'react'
import { usePlacesAutocompleteNew, getGeocode, getLatLng } from '../../hooks/usePlacesAutocompleteNew'
import { Input } from './input'
import { cn } from '../../lib/utils'
import { MapPin, Loader2 } from 'lucide-react'

const AddressAutocomplete = ({
  value,
  onChange,
  onSelect,
  onCoordinatesSelect,
  placeholder = "Enter address...",
  className,
  disabled = false,
  error,
  ...props
}) => {
  const {
    ready,
    value: searchValue,
    suggestions: { status, data },
    setValue,
    clearSuggestions,
  } = usePlacesAutocompleteNew({
    requestOptions: {
      // Bias results towards commercial buildings and addresses
      includedPrimaryTypes: ['street_address', 'establishment'],
      // You can add location bias here if needed
      // locationBias: { lat: 40.7128, lng: -74.0060 }, // Example: NYC
    },
    debounce: 300,
    cache: 24 * 60 * 60, // Cache for 24 hours
  })

  const dropdownRef = useRef(null)
  const inputRef = useRef(null)
  const [showDropdown, setShowDropdown] = React.useState(false)
  const [selectedIndex, setSelectedIndex] = React.useState(-1)

  // Sync external value with internal search value
  useEffect(() => {
    if (value !== searchValue) {
      setValue(value, false)
    }
  }, [value, searchValue, setValue])

  // Handle input changes
  const handleInputChange = (e) => {
    const newValue = e.target.value
    setValue(newValue)
    onChange?.(newValue)
    setShowDropdown(true)
    setSelectedIndex(-1)
  }

  // Handle suggestion selection
  const handleSelect = async (suggestion) => {
    const address = suggestion.description
    setValue(address, false)
    onChange?.(address)
    onSelect?.(address, suggestion)
    
    // Get coordinates if callback provided
    if (onCoordinatesSelect) {
      try {
        const results = await getGeocode({ address })
        const { lat, lng } = await getLatLng(results[0])
        onCoordinatesSelect({ lat, lng, address, suggestion })
      } catch (error) {
        console.error('Error getting coordinates:', error)
      }
    }
    
    clearSuggestions()
    setShowDropdown(false)
    setSelectedIndex(-1)
  }

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showDropdown || data.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < data.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && data[selectedIndex]) {
          handleSelect(data[selectedIndex])
        }
        break
      case 'Escape':
        setShowDropdown(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Show dropdown when there are suggestions
  useEffect(() => {
    if (status === 'OK' && data.length > 0) {
      setShowDropdown(true)
    } else if (status === 'ZERO_RESULTS') {
      setShowDropdown(false)
    }
  }, [status, data])

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <Input
          ref={inputRef}
          value={searchValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (data.length > 0) setShowDropdown(true)
          }}
          placeholder={ready ? placeholder : "Loading Google Maps..."}
          disabled={!ready || disabled}
          className={cn(
            "pl-10",
            error && "border-red-300 focus:border-red-500 focus:ring-red-500",
            className
          )}
          {...props}
        />
        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        {status === 'LOADING' && (
          <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
        )}
      </div>

      {/* Dropdown */}
      {showDropdown && status === 'OK' && data.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {data.map((suggestion, index) => {
            const {
              place_id,
              structured_formatting: { main_text, secondary_text },
              description: _description
            } = suggestion

            return (
              <div
                key={place_id}
                className={cn(
                  "px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 hover:bg-gray-50",
                  index === selectedIndex && "bg-blue-50 border-blue-200"
                )}
                onClick={() => handleSelect(suggestion)}
                onMouseEnter={() => setSelectedIndex(index)}
              >
                <div className="flex items-start space-x-3">
                  <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-900 truncate">
                      {main_text}
                    </div>
                    {secondary_text && (
                      <div className="text-sm text-gray-500 truncate">
                        {secondary_text}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Error messages */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {/* Google Maps API error messages */}
      {status === 'API_NOT_ACTIVATED' && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded">
          <p className="text-sm text-red-800">
            <strong>Google Maps API Error:</strong> The Maps JavaScript API is not activated for this API key.
          </p>
          <p className="text-xs text-red-600 mt-1">
            Please enable the Maps JavaScript API and Places API in Google Cloud Console, and ensure billing is set up.
          </p>
        </div>
      )}

      {status === 'INVALID_KEY' && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded">
          <p className="text-sm text-red-800">
            <strong>API Key Error:</strong> The provided Google Maps API key is invalid.
          </p>
          <p className="text-xs text-red-600 mt-1">
            Please check your API key in the .env.local file.
          </p>
        </div>
      )}

      {status === 'QUOTA_EXCEEDED' && (
        <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm text-yellow-800">
            <strong>Quota Exceeded:</strong> You have exceeded your daily quota for the Google Maps API.
          </p>
          <p className="text-xs text-yellow-600 mt-1">
            Please check your usage in Google Cloud Console or increase your quota limits.
          </p>
        </div>
      )}

      {status === 'REQUEST_DENIED' && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded">
          <p className="text-sm text-red-800">
            <strong>Request Denied:</strong> The Google Maps API request was denied.
          </p>
          <p className="text-xs text-red-600 mt-1">
            Please check that the required APIs are enabled and billing is active.
          </p>
        </div>
      )}
    </div>
  )
}

export default AddressAutocomplete
