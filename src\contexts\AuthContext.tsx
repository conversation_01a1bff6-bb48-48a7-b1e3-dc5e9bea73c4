import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { supabase, hasSupabaseConfig } from '../lib/supabase'
import { setSentryUser, Sentry } from '../lib/sentry'
import type { User } from '@supabase/supabase-js'
import type { UseAuthReturn } from '@/types'

interface AuthContextType extends UseAuthReturn {}

const AuthContext = createContext<AuthContextType | null>(null)

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState<boolean>(true)

  useEffect(() => {
    // If Supabase is not configured, just set loading to false
    if (!hasSupabaseConfig) {
      setLoading(false)
      return
    }

    // Get initial session
    const getSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        const user = session?.user ?? null
        setUser(user)

        // Set initial Sentry user context
        if (user) {
          setSentryUser(user)
          console.log('✅ Initial Sentry user context set for:', user.email)
        }
      } catch (error) {
        console.warn('Supabase auth error:', error.message)
      } finally {
        setLoading(false)
      }
    }

    getSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        const user = session?.user ?? null
        setUser(user)
        setLoading(false)

        // Update Sentry user context
        if (user) {
          setSentryUser(user)
          console.log('✅ Sentry user context updated for:', user.email)
        } else {
          // Clear Sentry user context on logout
          Sentry.setUser(null)
          console.log('🔄 Sentry user context cleared')
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signUp = async (email: string, password: string) => {
    if (!hasSupabaseConfig) {
      return { data: null, error: { message: 'Supabase not configured' } }
    }
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      })
      return { data, error }
    } catch (error) {
      return { data: null, error: error as Error }
    }
  }

  const signIn = async (email: string, password: string) => {
    if (!hasSupabaseConfig) {
      return { data: null, error: { message: 'Supabase not configured' } }
    }
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      return { data, error }
    } catch (error) {
      return { data: null, error: error as Error }
    }
  }

  const signOut = async () => {
    if (!hasSupabaseConfig) {
      return { error: { message: 'Supabase not configured' } }
    }
    try {
      const { error } = await supabase.auth.signOut()
      return { error }
    } catch (error) {
      return { error: error as Error }
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    signUp,
    signIn,
    signOut,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
