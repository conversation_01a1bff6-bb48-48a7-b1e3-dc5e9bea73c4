import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { AuthProvider, useAuth } from '../AuthContext'
import { ReactNode } from 'react'

// Mock the dependencies
vi.mock('../../lib/supabase')
vi.mock('../../lib/sentry')

// Test component to access the auth context
const TestComponent = () => {
  const { user, loading, signIn, signUp, signOut } = useAuth()
  
  return (
    <div>
      <div data-testid="loading">{loading ? 'loading' : 'not-loading'}</div>
      <div data-testid="user">{user ? user.email : 'no-user'}</div>
      <button onClick={() => signIn('<EMAIL>', 'password')}>Sign In</button>
      <button onClick={() => signUp('<EMAIL>', 'password')}>Sign Up</button>
      <button onClick={() => signOut()}>Sign Out</button>
    </div>
  )
}

const renderWithAuthProvider = (children: ReactNode) => {
  return render(
    <AuthProvider>
      {children}
    </AuthProvider>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should throw error when useAuth is used outside AuthProvider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    expect(() => render(<TestComponent />)).toThrow(
      'useAuth must be used within an AuthProvider'
    )
    
    consoleSpy.mockRestore()
  })

  it('should provide initial loading state', () => {
    renderWithAuthProvider(<TestComponent />)
    
    expect(screen.getByTestId('loading')).toHaveTextContent('loading')
    expect(screen.getByTestId('user')).toHaveTextContent('no-user')
  })

  it('should handle successful session retrieval', async () => {
    const mockUser = { id: '1', email: '<EMAIL>' }
    
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.auth.getSession.mockResolvedValue({
      data: { session: { user: mockUser } }
    })
    mockSupabase.hasSupabaseConfig = true

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
  })

  it('should handle no session', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.auth.getSession.mockResolvedValue({
      data: { session: null }
    })
    mockSupabase.hasSupabaseConfig = true

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    expect(screen.getByTestId('user')).toHaveTextContent('no-user')
  })

  it('should handle Supabase not configured', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.hasSupabaseConfig = false

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    expect(screen.getByTestId('user')).toHaveTextContent('no-user')
  })

  it('should handle auth state changes', async () => {
    const mockUser = { id: '1', email: '<EMAIL>' }
    let authStateCallback: (event: string, session: any) => void

    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.auth.getSession.mockResolvedValue({
      data: { session: null }
    })
    mockSupabase.supabase.auth.onAuthStateChange.mockImplementation((callback) => {
      authStateCallback = callback
      return { data: { subscription: { unsubscribe: vi.fn() } } }
    })
    mockSupabase.hasSupabaseConfig = true

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    // Simulate sign in
    authStateCallback('SIGNED_IN', { user: mockUser })

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    })

    // Simulate sign out
    authStateCallback('SIGNED_OUT', null)

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('no-user')
    })
  })

  it('should handle sign in', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: { id: '1', email: '<EMAIL>' } },
      error: null
    })
    mockSupabase.hasSupabaseConfig = true

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    const signInButton = screen.getByText('Sign In')
    signInButton.click()

    await waitFor(() => {
      expect(mockSupabase.supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password'
      })
    })
  })

  it('should handle sign up', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.auth.signUp.mockResolvedValue({
      data: { user: { id: '1', email: '<EMAIL>' } },
      error: null
    })
    mockSupabase.hasSupabaseConfig = true

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    const signUpButton = screen.getByText('Sign Up')
    signUpButton.click()

    await waitFor(() => {
      expect(mockSupabase.supabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password'
      })
    })
  })

  it('should handle sign out', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.auth.signOut.mockResolvedValue({
      error: null
    })
    mockSupabase.hasSupabaseConfig = true

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    const signOutButton = screen.getByText('Sign Out')
    signOutButton.click()

    await waitFor(() => {
      expect(mockSupabase.supabase.auth.signOut).toHaveBeenCalled()
    })
  })

  it('should handle auth errors gracefully', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.auth.getSession.mockRejectedValue(new Error('Auth error'))
    mockSupabase.hasSupabaseConfig = true

    renderWithAuthProvider(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })

    expect(screen.getByTestId('user')).toHaveTextContent('no-user')
  })

  it('should return error when Supabase not configured for auth operations', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.hasSupabaseConfig = false

    const TestAuthOperations = () => {
      const { signIn, signUp, signOut } = useAuth()
      
      const handleSignIn = async () => {
        const result = await signIn('<EMAIL>', 'password')
        expect(result.error).toEqual({ message: 'Supabase not configured' })
      }

      const handleSignUp = async () => {
        const result = await signUp('<EMAIL>', 'password')
        expect(result.error).toEqual({ message: 'Supabase not configured' })
      }

      const handleSignOut = async () => {
        const result = await signOut()
        expect(result.error).toEqual({ message: 'Supabase not configured' })
      }
      
      return (
        <div>
          <button onClick={handleSignIn}>Sign In</button>
          <button onClick={handleSignUp}>Sign Up</button>
          <button onClick={handleSignOut}>Sign Out</button>
        </div>
      )
    }

    renderWithAuthProvider(<TestAuthOperations />)

    await waitFor(() => {
      expect(screen.getByText('Sign In')).toBeInTheDocument()
    })

    // Test each auth operation
    screen.getByText('Sign In').click()
    screen.getByText('Sign Up').click()
    screen.getByText('Sign Out').click()
  })
})
