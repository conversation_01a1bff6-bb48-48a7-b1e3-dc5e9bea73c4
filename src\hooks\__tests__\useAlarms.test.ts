import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { useAlarms } from '../useAlarms'

// Mock the dependencies
vi.mock('../../lib/supabase')
vi.mock('../../lib/alarmUtils')
vi.mock('../../lib/shared/escalationService')

describe('useAlarms', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useAlarms())
    
    expect(result.current.loading).toBe(true)
    expect(result.current.alarms).toEqual([])
    expect(result.current.error).toBe(null)
  })

  it('should fetch alarms data on mount', async () => {
    const mockAlarms = [
      {
        id: '1',
        building_id: 'building-1',
        subject: 'Test Alarm',
        status: 'received',
        created_at: '2024-01-01T00:00:00Z'
      }
    ]

    // Mock the supabase response
    const mockSupabase = await import('../../lib/supabase')
    const mockChain = {
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockAlarms, error: null }),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: mockAlarms[0], error: null })
    }

    mockSupabase.supabase.from.mockReturnValue(mockChain)

    const { result } = renderHook(() => useAlarms())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    }, { timeout: 3000 })

    expect(result.current.alarms).toEqual(expect.arrayContaining([
      expect.objectContaining({
        id: '1',
        status: 'received'
      })
    ]))
    expect(result.current.error).toBe(null)
  })

  it('should handle errors when fetching alarms', async () => {
    const mockError = new Error('Database connection failed')

    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        order: vi.fn().mockResolvedValue({
          data: null,
          error: mockError
        })
      })
    })

    const { result } = renderHook(() => useAlarms())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.error).toBe(mockError.message)
    expect(result.current.alarms).toEqual([])
  })

  it('should acknowledge alarm successfully', async () => {
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        order: vi.fn().mockResolvedValue({
          data: [],
          error: null
        })
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: { id: '1', status: 'acknowledged' },
          error: null
        })
      })
    })

    const { result } = renderHook(() => useAlarms())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    const response = await result.current.acknowledgeAlarm('1')
    
    expect(response.success).toBe(true)
    expect(response.error).toBeUndefined()
  })

  it('should handle acknowledge alarm errors', async () => {
    const mockError = new Error('Update failed')
    
    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        order: vi.fn().mockResolvedValue({
          data: [],
          error: null
        })
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: mockError
        })
      })
    })

    const { result } = renderHook(() => useAlarms())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    const response = await result.current.acknowledgeAlarm('1')
    
    expect(response.success).toBe(false)
    expect(response.error).toBe(mockError.message)
  })

  it('should filter alarms by status', async () => {
    const mockAlarms = [
      { id: '1', status: 'received' },
      { id: '2', status: 'acknowledged' },
      { id: '3', status: 'received' }
    ]

    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        order: vi.fn().mockResolvedValue({
          data: mockAlarms,
          error: null
        })
      })
    })

    const { result } = renderHook(() => useAlarms())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    const receivedAlarms = result.current.getAlarmsByStatus('received')
    expect(receivedAlarms).toHaveLength(2)
    expect(receivedAlarms.every(alarm => alarm.status === 'received')).toBe(true)
  })

  it('should trigger escalation for critical alarms', async () => {
    const mockEscalationService = await import('../../lib/shared/escalationService')
    mockEscalationService.triggerEscalation.mockResolvedValue({
      success: true,
      escalationTriggered: true
    })

    const mockSupabase = await import('../../lib/supabase')
    mockSupabase.supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        order: vi.fn().mockResolvedValue({
          data: [],
          error: null
        })
      })
    })

    const { result } = renderHook(() => useAlarms())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    const response = await result.current.triggerEscalation('alarm-1')
    
    expect(response.success).toBe(true)
    expect(mockEscalationService.triggerEscalation).toHaveBeenCalledWith(
      expect.anything(),
      'alarm-1'
    )
  })

  it('should refresh alarms data', async () => {
    const mockSupabase = await import('../../lib/supabase')
    const selectMock = vi.fn().mockReturnValue({
      order: vi.fn().mockResolvedValue({
        data: [],
        error: null
      })
    })
    
    mockSupabase.supabase.from.mockReturnValue({
      select: selectMock
    })

    const { result } = renderHook(() => useAlarms())

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Clear previous calls
    selectMock.mockClear()

    await result.current.refreshAlarms()

    // Should have called select again for refresh
    expect(selectMock).toHaveBeenCalled()
  })
})
