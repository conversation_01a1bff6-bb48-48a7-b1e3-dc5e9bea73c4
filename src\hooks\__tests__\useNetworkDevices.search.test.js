import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useNetworkDevices } from '../useNetworkDevices'

// Mock Supabase
vi.mock('../../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        order: vi.fn(() => Promise.resolve({
          data: [
            {
              id: '1',
              station_name: 'Router-01',
              host_id: 'RTR-001',
              ip_address: '***********',
              device_type: 'BACnet router',
              building: { name: 'Main Office' }
            },
            {
              id: '2',
              station_name: null, // Test null station_name
              host_id: 'SW-002',
              ip_address: '***********',
              device_type: 'Switch',
              building: { name: 'Main Office' }
            },
            {
              id: '3',
              station_name: 'Access-Point-03',
              host_id: null, // Test null host_id
              ip_address: '***********',
              device_type: 'Access Point',
              building: { name: 'Main Office' }
            },
            {
              id: '4',
              station_name: 'Server-04',
              host_id: 'SRV-004',
              ip_address: null, // Test null ip_address
              device_type: null, // Test null device_type
              building: null // Test null building
            }
          ],
          error: null
        }))
      }))
    }))
  },
  hasSupabaseConfig: true
}))

describe('useNetworkDevices - searchDevices function', () => {
  it('should handle null values in device properties without crashing', async () => {
    const { result } = renderHook(() => useNetworkDevices())

    // Wait for the hook to load data
    await vi.waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    // Test searchDevices function with various search terms
    const { searchDevices } = result.current

    // Should not crash when searching devices with null properties
    expect(() => {
      const results1 = searchDevices('router')
      expect(Array.isArray(results1)).toBe(true)
    }).not.toThrow()

    expect(() => {
      const results2 = searchDevices('SW-002')
      expect(Array.isArray(results2)).toBe(true)
    }).not.toThrow()

    expect(() => {
      const results3 = searchDevices('***********')
      expect(Array.isArray(results3)).toBe(true)
    }).not.toThrow()

    expect(() => {
      const results4 = searchDevices('nonexistent')
      expect(Array.isArray(results4)).toBe(true)
    }).not.toThrow()
  })

  it('should return correct search results despite null values', async () => {
    const { result } = renderHook(() => useNetworkDevices())

    await vi.waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    const { searchDevices } = result.current

    // Search for "router" - should find Router-01
    const routerResults = searchDevices('router')
    expect(routerResults).toHaveLength(1)
    expect(routerResults[0].station_name).toBe('Router-01')

    // Search for "SW-002" - should find device with null station_name
    const switchResults = searchDevices('SW-002')
    expect(switchResults).toHaveLength(1)
    expect(switchResults[0].host_id).toBe('SW-002')
    expect(switchResults[0].station_name).toBe(null)

    // Search for "***********" - should find Access-Point-03
    const ipResults = searchDevices('***********')
    expect(ipResults).toHaveLength(1)
    expect(ipResults[0].ip_address).toBe('***********')

    // Search for empty string - should return all devices
    const allResults = searchDevices('')
    expect(allResults).toHaveLength(4)

    // Search for null/undefined - should return all devices
    const nullResults = searchDevices(null)
    expect(nullResults).toHaveLength(4)

    const undefinedResults = searchDevices(undefined)
    expect(undefinedResults).toHaveLength(4)
  })

  it('should handle case-insensitive search with null values', async () => {
    const { result } = renderHook(() => useNetworkDevices())

    await vi.waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    const { searchDevices } = result.current

    // Test case-insensitive search
    const upperCaseResults = searchDevices('ROUTER')
    expect(upperCaseResults).toHaveLength(1)
    expect(upperCaseResults[0].station_name).toBe('Router-01')

    const mixedCaseResults = searchDevices('BaCnEt')
    expect(mixedCaseResults).toHaveLength(1)
    expect(mixedCaseResults[0].device_type).toBe('BACnet router')

    // Search in building name (case-insensitive)
    const buildingResults = searchDevices('main')
    expect(buildingResults).toHaveLength(3) // Should find 3 devices in Main Office
  })
})
