import { useState, useEffect, useMemo, useCallback } from 'react'
import { supabase, hasSupabaseConfig } from '../lib/supabase'
import { parseAlarmEmail, findBuildingByEmail, findAlarmTypeByName, findSeverityByName } from '../lib/alarmUtils'
import { triggerEscalation as triggerEscalationService, escalationService } from '../lib/shared/escalationService'
import { handleApiOperation } from '../lib/errorHandling'
import type {
  AlarmNotification,
  Building,
  AlarmType,
  SeverityLevel,
  UseAlarmsReturn,
  ApiResponse
} from '@/types'

/**
 * Custom hook for alarm-related operations
 */
export const useAlarms = (): UseAlarmsReturn => {
  const [alarms, setAlarms] = useState<AlarmNotification[]>([])
  const [buildings, setBuildings] = useState<Building[]>([])
  const [alarmTypes, setAlarmTypes] = useState<AlarmType[]>([])
  const [severityLevels, setSeverityLevels] = useState<SeverityLevel[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch all alarm-related data
  useEffect(() => {
    const fetchData = async () => {
      if (!hasSupabaseConfig) {
        setError('Supabase not configured. Please add your Supabase URL and API key to .env.local')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        
        // Fetch all reference data in parallel
        const [
          alarmsResult,
          buildingsResult,
          alarmTypesResult,
          severityLevelsResult
        ] = await Promise.all([
          supabase
            .from('alarm_notifications')
            .select(`
              *,
              building:buildings(*),
              alarm_type:alarm_types(*),
              severity:severity_levels(*)
            `)
            .order('created_at', { ascending: false }),

          supabase
            .from('buildings')
            .select('*')
            .eq('is_active', true)
            .order('name'),

          supabase
            .from('alarm_types')
            .select('*')
            .order('name'),

          supabase
            .from('severity_levels')
            .select('*')
            .order('level')
        ])

        // Check for errors
        if (alarmsResult.error) throw alarmsResult.error
        if (buildingsResult.error) throw buildingsResult.error
        if (alarmTypesResult.error) throw alarmTypesResult.error
        if (severityLevelsResult.error) throw severityLevelsResult.error

        // Process alarms data to include user information
        const processedAlarms = await processAlarmsWithUserInfo(alarmsResult.data || [])

        // Set the data
        setAlarms(processedAlarms)
        setBuildings(buildingsResult.data || [])
        setAlarmTypes(alarmTypesResult.data || [])
        setSeverityLevels(severityLevelsResult.data || [])
        
      } catch (err) {
        console.error('Error fetching alarm data:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  /**
   * Refresh alarms data (useful when buildings are updated)
   */
  const refreshAlarms = async () => {
    if (!hasSupabaseConfig) return

    try {
      setLoading(true)

      const [
        alarmsResult,
        buildingsResult,
        alarmTypesResult,
        severityLevelsResult
      ] = await Promise.all([
        supabase
          .from('alarm_notifications')
          .select(`
            *,
            building:buildings(*),
            alarm_type:alarm_types(*),
            severity:severity_levels(*)
          `)
          .order('created_at', { ascending: false }),

        supabase
          .from('buildings')
          .select('*')
          .eq('is_active', true)
          .order('name'),

        supabase
          .from('alarm_types')
          .select('*')
          .order('name'),

        supabase
          .from('severity_levels')
          .select('*')
          .order('level')
      ])

      if (alarmsResult.error) throw alarmsResult.error
      if (buildingsResult.error) throw buildingsResult.error
      if (alarmTypesResult.error) throw alarmTypesResult.error
      if (severityLevelsResult.error) throw severityLevelsResult.error

      const processedAlarms = await processAlarmsWithUserInfo(alarmsResult.data || [])

      setAlarms(processedAlarms)
      setBuildings(buildingsResult.data || [])
      setAlarmTypes(alarmTypesResult.data || [])
      setSeverityLevels(severityLevelsResult.data || [])

    } catch (err) {
      console.error('Error refreshing alarm data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Helper function to fetch user information for alarms
   * @param {Array} alarms - Array of alarm objects
   * @returns {Array} Alarms with user information added
   */
  const processAlarmsWithUserInfo = async (alarms) => {
    if (!alarms || alarms.length === 0) return alarms

    try {
      // Get unique user IDs from acknowledged_by and resolved_by fields
      const userIds = new Set()
      alarms.forEach(alarm => {
        if (alarm.acknowledged_by) userIds.add(alarm.acknowledged_by)
        if (alarm.resolved_by) userIds.add(alarm.resolved_by)
      })

      if (userIds.size === 0) return alarms

      // Try to fetch user information using Supabase auth admin API
      // Note: This requires admin privileges and might not work in all setups
      const userMap = new Map()

      try {
        // Attempt to get current user info if available
        const { data: { user } } = await supabase.auth.getUser()

        // For now, we'll create user info based on available data
        Array.from(userIds).forEach(userId => {
          if (user && user.id === userId) {
            userMap.set(userId, {
              id: userId,
              email: user.email || `user-${userId.substring(0, 8)}@example.com`
            })
          } else {
            userMap.set(userId, {
              id: userId,
              email: `user-${userId.substring(0, 8)}@example.com` // Placeholder
            })
          }
        })
      } catch (authError) {
        console.warn('Could not fetch user auth info:', authError)
        // Fallback to placeholder user info
        Array.from(userIds).forEach(userId => {
          userMap.set(userId, {
            id: userId,
            email: `user-${userId.substring(0, 8)}@example.com`
          })
        })
      }

      // Add user information to alarms
      return alarms.map(alarm => ({
        ...alarm,
        acknowledged_user: alarm.acknowledged_by ? userMap.get(alarm.acknowledged_by) : null,
        resolved_user: alarm.resolved_by ? userMap.get(alarm.resolved_by) : null
      }))
    } catch (error) {
      console.warn('Could not process user information:', error)
      return alarms
    }
  }

  /**
   * Process incoming alarm webhook
   * @param {Object} webhookData - Raw webhook data from Mailgun
   * @returns {Object} Result with success status and data/error
   */
  const processAlarmWebhook = async (webhookData) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Parse the alarm email
      const parsedAlarm = parseAlarmEmail(webhookData)
      
      // Find the building by email address
      const building = findBuildingByEmail(parsedAlarm.recipientEmail, buildings)
      if (!building) {
        console.warn('Building not found for email:', parsedAlarm.recipientEmail)
        // We'll still store the alarm but without building association
      }

      // Find alarm type
      const alarmType = findAlarmTypeByName(parsedAlarm.alarmType, alarmTypes)
      
      // Find severity level
      const severityLevel = findSeverityByName(parsedAlarm.severity, severityLevels)

      // Generate a fallback alarm ID if none was parsed from the email
      const buildingAlarmId = parsedAlarm.buildingAlarmId ||
        parsedAlarm.messageId ||
        `fallback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      // Prepare alarm notification data
      const alarmData = {
        building_id: building?.id || null,
        building_alarm_id: buildingAlarmId,
        alarm_type_id: alarmType?.id || null,
        severity_id: severityLevel?.id || null,
        subject: parsedAlarm.subject,
        sender_email: parsedAlarm.senderEmail,
        recipient_email: parsedAlarm.recipientEmail,
        message_id: parsedAlarm.messageId,
        alarm_time: parsedAlarm.alarmTime,
        alarm_details: parsedAlarm.alarmDetails,
        location_details: parsedAlarm.locationDetails,
        body_html: parsedAlarm.bodyHtml,
        body_plain: parsedAlarm.bodyPlain,
        webhook_signature: parsedAlarm.webhookSignature,
        webhook_timestamp: parsedAlarm.webhookTimestamp,
        webhook_token: parsedAlarm.webhookToken,
        raw_webhook_data: parsedAlarm.rawWebhookData,
        status: 'received'
      }

      // Insert the alarm notification
      const { data, error } = await supabase
        .from('alarm_notifications')
        .insert(alarmData)
        .select(`
          *,
          building:buildings(*),
          alarm_type:alarm_types(*),
          severity:severity_levels(*)
        `)

      if (error) throw error

      // Process the new alarm with user info
      const processedData = await processAlarmsWithUserInfo(data)

      // Update local state
      if (processedData && processedData.length > 0) {
        setAlarms(prev => [processedData[0], ...prev])

        // Trigger escalation for critical alarms using shared service
        const newAlarm = processedData[0]
        try {
          const escalationResult = await triggerEscalationService(supabase, newAlarm)
          if (escalationResult.escalationTriggered) {
            console.log('Escalation triggered for alarm:', newAlarm.id)
          } else {
            console.log('Escalation not triggered for alarm:', newAlarm.id, 'Reason:', escalationResult.error)
          }
        } catch (escalationError) {
          console.error('Failed to start escalation:', escalationError)
          // Don't fail the webhook processing if escalation fails
        }
      }

      return { success: true, data: processedData[0] }
      
    } catch (err) {
      console.error('Error processing alarm webhook:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Acknowledge an alarm
   * @param {string} alarmId - Alarm notification ID
   * @param {string} userId - User ID acknowledging the alarm
   * @param {string} notes - Optional notes
   * @returns {Object} Result with success status
   */
  const acknowledgeAlarm = async (alarmId, userId, notes = '') => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { data, error } = await supabase
        .from('alarm_notifications')
        .update({
          status: 'acknowledged',
          acknowledged_by: userId,
          acknowledged_at: new Date().toISOString(),
          notes: notes
        })
        .eq('id', alarmId)
        .select(`
          *,
          building:buildings(*),
          alarm_type:alarm_types(*),
          severity:severity_levels(*)
        `)

      if (error) throw error

      // Process the updated alarm with user info
      const processedData = await processAlarmsWithUserInfo(data)

      // Update local state
      if (processedData && processedData.length > 0) {
        setAlarms(prev => prev.map(alarm =>
          alarm.id === alarmId ? processedData[0] : alarm
        ))
      }

      return { success: true, data: processedData[0] }
      
    } catch (err) {
      console.error('Error acknowledging alarm:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Resolve an alarm
   * @param {string} alarmId - Alarm notification ID
   * @param {string} userId - User ID resolving the alarm
   * @param {string} notes - Optional resolution notes
   * @returns {Object} Result with success status
   */
  const resolveAlarm = async (alarmId, userId, notes = '') => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { data, error } = await supabase
        .from('alarm_notifications')
        .update({
          status: 'resolved',
          resolved_by: userId,
          resolved_at: new Date().toISOString(),
          notes: notes
        })
        .eq('id', alarmId)
        .select(`
          *,
          building:buildings(*),
          alarm_type:alarm_types(*),
          severity:severity_levels(*)
        `)

      if (error) throw error

      // Process the updated alarm with user info
      const processedData = await processAlarmsWithUserInfo(data)

      // Update local state
      if (processedData && processedData.length > 0) {
        setAlarms(prev => prev.map(alarm =>
          alarm.id === alarmId ? processedData[0] : alarm
        ))
      }

      return { success: true, data: processedData[0] }
      
    } catch (err) {
      console.error('Error resolving alarm:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Get alarms filtered by status (memoized for performance)
   * @param {string} status - Status to filter by
   * @returns {Array} Filtered alarms
   */
  const getAlarmsByStatus = useCallback((status) => {
    return alarms.filter(alarm => alarm.status === status)
  }, [alarms])

  /**
   * Get alarms filtered by severity (memoized for performance)
   * @param {string} severityName - Severity name to filter by
   * @returns {Array} Filtered alarms
   */
  const getAlarmsBySeverity = useCallback((severityName) => {
    return alarms.filter(alarm =>
      alarm.severity?.name?.toLowerCase() === severityName.toLowerCase()
    )
  }, [alarms])

  /**
   * Memoized alarm statistics for performance
   */
  const alarmStats = useMemo(() => {
    const stats = {
      total: alarms.length,
      byStatus: {},
      bySeverity: {},
      critical: 0,
      recent: 0
    }

    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    alarms.forEach(alarm => {
      // Count by status
      stats.byStatus[alarm.status] = (stats.byStatus[alarm.status] || 0) + 1

      // Count by severity
      if (alarm.severity?.name) {
        stats.bySeverity[alarm.severity.name] = (stats.bySeverity[alarm.severity.name] || 0) + 1
      }

      // Count critical alarms
      if (alarm.severity?.name?.toLowerCase() === 'critical') {
        stats.critical++
      }

      // Count recent alarms (last 24 hours)
      if (new Date(alarm.created_at) > oneDayAgo) {
        stats.recent++
      }
    })

    return stats
  }, [alarms])

  // shouldTriggerEscalation logic moved to shared escalation service

  /**
   * Manually trigger escalation for an alarm
   * @param {string} alarmId - Alarm notification ID
   * @returns {Object} Result with success status
   */
  const triggerEscalation = async (alarmId) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const alarm = alarms.find(a => a.id === alarmId)
      if (!alarm) {
        throw new Error('Alarm not found')
      }

      if (!alarm.building_id) {
        throw new Error('Cannot escalate alarm without building association')
      }

      const escalationResult = await triggerEscalationService(supabase, alarm)

      return { success: escalationResult.success, data: escalationResult.callOut, escalationTriggered: escalationResult.escalationTriggered }

    } catch (err) {
      console.error('Error triggering escalation:', err)
      return { success: false, error: err.message }
    }
  }

  return {
    // Data
    alarms,
    buildings,
    alarmTypes,
    severityLevels,
    loading,
    error,
    alarmStats,

    // Actions
    processAlarmWebhook,
    acknowledgeAlarm,
    resolveAlarm,
    refreshAlarms,
    triggerEscalation,

    // Utilities
    getAlarmsByStatus,
    getAlarmsBySeverity
  }
}
