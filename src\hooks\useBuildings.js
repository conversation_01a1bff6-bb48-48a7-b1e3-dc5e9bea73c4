import { useState, useEffect } from 'react'
import { supabase, hasSupabaseConfig } from '../lib/supabase'

/**
 * Custom hook for building management operations
 */
export const useBuildings = () => {
  const [buildings, setBuildings] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch all buildings
  useEffect(() => {
    const fetchBuildings = async () => {
      if (!hasSupabaseConfig) {
        setError('Supabase not configured. Please add your Supabase URL and API key to .env.local')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        
        const { data, error } = await supabase
          .from('buildings')
          .select('*')
          .order('name')

        if (error) throw error

        setBuildings(data || [])
        
      } catch (err) {
        console.error('Error fetching buildings:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchBuildings()
  }, [])

  /**
   * Generate a unique email address for a building
   * @returns {string} Generated email address
   */
  const generateUniqueEmail = async () => {
    const generateRandomId = () => {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      for (let i = 0; i < 10; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }

    let attempts = 0
    const maxAttempts = 10

    while (attempts < maxAttempts) {
      const randomId = generateRandomId()
      const email = `bldg-${randomId}@mg.stieralarms.online`
      
      // Check if email already exists
      const { data, error } = await supabase
        .from('buildings')
        .select('id')
        .eq('email_address', email)

      if (error) {
        console.error('Error checking email existence:', error)
        attempts++
        continue
      }

      // If no data or empty array, email is unique
      if (!data || data.length === 0) {
        return email
      }

      attempts++
    }

    throw new Error('Failed to generate unique email after multiple attempts')
  }

  /**
   * Create a new building
   * @param {Object} buildingData - Building data
   * @returns {Object} Result with success status
   */
  const createBuilding = async (buildingData) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Generate unique email if not provided
      if (!buildingData.email_address) {
        buildingData.email_address = await generateUniqueEmail()
      }

      const { data, error } = await supabase
        .from('buildings')
        .insert({
          name: buildingData.name,
          address: buildingData.address,
          email_address: buildingData.email_address,
          building_code: buildingData.building_code,
          contact_phone: buildingData.contact_phone,
          contact_email: buildingData.contact_email,
          is_active: buildingData.is_active !== undefined ? buildingData.is_active : true
        })
        .select()
        .single()

      if (error) throw error

      // Update local state
      setBuildings(prev => [...prev, data].sort((a, b) => a.name.localeCompare(b.name)))

      return { success: true, data }
      
    } catch (err) {
      console.error('Error creating building:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Update an existing building
   * @param {string} buildingId - Building ID
   * @param {Object} buildingData - Updated building data
   * @returns {Object} Result with success status
   */
  const updateBuilding = async (buildingId, buildingData) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { data, error } = await supabase
        .from('buildings')
        .update({
          name: buildingData.name,
          address: buildingData.address,
          email_address: buildingData.email_address,
          building_code: buildingData.building_code,
          contact_phone: buildingData.contact_phone,
          contact_email: buildingData.contact_email,
          is_active: buildingData.is_active
        })
        .eq('id', buildingId)
        .select()
        .single()

      if (error) throw error

      // Update local state
      setBuildings(prev => 
        prev.map(building => 
          building.id === buildingId ? data : building
        ).sort((a, b) => a.name.localeCompare(b.name))
      )

      return { success: true, data }
      
    } catch (err) {
      console.error('Error updating building:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Delete a building
   * @param {string} buildingId - Building ID
   * @returns {Object} Result with success status
   */
  const deleteBuilding = async (buildingId) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { error } = await supabase
        .from('buildings')
        .delete()
        .eq('id', buildingId)

      if (error) throw error

      // Update local state
      setBuildings(prev => prev.filter(building => building.id !== buildingId))

      return { success: true }
      
    } catch (err) {
      console.error('Error deleting building:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Toggle building active status
   * @param {string} buildingId - Building ID
   * @param {boolean} isActive - New active status
   * @returns {Object} Result with success status
   */
  const toggleBuildingStatus = async (buildingId, isActive) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { data, error } = await supabase
        .from('buildings')
        .update({ is_active: isActive })
        .eq('id', buildingId)
        .select()
        .single()

      if (error) throw error

      // Update local state
      setBuildings(prev => 
        prev.map(building => 
          building.id === buildingId ? data : building
        )
      )

      return { success: true, data }
      
    } catch (err) {
      console.error('Error toggling building status:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Check if email address is unique
   * @param {string} email - Email to check
   * @param {string} excludeId - Building ID to exclude from check (for updates)
   * @returns {boolean} True if email is unique
   */
  const isEmailUnique = async (email, excludeId = null) => {
    if (!hasSupabaseConfig) {
      console.warn('Supabase not configured, cannot check email uniqueness')
      return false
    }

    try {
      let query = supabase
        .from('buildings')
        .select('id')
        .eq('email_address', email)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      // Use regular query instead of .single() to avoid 406 errors
      const { data, error } = await query

      if (error) {
        console.error('Error checking email uniqueness:', error)
        return false
      }

      // Email is unique if no records are found
      return !data || data.length === 0
    } catch (err) {
      console.error('Error checking email uniqueness:', err)
      return false
    }
  }

  /**
   * Get buildings filtered by status
   * @param {boolean} isActive - Active status to filter by
   * @returns {Array} Filtered buildings
   */
  const getBuildingsByStatus = (isActive) => {
    return buildings.filter(building => building.is_active === isActive)
  }

  /**
   * Search buildings by name or code
   * @param {string} searchTerm - Search term
   * @returns {Array} Filtered buildings
   */
  const searchBuildings = (searchTerm) => {
    if (!searchTerm) return buildings

    const term = searchTerm.toLowerCase()
    return buildings.filter(building =>
      (building.name?.toLowerCase() || '').includes(term) ||
      (building.building_code?.toLowerCase() || '').includes(term) ||
      (building.address?.toLowerCase() || '').includes(term)
    )
  }

  return {
    // Data
    buildings,
    loading,
    error,
    
    // Actions
    createBuilding,
    updateBuilding,
    deleteBuilding,
    toggleBuildingStatus,
    generateUniqueEmail,
    isEmailUnique,
    
    // Utilities
    getBuildingsByStatus,
    searchBuildings
  }
}
