import { useState, useEffect } from 'react'
import { supabase, hasSupabaseConfig } from '../lib/supabase'

/**
 * Custom hook for network device management operations
 */
export const useNetworkDevices = () => {
  const [devices, setDevices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch all network devices with building information
  useEffect(() => {
    const fetchDevices = async () => {
      if (!hasSupabaseConfig) {
        setError('Supabase not configured. Please add your Supabase URL and API key to .env.local')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        
        const { data, error } = await supabase
          .from('network_devices')
          .select(`
            *,
            building:buildings(
              id,
              name,
              address,
              building_code
            )
          `)
          .order('station_name')

        if (error) throw error

        setDevices(data || [])
        
      } catch (err) {
        console.error('Error fetching network devices:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchDevices()
  }, [])

  /**
   * Create a new network device
   * @param {Object} deviceData - Device data
   * @param {boolean} debugMode - Enable detailed logging for debugging
   * @returns {Object} Result with success status and detailed error info
   */
  const createDevice = async (deviceData, debugMode = false) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Validate required fields before processing (only station_name and building_id are required)
      const requiredFields = ['building_id', 'station_name']
      const missingFields = requiredFields.filter(field => !deviceData[field] || String(deviceData[field]).trim() === '')

      if (missingFields.length > 0) {
        const error = `Missing required fields: ${missingFields.join(', ')}`
        if (debugMode) {
          console.error('Validation Error:', error)
          console.error('Device Data:', deviceData)
        }
        return { success: false, error, type: 'validation' }
      }

      // Encrypt passwords if provided
      const processedData = { ...deviceData }

      // Handle password fields - encrypt and convert to encrypted fields
      // Always delete the non-encrypted field names to prevent schema errors
      if (processedData.station_password && typeof processedData.station_password === 'string' && processedData.station_password.trim() !== '') {
        // Import encryption function dynamically to avoid circular dependencies
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.station_password_encrypted = encryptPassword(processedData.station_password)
      }
      delete processedData.station_password

      if (processedData.windows_password && typeof processedData.windows_password === 'string' && processedData.windows_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.windows_password_encrypted = encryptPassword(processedData.windows_password)
      }
      delete processedData.windows_password

      if (processedData.platform_password && typeof processedData.platform_password === 'string' && processedData.platform_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.platform_password_encrypted = encryptPassword(processedData.platform_password)
      }
      delete processedData.platform_password

      if (processedData.passphrase && typeof processedData.passphrase === 'string' && processedData.passphrase.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.passphrase_encrypted = encryptPassword(processedData.passphrase)
      }
      delete processedData.passphrase

      // Clean up empty string values for optional fields
      Object.keys(processedData).forEach(key => {
        if (processedData[key] === '') {
          processedData[key] = null
        }
      })

      if (debugMode) {
        console.log('Processed Device Data:', processedData)
      }

      const { data, error } = await supabase
        .from('network_devices')
        .insert(processedData)
        .select(`
          *,
          building:buildings(
            id,
            name,
            address,
            building_code
          )
        `)
        .single()

      if (error) {
        if (debugMode) {
          console.error('Supabase Error Details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          })
          console.error('Request Payload:', processedData)
        }

        // Categorize error types for better user feedback
        let errorType = 'database'
        let userFriendlyMessage = error.message

        if (error.code === '23505') { // Unique constraint violation
          errorType = 'constraint'
          if (error.message.includes('unique_building_host_id')) {
            userFriendlyMessage = `Host ID "${processedData.host_id}" already exists for this building`
          } else if (error.message.includes('unique_building_ip')) {
            userFriendlyMessage = `IP address "${processedData.ip_address}" already exists for this building`
          } else if (error.message.includes('host_id_key')) {
            userFriendlyMessage = `Host ID "${processedData.host_id}" already exists in the system`
          } else {
            userFriendlyMessage = 'A device with these details already exists'
          }
        } else if (error.code === '23502') { // Not null constraint violation
          errorType = 'validation'
          userFriendlyMessage = `Required field is missing: ${error.message}`
        } else if (error.code === '23503') { // Foreign key constraint violation
          errorType = 'reference'
          userFriendlyMessage = 'Invalid building reference'
        }

        throw new Error(userFriendlyMessage)
      }

      // Update local state
      setDevices(prev => [...prev, data].sort((a, b) => a.station_name.localeCompare(b.station_name)))

      return { success: true, data }

    } catch (err) {
      const errorMessage = err.message || 'Unknown error occurred'
      console.error('Error creating network device:', {
        message: errorMessage,
        deviceData: debugMode ? deviceData : 'Enable debug mode for details'
      })
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Update an existing network device
   * @param {string} deviceId - Device ID
   * @param {Object} deviceData - Updated device data
   * @returns {Object} Result with success status
   */
  const updateDevice = async (deviceId, deviceData) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      // Process passwords
      const processedData = { ...deviceData }

      // Handle password fields - encrypt and convert to encrypted fields
      // Always delete the non-encrypted field names to prevent schema errors
      if (processedData.station_password && typeof processedData.station_password === 'string' && processedData.station_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.station_password_encrypted = encryptPassword(processedData.station_password)
      }
      delete processedData.station_password

      if (processedData.windows_password && typeof processedData.windows_password === 'string' && processedData.windows_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.windows_password_encrypted = encryptPassword(processedData.windows_password)
      }
      delete processedData.windows_password

      if (processedData.platform_password && typeof processedData.platform_password === 'string' && processedData.platform_password.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.platform_password_encrypted = encryptPassword(processedData.platform_password)
      }
      delete processedData.platform_password

      if (processedData.passphrase && typeof processedData.passphrase === 'string' && processedData.passphrase.trim() !== '') {
        const { encryptPassword } = await import('../utils/passwordEncryption.js')
        processedData.passphrase_encrypted = encryptPassword(processedData.passphrase)
      }
      delete processedData.passphrase

      const { data, error } = await supabase
        .from('network_devices')
        .update(processedData)
        .eq('id', deviceId)
        .select(`
          *,
          building:buildings(
            id,
            name,
            address,
            building_code
          )
        `)
        .single()

      if (error) throw error

      // Update local state
      setDevices(prev => 
        prev.map(device => 
          device.id === deviceId ? data : device
        ).sort((a, b) => a.station_name.localeCompare(b.station_name))
      )

      return { success: true, data }
      
    } catch (err) {
      console.error('Error updating network device:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Delete a network device
   * @param {string} deviceId - Device ID
   * @returns {Object} Result with success status
   */
  const deleteDevice = async (deviceId) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { error } = await supabase
        .from('network_devices')
        .delete()
        .eq('id', deviceId)

      if (error) throw error

      // Update local state
      setDevices(prev => prev.filter(device => device.id !== deviceId))

      return { success: true }
      
    } catch (err) {
      console.error('Error deleting network device:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Toggle device active status
   * @param {string} deviceId - Device ID
   * @param {boolean} isActive - New active status
   * @returns {Object} Result with success status
   */
  const toggleDeviceStatus = async (deviceId, isActive) => {
    if (!hasSupabaseConfig) {
      return { success: false, error: 'Supabase not configured' }
    }

    try {
      const { data, error } = await supabase
        .from('network_devices')
        .update({ is_active: isActive })
        .eq('id', deviceId)
        .select(`
          *,
          building:buildings(
            id,
            name,
            address,
            building_code
          )
        `)
        .single()

      if (error) throw error

      // Update local state
      setDevices(prev => 
        prev.map(device => 
          device.id === deviceId ? data : device
        )
      )

      return { success: true, data }
      
    } catch (err) {
      console.error('Error toggling device status:', err)
      return { success: false, error: err.message }
    }
  }

  /**
   * Check if host ID is unique within a building
   * @param {string} hostId - Host ID to check
   * @param {string} buildingId - Building ID
   * @param {string} excludeId - Device ID to exclude from check (for updates)
   * @returns {boolean} True if unique
   */
  const isHostIdUnique = async (hostId, buildingId, excludeId = null) => {
    if (!hasSupabaseConfig) {
      return false
    }

    try {
      let query = supabase
        .from('network_devices')
        .select('id')
        .eq('host_id', hostId)
        .eq('building_id', buildingId)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error checking host ID uniqueness:', error)
        return false
      }

      return !data || data.length === 0
    } catch (err) {
      console.error('Error checking host ID uniqueness:', err)
      return false
    }
  }

  /**
   * Check if IP address is unique within a building
   * @param {string} ipAddress - IP address to check
   * @param {string} buildingId - Building ID
   * @param {string} excludeId - Device ID to exclude from check (for updates)
   * @returns {boolean} True if unique
   */
  const isIpAddressUnique = async (ipAddress, buildingId, excludeId = null) => {
    if (!hasSupabaseConfig) {
      return false
    }

    try {
      let query = supabase
        .from('network_devices')
        .select('id')
        .eq('ip_address', ipAddress)
        .eq('building_id', buildingId)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error checking IP address uniqueness:', error)
        return false
      }

      return !data || data.length === 0
    } catch (err) {
      console.error('Error checking IP address uniqueness:', err)
      return false
    }
  }

  /**
   * Get devices filtered by building
   * @param {string} buildingId - Building ID
   * @returns {Array} Filtered devices
   */
  const getDevicesByBuilding = (buildingId) => {
    return devices.filter(device => device.building_id === buildingId)
  }

  /**
   * Get devices filtered by type
   * @param {string} deviceType - Device type
   * @returns {Array} Filtered devices
   */
  const getDevicesByType = (deviceType) => {
    return devices.filter(device => device.device_type === deviceType)
  }

  /**
   * Get devices filtered by status
   * @param {boolean} isActive - Active status
   * @returns {Array} Filtered devices
   */
  const getDevicesByStatus = (isActive) => {
    return devices.filter(device => device.is_active === isActive)
  }

  /**
   * Search devices by name, host ID, or IP address
   * @param {string} searchTerm - Search term
   * @returns {Array} Filtered devices
   */
  const searchDevices = (searchTerm) => {
    if (!searchTerm) return devices

    const term = searchTerm.toLowerCase()
    return devices.filter(device =>
      (device.station_name?.toLowerCase() || '').includes(term) ||
      (device.host_id?.toLowerCase() || '').includes(term) ||
      (device.ip_address || '').includes(term) ||
      (device.device_type?.toLowerCase() || '').includes(term) ||
      (device.building?.name?.toLowerCase() || '').includes(term)
    )
  }

  /**
   * Validate a batch of devices for uniqueness conflicts
   * @param {Array} deviceList - Array of device objects to validate
   * @returns {Array} Array of validation errors
   */
  const validateDeviceBatch = async (deviceList) => {
    const errors = []
    const hostIdMap = new Map()
    const ipAddressMap = new Map()

    // Check for duplicates within the batch
    deviceList.forEach((device, index) => {
      const rowNumber = index + 1

      // Check host ID duplicates within batch (only if host_id is provided)
      if (device.host_id && device.host_id.trim() !== '') {
        const key = `${device.building_id}-${device.host_id}`
        if (hostIdMap.has(key)) {
          errors.push(`Row ${rowNumber}: Host ID "${device.host_id}" is duplicated in the import (also in row ${hostIdMap.get(key)})`)
        } else {
          hostIdMap.set(key, rowNumber)
        }
      }

      // Check IP address duplicates within batch (only if ip_address is provided)
      if (device.ip_address && device.ip_address.trim() !== '') {
        const key = `${device.building_id}-${device.ip_address}`
        if (ipAddressMap.has(key)) {
          errors.push(`Row ${rowNumber}: IP address "${device.ip_address}" is duplicated in the import (also in row ${ipAddressMap.get(key)})`)
        } else {
          ipAddressMap.set(key, rowNumber)
        }
      }
    })

    // Check for conflicts with existing devices in database (only for provided values)
    for (let i = 0; i < deviceList.length; i++) {
      const device = deviceList[i]
      const rowNumber = i + 1

      if (device.host_id && device.host_id.trim() !== '' && device.building_id) {
        const isUnique = await isHostIdUnique(device.host_id, device.building_id)
        if (!isUnique) {
          errors.push(`Row ${rowNumber}: Host ID "${device.host_id}" already exists for this building`)
        }
      }

      if (device.ip_address && device.ip_address.trim() !== '' && device.building_id) {
        const isUnique = await isIpAddressUnique(device.ip_address, device.building_id)
        if (!isUnique) {
          errors.push(`Row ${rowNumber}: IP address "${device.ip_address}" already exists for this building`)
        }
      }
    }

    return errors
  }

  return {
    // Data
    devices,
    loading,
    error,

    // Actions
    createDevice,
    updateDevice,
    deleteDevice,
    toggleDeviceStatus,
    isHostIdUnique,
    isIpAddressUnique,
    validateDeviceBatch,

    // Utilities
    getDevicesByBuilding,
    getDevicesByType,
    getDevicesByStatus,
    searchDevices
  }
}
