import { useState, useEffect, useCallback } from 'react'
import { supabase, hasSupabaseConfig } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { fetchDemoData } from '../lib/demoData'

export const useUserProfile = () => {
  const { user } = useAuth()
  const [profile, setProfile] = useState(null)
  const [preferences, setPreferences] = useState(null)
  const [notificationPreferences, setNotificationPreferences] = useState(null)
  const [userRoles, setUserRoles] = useState([])
  const [activity, setActivity] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Helper function to create default user preferences
  const createDefaultPreferences = async (userId) => {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .insert({
          user_id: userId,
          default_view: 'bms',
          view_mode_preferences: {},
          theme: 'light',
          timezone: 'UTC',
          date_format: 'MM/DD/YYYY',
          time_format: '12h',
          temperature_unit: 'fahrenheit',
          measurement_unit: 'imperial',
          auto_refresh_interval: 30,
          show_tooltips: true,
          compact_mode: false
        })
        .select()
        .single()

      if (error) throw error
      return data
    } catch (err) {
      console.error('Error creating default preferences:', err)
      return null
    }
  }

  // Helper function to create default notification preferences
  const createDefaultNotificationPreferences = async (userId) => {
    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .insert({
          user_id: userId,
          email_enabled: true,
          email_critical: true,
          email_high: true,
          email_medium: false,
          email_low: false,
          email_digest: true,
          email_digest_frequency: 'daily',
          in_app_enabled: true,
          in_app_sound: true,
          in_app_desktop: true,
          sms_enabled: false,
          sms_critical: false,
          sms_high: false,
          quiet_hours_enabled: false,
          quiet_hours_start: '22:00',
          quiet_hours_end: '06:00',
          weekend_notifications: true
        })
        .select()
        .single()

      if (error) throw error
      return data
    } catch (err) {
      console.error('Error creating default notification preferences:', err)
      return null
    }
  }

  // Fetch user profile data
  const fetchProfile = useCallback(async () => {
    if (!hasSupabaseConfig || !user?.id) {
      // Use demo data when Supabase is not configured
      try {
        setLoading(true)
        setError(null)

        const [profileData, preferencesData, notificationData, rolesData, activityData] = await Promise.all([
          fetchDemoData('profile'),
          fetchDemoData('preferences'),
          fetchDemoData('notifications'),
          fetchDemoData('roles'),
          fetchDemoData('activity')
        ])

        setProfile(profileData)
        setPreferences(preferencesData)
        setNotificationPreferences(notificationData)
        setUserRoles(rolesData)
        setActivity(activityData)
      } catch (err) {
        console.error('Error loading demo data:', err)
        setError('Failed to load demo data')
      } finally {
        setLoading(false)
      }
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Fetch user profile
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle()

      if (profileError) {
        throw profileError
      }

      // Fetch user preferences (create default if not exists)
      let { data: preferencesData, error: preferencesError } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle()

      if (preferencesError) {
        throw preferencesError
      }

      // Create default preferences if none exist
      if (!preferencesData) {
        preferencesData = await createDefaultPreferences(user.id)
      }

      // Fetch notification preferences (create default if not exists)
      let { data: notificationData, error: notificationError } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle()

      if (notificationError) {
        throw notificationError
      }

      // Create default notification preferences if none exist
      if (!notificationData) {
        notificationData = await createDefaultNotificationPreferences(user.id)
      }

      // Fetch user roles
      const { data: rolesData, error: rolesError } = await supabase
        .from('user_roles')
        .select(`
          *,
          buildings:building_id (
            id,
            name,
            address
          )
        `)
        .eq('user_id', user.id)
        .eq('is_active', true)

      if (rolesError) {
        throw rolesError
      }

      // Fetch recent activity
      const { data: activityData, error: activityError } = await supabase
        .from('user_activity')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10)

      if (activityError) {
        throw activityError
      }

      setProfile(profileData)
      setPreferences(preferencesData)
      setNotificationPreferences(notificationData)
      setUserRoles(rolesData || [])
      setActivity(activityData || [])

    } catch (err) {
      console.error('Error fetching user profile:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [user?.id])

  // Update user profile
  const updateProfile = async (updates) => {
    if (!hasSupabaseConfig || !user?.id) {
      // Simulate update in demo mode
      try {
        await new Promise(resolve => setTimeout(resolve, 500)) // Simulate network delay
        const updatedProfile = { ...profile, ...updates, updated_at: new Date().toISOString() }
        setProfile(updatedProfile)
        return { data: updatedProfile, error: null }
      } catch {
        return { data: null, error: { message: 'Demo mode update failed' } }
      }
    }

    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: user.id,
          ...updates,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
        .select()
        .maybeSingle()

      if (error) throw error

      setProfile(data)
      return { data, error: null }
    } catch (err) {
      console.error('Error updating profile:', err)
      return { data: null, error: err }
    }
  }

  // Update user preferences
  const updatePreferences = async (updates) => {
    if (!hasSupabaseConfig || !user?.id) {
      // Simulate update in demo mode
      try {
        await new Promise(resolve => setTimeout(resolve, 500)) // Simulate network delay
        const updatedPreferences = { ...preferences, ...updates, updated_at: new Date().toISOString() }
        setPreferences(updatedPreferences)
        return { data: updatedPreferences, error: null }
      } catch {
        return { data: null, error: { message: 'Demo mode update failed' } }
      }
    }

    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          ...updates,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
        .select()
        .maybeSingle()

      if (error) throw error

      setPreferences(data)
      return { data, error: null }
    } catch (err) {
      console.error('Error updating preferences:', err)
      return { data: null, error: err }
    }
  }

  // Update notification preferences
  const updateNotificationPreferences = async (updates) => {
    if (!hasSupabaseConfig || !user?.id) {
      // Simulate update in demo mode
      try {
        await new Promise(resolve => setTimeout(resolve, 500)) // Simulate network delay
        const updatedNotifications = { ...notificationPreferences, ...updates, updated_at: new Date().toISOString() }
        setNotificationPreferences(updatedNotifications)
        return { data: updatedNotifications, error: null }
      } catch {
        return { data: null, error: { message: 'Demo mode update failed' } }
      }
    }

    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: user.id,
          ...updates,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })
        .select()
        .maybeSingle()

      if (error) throw error

      setNotificationPreferences(data)
      return { data, error: null }
    } catch (err) {
      console.error('Error updating notification preferences:', err)
      return { data: null, error: err }
    }
  }

  // Log user activity
  const logActivity = async (activityType, description, metadata = {}) => {
    if (!hasSupabaseConfig || !user?.id) {
      return
    }

    try {
      await supabase
        .from('user_activity')
        .insert({
          user_id: user.id,
          activity_type: activityType,
          activity_description: description,
          metadata
        })
    } catch (err) {
      console.error('Error logging activity:', err)
    }
  }

  useEffect(() => {
    fetchProfile()
  }, [user?.id, fetchProfile])

  return {
    profile,
    preferences,
    notificationPreferences,
    userRoles,
    activity,
    loading,
    error,
    updateProfile,
    updatePreferences,
    updateNotificationPreferences,
    logActivity,
    refetch: fetchProfile
  }
}
