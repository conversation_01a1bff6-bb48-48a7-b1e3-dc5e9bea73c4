import { describe, it, expect } from 'vitest'
import {
  parseAlarmEmail,
  extractBuildingAlarmId,
  formatAlarmNotification,
  getSeverityColor,
  getStatusColor,
  isHighPriorityAlarm
} from '../alarmUtils'

describe('alarmUtils', () => {
  describe('parseAlarmEmail', () => {
    const mockEmailData = {
      subject: 'Fire Alarm - Building A',
      body: 'Fire alarm detected in Building A\nBuilding Alarm ID: FIRE-001\nLocation: First Floor',
      from: '<EMAIL>',
      timestamp: '2024-01-15T10:30:00Z'
    }

    it('should parse email data correctly', () => {
      const result = parseAlarmEmail(mockEmailData)
      
      expect(result.subject).toBe('Fire Alarm - Building A')
      expect(result.message).toBe('Fire alarm detected in Building A\nBuilding Alarm ID: FIRE-001\nLocation: First Floor')
      expect(result.sender_email).toBe('<EMAIL>')
      expect(result.received_at).toBe('2024-01-15T10:30:00Z')
    })

    it('should handle missing fields gracefully', () => {
      const incompleteData = { subject: 'Test' }
      const result = parseAlarmEmail(incompleteData)
      
      expect(result.subject).toBe('Test')
      expect(result.message).toBe('')
      expect(result.sender_email).toBe('')
    })
  })

  describe('extractBuildingAlarmId', () => {
    it('should extract alarm ID from various formats', () => {
      const testCases = [
        {
          text: 'Building Alarm ID: FIRE-001',
          expected: 'FIRE-001'
        },
        {
          text: 'Alarm ID: SEC-123\nOther text',
          expected: 'SEC-123'
        },
        {
          text: 'ID: HVAC-456',
          expected: 'HVAC-456'
        },
        {
          text: 'No alarm ID here',
          expected: null
        }
      ]

      testCases.forEach(({ text, expected }) => {
        expect(extractBuildingAlarmId(text)).toBe(expected)
      })
    })

    it('should handle empty or null input', () => {
      expect(extractBuildingAlarmId('')).toBe(null)
      expect(extractBuildingAlarmId(null)).toBe(null)
      expect(extractBuildingAlarmId(undefined)).toBe(null)
    })
  })

  describe('formatAlarmNotification', () => {
    const mockAlarm = {
      id: '123',
      subject: 'Fire Alarm',
      message: 'Fire detected',
      status: 'received',
      severity: { name: 'HIGH', level: 3 },
      alarm_type: { name: 'Fire' },
      building: { name: 'Building A' },
      received_at: '2024-01-15T10:30:00Z'
    }

    it('should format alarm notification correctly', () => {
      const result = formatAlarmNotification(mockAlarm)
      
      expect(result.id).toBe('123')
      expect(result.subject).toBe('Fire Alarm')
      expect(result.severityName).toBe('HIGH')
      expect(result.severityLevel).toBe(3)
      expect(result.typeName).toBe('Fire')
      expect(result.buildingName).toBe('Building A')
      expect(result.statusColor).toBeDefined()
      expect(result.severityColor).toBeDefined()
    })

    it('should handle missing nested objects', () => {
      const incompleteAlarm = {
        id: '123',
        subject: 'Test Alarm',
        status: 'received'
      }
      
      const result = formatAlarmNotification(incompleteAlarm)
      
      expect(result.id).toBe('123')
      expect(result.severityName).toBe('Unknown')
      expect(result.typeName).toBe('Unknown')
      expect(result.buildingName).toBe('Unknown')
    })
  })

  describe('getSeverityColor', () => {
    it('should return correct colors for severity levels', () => {
      expect(getSeverityColor('LOW')).toBe('#10B981') // green
      expect(getSeverityColor('MEDIUM')).toBe('#F59E0B') // yellow
      expect(getSeverityColor('HIGH')).toBe('#EF4444') // red
      expect(getSeverityColor('CRITICAL')).toBe('#DC2626') // dark red
      expect(getSeverityColor('EMERGENCY')).toBe('#7C2D12') // very dark red
      expect(getSeverityColor('UNKNOWN')).toBe('#6B7280') // gray
    })
  })

  describe('getStatusColor', () => {
    it('should return correct colors for status values', () => {
      expect(getStatusColor('received')).toBe('#3B82F6') // blue
      expect(getStatusColor('acknowledged')).toBe('#10B981') // green
      expect(getStatusColor('resolved')).toBe('#6B7280') // gray
      expect(getStatusColor('cancelled')).toBe('#EF4444') // red
      expect(getStatusColor('unknown')).toBe('#6B7280') // gray
    })
  })

  describe('isHighPriorityAlarm', () => {
    it('should identify high priority alarms correctly', () => {
      const highPriorityAlarm = {
        severity: { name: 'CRITICAL' },
        alarm_type: { name: 'Fire' }
      }
      
      const lowPriorityAlarm = {
        severity: { name: 'LOW' },
        alarm_type: { name: 'Maintenance' }
      }
      
      expect(isHighPriorityAlarm(highPriorityAlarm)).toBe(true)
      expect(isHighPriorityAlarm(lowPriorityAlarm)).toBe(false)
    })

    it('should handle missing severity or type', () => {
      const incompleteAlarm = {
        severity: { name: 'HIGH' }
        // missing alarm_type
      }
      
      expect(isHighPriorityAlarm(incompleteAlarm)).toBe(false)
    })

    it('should identify emergency types as high priority', () => {
      const emergencyTypes = ['Fire', 'Security', 'Medical', 'Gas Leak']
      
      emergencyTypes.forEach(type => {
        const alarm = {
          severity: { name: 'MEDIUM' },
          alarm_type: { name: type }
        }
        expect(isHighPriorityAlarm(alarm)).toBe(true)
      })
    })
  })
})
