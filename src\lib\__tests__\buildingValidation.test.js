import { describe, it, expect } from 'vitest'
import {
  isValidEmail,
  isValidPhone,
  isValidBuildingCode,
  validateBuildingForm,
  cleanBuildingFormData,
  formatPhoneNumber,
  generateBuildingCodeSuggestion,
  validateBuildingEmail
} from '../buildingValidation'

describe('buildingValidation', () => {
  describe('isValidEmail', () => {
    it('should validate correct email formats', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email formats', () => {
      expect(isValidEmail('invalid-email')).toBe(false)
      expect(isValidEmail('test@')).toBe(false)
      expect(isValidEmail('@example.com')).toBe(false)
      expect(isValidEmail('')).toBe(false)
    })
  })

  describe('isValidPhone', () => {
    it('should validate correct phone formats', () => {
      expect(isValidPhone('(*************')).toBe(true)
      expect(isValidPhone('************')).toBe(true)
      expect(isValidPhone('************')).toBe(true)
      expect(isValidPhone('5551234567')).toBe(true)
    })

    it('should reject invalid phone formats', () => {
      expect(isValidPhone('123')).toBe(false)
      expect(isValidPhone('abc-def-ghij')).toBe(false)
      expect(isValidPhone('')).toBe(false)
    })
  })

  describe('isValidBuildingCode', () => {
    it('should validate correct building codes', () => {
      expect(isValidBuildingCode('ABC123')).toBe(true)
      expect(isValidBuildingCode('MOB-001')).toBe(true)
      expect(isValidBuildingCode('BLDG_A1')).toBe(true)
    })

    it('should reject invalid building codes', () => {
      expect(isValidBuildingCode('A')).toBe(false) // too short
      expect(isValidBuildingCode('A'.repeat(21))).toBe(false) // too long
      expect(isValidBuildingCode('ABC@123')).toBe(false) // invalid characters
      expect(isValidBuildingCode('')).toBe(false)
    })
  })

  describe('validateBuildingForm', () => {
    const validFormData = {
      name: 'Test Building',
      address: '123 Main St, City, State 12345',
      email_address: '<EMAIL>',
      building_code: 'TEST001',
      contact_phone: '(*************',
      contact_email: '<EMAIL>'
    }

    it('should validate correct form data', () => {
      const result = validateBuildingForm(validFormData)
      expect(result.isValid).toBe(true)
      expect(Object.keys(result.errors)).toHaveLength(0)
    })

    it('should require building name', () => {
      const result = validateBuildingForm({ ...validFormData, name: '' })
      expect(result.isValid).toBe(false)
      expect(result.errors.name).toBeDefined()
    })

    it('should require address', () => {
      const result = validateBuildingForm({ ...validFormData, address: '' })
      expect(result.isValid).toBe(false)
      expect(result.errors.address).toBeDefined()
    })

    it('should require email address', () => {
      const result = validateBuildingForm({ ...validFormData, email_address: '' })
      expect(result.isValid).toBe(false)
      expect(result.errors.email_address).toBeDefined()
    })

    it('should validate email domain', () => {
      const result = validateBuildingForm({ 
        ...validFormData, 
        email_address: '<EMAIL>' 
      })
      expect(result.isValid).toBe(false)
      expect(result.errors.email_address).toContain('mg.stieralarms.online')
    })
  })

  describe('cleanBuildingFormData', () => {
    it('should clean and normalize form data', () => {
      const dirtyData = {
        name: '  Test Building  ',
        address: '  123 Main St  ',
        email_address: '  <EMAIL>  ',
        building_code: '  test001  ',
        contact_phone: '  (*************  ',
        contact_email: '  <EMAIL>  '
      }

      const cleaned = cleanBuildingFormData(dirtyData)
      
      expect(cleaned.name).toBe('Test Building')
      expect(cleaned.address).toBe('123 Main St')
      expect(cleaned.email_address).toBe('<EMAIL>')
      expect(cleaned.building_code).toBe('TEST001')
      expect(cleaned.contact_phone).toBe('(*************')
      expect(cleaned.contact_email).toBe('<EMAIL>')
    })
  })

  describe('formatPhoneNumber', () => {
    it('should format 10-digit numbers', () => {
      expect(formatPhoneNumber('5551234567')).toBe('(*************')
      expect(formatPhoneNumber('************')).toBe('(*************')
    })

    it('should format 11-digit numbers with country code', () => {
      expect(formatPhoneNumber('15551234567')).toBe('+1 (*************')
    })

    it('should return original for invalid formats', () => {
      expect(formatPhoneNumber('123')).toBe('123')
      expect(formatPhoneNumber('')).toBe('')
    })
  })

  describe('generateBuildingCodeSuggestion', () => {
    it('should generate codes from building names', () => {
      expect(generateBuildingCodeSuggestion('Main Office Building')).toBe('MAIOFFBU')
      expect(generateBuildingCodeSuggestion('Tower A')).toBe('TOWA')
      expect(generateBuildingCodeSuggestion('Building 123')).toBe('BUI123')
    })

    it('should handle edge cases', () => {
      expect(generateBuildingCodeSuggestion('')).toBe('')
      expect(generateBuildingCodeSuggestion('A')).toBe('A')
      expect(generateBuildingCodeSuggestion('Very Long Building Name That Exceeds Limits')).toHaveLength(8)
    })
  })

  describe('validateBuildingEmail', () => {
    it('should validate correct building email format', () => {
      const result = validateBuildingEmail('<EMAIL>')
      expect(result.isValid).toBe(true)
    })

    it('should reject invalid building email formats', () => {
      const result = validateBuildingEmail('<EMAIL>')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('pattern')
    })

    it('should reject wrong domain', () => {
      const result = validateBuildingEmail('<EMAIL>')
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('mg.stieralarms.online')
    })
  })
})
