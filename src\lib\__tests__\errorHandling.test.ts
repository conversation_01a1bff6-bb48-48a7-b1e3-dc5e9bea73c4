import { describe, it, expect, vi, beforeEach } from 'vitest'
import { 
  AppError, 
  withRetry, 
  isRetryableError, 
  normalizeError, 
  handleApiOperation 
} from '../errorHandling'

describe('errorHandling', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('AppError', () => {
    it('should create AppError with correct properties', () => {
      const context = { operation: 'test', component: 'TestComponent' }
      const error = new AppError('Test error', 'TEST_ERROR', context, true, 'User message')

      expect(error.message).toBe('Test error')
      expect(error.code).toBe('TEST_ERROR')
      expect(error.context).toEqual(context)
      expect(error.isRetryable).toBe(true)
      expect(error.userMessage).toBe('User message')
    })

    it('should generate default user message for known error codes', () => {
      const context = { operation: 'test' }
      const error = new AppError('Network failed', 'NETWORK_ERROR', context)

      expect(error.userMessage).toBe('Network connection failed. Please check your internet connection and try again.')
    })

    it('should use default message for unknown error codes', () => {
      const context = { operation: 'test' }
      const error = new AppError('Unknown error', 'UNKNOWN_CODE', context)

      expect(error.userMessage).toBe('An unexpected error occurred. Please try again.')
    })
  })

  describe('withRetry', () => {
    it('should succeed on first attempt', async () => {
      const operation = vi.fn().mockResolvedValue('success')
      const context = { operation: 'test' }

      const result = await withRetry(operation, {}, context)

      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(1)
    })

    it('should retry on retryable errors', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('network error'))
        .mockResolvedValue('success')
      
      const context = { operation: 'test' }

      const result = await withRetry(operation, { maxAttempts: 3, baseDelay: 10 }, context)

      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(2)
    })

    it('should not retry on non-retryable errors', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('validation error'))
      const context = { operation: 'test' }

      await expect(withRetry(operation, { maxAttempts: 3 }, context)).rejects.toThrow('validation error')
      expect(operation).toHaveBeenCalledTimes(1)
    })

    it('should respect maxAttempts limit', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('network error'))
      const context = { operation: 'test' }

      await expect(withRetry(operation, { maxAttempts: 2, baseDelay: 10 }, context)).rejects.toThrow('network error')
      expect(operation).toHaveBeenCalledTimes(2)
    })

    it('should use custom retry condition', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('custom error'))
      const context = { operation: 'test' }
      const retryCondition = vi.fn().mockReturnValue(false)

      await expect(withRetry(operation, { retryCondition }, context)).rejects.toThrow('custom error')
      expect(operation).toHaveBeenCalledTimes(1)
      expect(retryCondition).toHaveBeenCalledWith(expect.any(Error))
    })
  })

  describe('isRetryableError', () => {
    it('should identify network errors as retryable', () => {
      expect(isRetryableError(new Error('fetch failed'))).toBe(true)
      expect(isRetryableError(new Error('network timeout'))).toBe(true)
    })

    it('should identify timeout errors as retryable', () => {
      expect(isRetryableError(new Error('request timeout'))).toBe(true)
    })

    it('should identify rate limit errors as retryable', () => {
      expect(isRetryableError(new Error('rate limit exceeded'))).toBe(true)
      expect(isRetryableError(new Error('429 Too Many Requests'))).toBe(true)
    })

    it('should identify server errors as retryable', () => {
      expect(isRetryableError(new Error('500 Internal Server Error'))).toBe(true)
      expect(isRetryableError(new Error('502 Bad Gateway'))).toBe(true)
      expect(isRetryableError(new Error('503 Service Unavailable'))).toBe(true)
      expect(isRetryableError(new Error('504 Gateway Timeout'))).toBe(true)
    })

    it('should identify Supabase connection errors as retryable', () => {
      expect(isRetryableError(new Error('connection refused'))).toBe(true)
      expect(isRetryableError(new Error('PGRST error'))).toBe(true)
    })

    it('should not identify validation errors as retryable', () => {
      expect(isRetryableError(new Error('validation failed'))).toBe(false)
      expect(isRetryableError(new Error('invalid input'))).toBe(false)
    })

    it('should not identify auth errors as retryable', () => {
      expect(isRetryableError(new Error('unauthorized'))).toBe(false)
      expect(isRetryableError(new Error('auth failed'))).toBe(false)
    })
  })

  describe('normalizeError', () => {
    it('should return AppError as-is', () => {
      const context = { operation: 'test' }
      const appError = new AppError('test', 'TEST_ERROR', context)
      
      const result = normalizeError(appError, context)
      
      expect(result).toBe(appError)
    })

    it('should categorize auth errors correctly', () => {
      const context = { operation: 'test' }
      const error = new Error('unauthorized access')
      
      const result = normalizeError(error, context)
      
      expect(result.code).toBe('AUTH_ERROR')
      expect(result.isRetryable).toBe(false)
    })

    it('should categorize network errors correctly', () => {
      const context = { operation: 'test' }
      const error = new Error('network connection failed')
      
      const result = normalizeError(error, context)
      
      expect(result.code).toBe('NETWORK_ERROR')
      expect(result.isRetryable).toBe(true)
    })

    it('should categorize validation errors correctly', () => {
      const context = { operation: 'test' }
      const error = new Error('validation failed')
      
      const result = normalizeError(error, context)
      
      expect(result.code).toBe('VALIDATION_ERROR')
      expect(result.isRetryable).toBe(false)
    })

    it('should categorize Retell AI errors correctly', () => {
      const context = { operation: 'test' }
      const error = new Error('retell api error')
      
      const result = normalizeError(error, context)
      
      expect(result.code).toBe('RETELL_API_ERROR')
      expect(result.isRetryable).toBe(true)
    })

    it('should default to UNKNOWN_ERROR for unrecognized errors', () => {
      const context = { operation: 'test' }
      const error = new Error('some random error')
      
      const result = normalizeError(error, context)
      
      expect(result.code).toBe('UNKNOWN_ERROR')
    })
  })

  describe('handleApiOperation', () => {
    it('should return data on successful operation', async () => {
      const operation = vi.fn().mockResolvedValue('success')
      const context = { operation: 'test' }

      const result = await handleApiOperation(operation, context)

      expect(result.data).toBe('success')
      expect(result.error).toBe(null)
    })

    it('should return normalized error on failed operation', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('network error'))
      const context = { operation: 'test' }

      const result = await handleApiOperation(operation, context)

      expect(result.data).toBe(null)
      expect(result.error).toBeInstanceOf(AppError)
      expect(result.error?.code).toBe('NETWORK_ERROR')
    })

    it('should pass retry options to withRetry', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('network error'))
        .mockResolvedValue('success')
      
      const context = { operation: 'test' }
      const retryOptions = { maxAttempts: 2, baseDelay: 10 }

      const result = await handleApiOperation(operation, context, retryOptions)

      expect(result.data).toBe('success')
      expect(result.error).toBe(null)
      expect(operation).toHaveBeenCalledTimes(2)
    })
  })
})
