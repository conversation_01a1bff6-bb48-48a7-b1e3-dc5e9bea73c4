/**
 * Utility functions for parsing and processing alarm emails
 */

/**
 * Parse alarm email content to extract structured data
 * @param {Object} emailData - Email data object with subject, body, from, timestamp
 * @returns {Object} Parsed alarm data
 */
export function parseAlarmEmail(emailData) {
  const {
    subject = '',
    body = '',
    from = '',
    timestamp = '',
    sender,
    'body-plain': bodyPlain,
    'body-html': bodyHtml,
    'message-headers': messageHeaders,
    signature,
    token
  } = emailData;

  // Handle both test format and webhook format
  const actualSubject = subject || emailData.subject || '';
  const actualBody = body || bodyPlain || '';
  const actualFrom = from || sender || '';
  const actualTimestamp = timestamp || emailData.timestamp || '';

  // Extract message ID from headers if available
  const messageId = extractMessageId(messageHeaders);

  // Parse alarm details from email body
  const alarmDetails = parseAlarmContent(actualBody);

  return {
    subject: actualSubject,
    message: actualBody,
    sender_email: actualFrom,
    received_at: actualTimestamp,
    senderEmail: actualFrom, // Keep backward compatibility
    recipientEmail: emailData.recipient,
    messageId,
    bodyPlain: actualBody,
    bodyHtml: bodyHtml,
    webhookSignature: signature,
    webhookTimestamp: actualTimestamp,
    webhookToken: token,
    rawWebhookData: emailData,
    ...alarmDetails
  };
}

/**
 * Extract message ID from email headers
 * @param {Array} messageHeaders - Array of header arrays
 * @returns {string|null} Message ID
 */
function extractMessageId(messageHeaders) {
  if (!Array.isArray(messageHeaders)) return null;
  
  const messageIdHeader = messageHeaders.find(header => 
    Array.isArray(header) && header[0] === 'Message-Id'
  );
  
  return messageIdHeader ? messageIdHeader[1] : null;
}

/**
 * Parse alarm content from email body text
 * @param {string} bodyText - Plain text email body
 * @returns {Object} Parsed alarm details
 */
function parseAlarmContent(bodyText) {
  const details = {
    alarmTime: null,
    alarmType: null,
    severity: null,
    alarmDetails: null,
    locationDetails: null,
    buildingAlarmId: null
  };

  if (!bodyText) return details;

  // Normalize line breaks and clean up the text
  const normalizedText = bodyText
    .replace(/\r\n/g, ' ')
    .replace(/\r/g, ' ')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Parse building alarm ID - look for various patterns that building systems might use
  const alarmIdPatterns = [
    /Building\s*Alarm\s*ID:\s*([A-Za-z0-9\-_]+)/i, // "Building Alarm ID: xxx"
    /Alarm\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Event\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Reference\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
    /Incident\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
    /Alert\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Notification\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /System\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /ID:\s*([A-Za-z0-9\-_]{4,})/i, // Generic ID pattern with minimum 4 characters
    /#([A-Za-z0-9\-_]{4,})/i, // Hash-prefixed ID
    /\[([A-Za-z0-9\-_]{4,})\]/i // Bracket-enclosed ID
  ];

  for (const pattern of alarmIdPatterns) {
    const idMatch = normalizedText.match(pattern);
    if (idMatch && idMatch[1]) {
      details.buildingAlarmId = idMatch[1].trim();
      break;
    }
  }

  // Parse time - look for patterns like "Time: 4/27/2025, 11:16:06 PM EST"
  const timeMatch = normalizedText.match(/Time:\s*([^A-Za-z]*(?:AM|PM)?[^A-Za-z]*(?:EST|CST|MST|PST|EDT|CDT|MDT|PDT)?)/i);
  if (timeMatch) {
    const timeStr = timeMatch[1].trim();
    const parsedDate = parseAlarmTime(timeStr);
    if (parsedDate) {
      details.alarmTime = parsedDate.toISOString();
    }
  }

  // Parse alarm type - look for patterns like "Alarm Type: Fire Detection System"
  const typeMatch = normalizedText.match(/Alarm Type:\s*([^A-Za-z]*[A-Za-z][^:]*?)(?:\s+(?:Severity|Details|Location|$))/i);
  if (typeMatch) {
    details.alarmType = typeMatch[1].trim();
  }

  // Parse severity - look for patterns like "Severity: CRITICAL"
  const severityMatch = normalizedText.match(/Severity:\s*(CRITICAL|HIGH|MEDIUM|LOW|EMERGENCY|WARNING|INFO|ALERT)/i);
  if (severityMatch) {
    details.severity = severityMatch[1].trim().toUpperCase();
  }

  // Parse details - look for patterns like "Details: Smoke detectors have been triggered..."
  const detailsMatch = normalizedText.match(/Details:\s*(.+?)(?:\s*(?:---|\*This email|$))/i);
  if (detailsMatch) {
    details.alarmDetails = detailsMatch[1].trim();
  }

  // Extract location information with improved patterns
  const locationPatterns = [
    /Location:\s*(.+?)(?:\s*(?:Details|Time|Alarm Type|Severity|---|\*This email|$))/i,
    /(?:in the|at the|location:?\s*)([^.]+(?:building|room|floor|area|zone|facility|site)[^.]*)/i,
    /(?:triggered in|detected in|alarm in)\s*([^.]+)/i
  ];

  for (const pattern of locationPatterns) {
    const locationMatch = normalizedText.match(pattern);
    if (locationMatch && locationMatch[1].trim().length > 1) {
      details.locationDetails = locationMatch[1].trim();
      break;
    }
  }

  return details;
}

/**
 * Extract building alarm ID from text content
 * @param {string} text - Text content to search for alarm ID
 * @returns {string|null} Extracted alarm ID or null if not found
 */
export function extractBuildingAlarmId(text) {
  if (!text || typeof text !== 'string') return null;

  // Normalize line breaks and clean up the text
  const normalizedText = text
    .replace(/\r\n/g, ' ')
    .replace(/\r/g, ' ')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Parse building alarm ID - look for various patterns that building systems might use
  const alarmIdPatterns = [
    /Building\s*Alarm\s*ID:\s*([A-Za-z0-9\-_]+)/i, // "Building Alarm ID: xxx"
    /Alarm\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Event\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Reference\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
    /Incident\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
    /Alert\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Notification\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /System\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /ID:\s*([A-Za-z0-9\-_]{4,})/i, // Generic ID pattern with minimum 4 characters
    /#([A-Za-z0-9\-_]{4,})/i, // Hash-prefixed ID
    /\[([A-Za-z0-9\-_]{4,})\]/i // Bracket-enclosed ID
  ];

  for (const pattern of alarmIdPatterns) {
    const idMatch = normalizedText.match(pattern);
    if (idMatch && idMatch[1]) {
      return idMatch[1].trim();
    }
  }

  return null;
}

/**
 * Parse alarm time string into Date object
 * @param {string} timeStr - Time string from alarm email
 * @returns {Date|null} Parsed date or null if parsing fails
 */
function parseAlarmTime(timeStr) {
  try {
    // Handle various time formats
    // Example: "4/27/2025, 11:16:06 PM EST"

    if (!timeStr || typeof timeStr !== 'string') {
      console.warn('Invalid time string provided:', timeStr);
      return null;
    }

    // Clean up the time string - remove extra content that might be concatenated
    let cleanTimeStr = timeStr;

    // If the string contains "Alarm Type:" or other alarm content, extract just the time part
    const timeOnlyMatch = timeStr.match(/^([^A-Za-z]*(?:AM|PM)?[^A-Za-z]*(?:EST|CST|MST|PST|EDT|CDT|MDT|PDT)?)/i);
    if (timeOnlyMatch) {
      cleanTimeStr = timeOnlyMatch[1].trim();
    }

    // Remove timezone abbreviations and normalize
    let normalizedTime = cleanTimeStr
      .replace(/\s+(EST|CST|MST|PST|EDT|CDT|MDT|PDT)\s*$/i, '')
      .trim();

    // Handle edge case where time string might be empty after cleaning
    if (!normalizedTime) {
      console.warn('Time string became empty after normalization:', timeStr);
      return null;
    }

    // Try to parse the date
    const date = new Date(normalizedTime);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Failed to parse alarm time after normalization:', {
        original: timeStr,
        cleaned: cleanTimeStr,
        normalized: normalizedTime
      });
      return null;
    }

    return date;
  } catch (error) {
    console.error('Error parsing alarm time:', error, 'Input:', timeStr);
    return null;
  }
}

/**
 * Find building by email address
 * @param {string} emailAddress - Building email address
 * @param {Array} buildings - Array of building objects
 * @returns {Object|null} Building object or null if not found
 */
export function findBuildingByEmail(emailAddress, buildings) {
  if (!emailAddress || !Array.isArray(buildings)) return null;
  
  return buildings.find(building => 
    building.email_address === emailAddress
  ) || null;
}

/**
 * Find alarm type by name
 * @param {string} typeName - Alarm type name
 * @param {Array} alarmTypes - Array of alarm type objects
 * @returns {Object|null} Alarm type object or null if not found
 */
export function findAlarmTypeByName(typeName, alarmTypes) {
  if (!typeName || !Array.isArray(alarmTypes)) return null;
  
  return alarmTypes.find(type => 
    type.name.toLowerCase() === typeName.toLowerCase()
  ) || null;
}

/**
 * Find severity level by name
 * @param {string} severityName - Severity level name
 * @param {Array} severityLevels - Array of severity level objects
 * @returns {Object|null} Severity level object or null if not found
 */
export function findSeverityByName(severityName, severityLevels) {
  if (!severityName || !Array.isArray(severityLevels)) return null;
  
  return severityLevels.find(level => 
    level.name.toLowerCase() === severityName.toLowerCase()
  ) || null;
}

/**
 * Validate webhook signature (placeholder for Mailgun signature verification)
 * @param {string} signature - Webhook signature
 * @param {string} timestamp - Webhook timestamp
 * @param {string} token - Webhook token
 * @returns {boolean} True if signature is valid
 */
export function validateWebhookSignature(signature, timestamp, token) {
  // TODO: Implement actual Mailgun signature verification
  // This would require the Mailgun API key and proper HMAC verification
  // For now, we'll accept all webhooks but log them for security review
  
  console.log('Webhook signature validation (placeholder):', {
    signature: signature?.substring(0, 10) + '...',
    timestamp,
    token: token?.substring(0, 10) + '...'
  });
  
  return true; // Placeholder - implement proper validation in production
}

/**
 * Format alarm notification for display
 * @param {Object} alarm - Alarm notification object
 * @returns {Object} Formatted notification data
 */
export function formatAlarmNotification(alarm) {
  if (!alarm) return {};

  return {
    ...alarm,
    id: alarm.id,
    subject: alarm.subject,
    severityName: alarm.severity?.name || 'Unknown',
    severityLevel: alarm.severity?.level || 0,
    typeName: alarm.alarm_type?.name || 'Unknown',
    buildingName: alarm.building?.name || 'Unknown',
    statusColor: getStatusColor(alarm.status),
    severityColor: getSeverityColor(alarm.severity?.name || 'UNKNOWN'),
    formattedTime: alarm.alarm_time
      ? new Date(alarm.alarm_time).toLocaleString()
      : 'Unknown',
    formattedCreatedAt: alarm.created_at
      ? new Date(alarm.created_at).toLocaleString()
      : 'Unknown'
  };
}

/**
 * Get color for alarm status
 * @param {string} status - Alarm status
 * @returns {string} Color code
 */
export function getStatusColor(status) {
  const statusColors = {
    'received': '#3B82F6', // blue
    'acknowledged': '#10B981', // green
    'resolved': '#6B7280', // gray
    'cancelled': '#EF4444', // red
    'in_progress': '#3b82f6',
    'false_alarm': '#8b5cf6'
  };

  return statusColors[status] || '#6B7280'; // gray as default
}

/**
 * Get color for alarm severity
 * @param {string} severity - Alarm severity level
 * @returns {string} Color code
 */
export function getSeverityColor(severity) {
  const severityColors = {
    'LOW': '#10B981', // green
    'MEDIUM': '#F59E0B', // yellow
    'HIGH': '#EF4444', // red
    'CRITICAL': '#DC2626', // dark red
    'EMERGENCY': '#7C2D12', // very dark red
    'UNKNOWN': '#6B7280' // gray
  };

  return severityColors[severity?.toUpperCase()] || '#6B7280'; // gray as default
}

/**
 * Check if an alarm is high priority
 * @param {Object} alarm - Alarm object with severity and alarm_type
 * @returns {boolean} True if alarm is high priority
 */
export function isHighPriorityAlarm(alarm) {
  if (!alarm || !alarm.severity || !alarm.alarm_type) {
    return false;
  }

  const severityName = alarm.severity.name?.toUpperCase();
  const typeName = alarm.alarm_type.name;

  // High priority severity levels
  const highPrioritySeverities = ['HIGH', 'CRITICAL', 'EMERGENCY'];

  // Emergency alarm types that are always high priority
  const emergencyTypes = ['Fire', 'Security', 'Medical', 'Gas Leak'];

  return (
    highPrioritySeverities.includes(severityName) ||
    emergencyTypes.includes(typeName)
  );
}
