/**
 * Building form validation utilities
 */

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if email is valid
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate phone number format
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if phone is valid
 */
export function isValidPhone(phone) {
  // Allow various phone formats: (*************, ************, ************, 1234567890
  const phoneRegex = /^[+]?[1-9]?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

/**
 * Validate building code format
 * @param {string} code - Building code to validate
 * @returns {boolean} True if code is valid
 */
export function isValidBuildingCode(code) {
  // Allow alphanumeric codes with hyphens and underscores, 2-20 characters
  const codeRegex = /^[A-Za-z0-9\-_]{2,20}$/
  return codeRegex.test(code)
}

/**
 * Validate building form data
 * @param {Object} formData - Form data to validate
 * @param {boolean} isUpdate - Whether this is an update operation
 * @returns {Object} Validation result with errors
 */
export function validateBuildingForm(formData) {
  const errors = {}

  // Building name validation
  if (!formData.name || formData.name.trim().length === 0) {
    errors.name = 'Building name is required'
  } else if (formData.name.trim().length < 2) {
    errors.name = 'Building name must be at least 2 characters'
  } else if (formData.name.trim().length > 255) {
    errors.name = 'Building name must be less than 255 characters'
  }

  // Address validation
  if (!formData.address || formData.address.trim().length === 0) {
    errors.address = 'Address is required'
  } else if (formData.address.trim().length < 5) {
    errors.address = 'Address must be at least 5 characters'
  } else if (formData.address.trim().length > 500) {
    errors.address = 'Address must be less than 500 characters'
  }

  // Email address validation
  if (!formData.email_address || formData.email_address.trim().length === 0) {
    errors.email_address = 'Email address is required'
  } else if (!isValidEmail(formData.email_address)) {
    errors.email_address = 'Please enter a valid email address'
  } else if (!formData.email_address.includes('@mg.stieralarms.online')) {
    errors.email_address = 'Email must be from the mg.stieralarms.online domain'
  }

  // Building code validation (optional but must be valid if provided)
  if (formData.building_code && formData.building_code.trim().length > 0) {
    if (!isValidBuildingCode(formData.building_code)) {
      errors.building_code = 'Building code must be 2-20 alphanumeric characters (hyphens and underscores allowed)'
    }
  }

  // Contact phone validation (optional but must be valid if provided)
  if (formData.contact_phone && formData.contact_phone.trim().length > 0) {
    if (!isValidPhone(formData.contact_phone)) {
      errors.contact_phone = 'Please enter a valid phone number'
    }
  }

  // Contact email validation (optional but must be valid if provided)
  if (formData.contact_email && formData.contact_email.trim().length > 0) {
    if (!isValidEmail(formData.contact_email)) {
      errors.contact_email = 'Please enter a valid contact email address'
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * Format phone number for display
 * @param {string} phone - Phone number to format
 * @returns {string} Formatted phone number
 */
export function formatPhoneNumber(phone) {
  if (!phone) return ''
  
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Format as (XXX) XXX-XXXX
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`
  }
  
  return phone // Return original if can't format
}

/**
 * Clean and normalize form data
 * @param {Object} formData - Raw form data
 * @returns {Object} Cleaned form data
 */
export function cleanBuildingFormData(formData) {
  return {
    name: formData.name?.trim() || '',
    address: formData.address?.trim() || '',
    email_address: formData.email_address?.trim().toLowerCase() || '',
    building_code: formData.building_code?.trim().toUpperCase() || '',
    contact_phone: formData.contact_phone?.trim() || '',
    contact_email: formData.contact_email?.trim().toLowerCase() || '',
    is_active: formData.is_active !== undefined ? formData.is_active : true
  }
}

/**
 * Generate building code suggestion from name
 * @param {string} name - Building name
 * @returns {string} Suggested building code
 */
export function generateBuildingCodeSuggestion(name) {
  if (!name) return ''
  
  // Take first 3 letters of each word, max 8 characters
  const words = name.trim().split(/\s+/)
  let code = ''
  
  for (const word of words) {
    if (code.length >= 8) break
    const cleanWord = word.replace(/[^A-Za-z0-9]/g, '')
    if (cleanWord.length > 0) {
      code += cleanWord.slice(0, 3).toUpperCase()
    }
  }
  
  // Ensure minimum length of 2
  if (code.length < 2) {
    code = name.replace(/[^A-Za-z0-9]/g, '').slice(0, 8).toUpperCase()
  }
  
  return code.slice(0, 8) // Max 8 characters
}

/**
 * Validate building email uniqueness format
 * @param {string} email - Email to validate
 * @returns {Object} Validation result
 */
export function validateBuildingEmail(email) {
  if (!email) {
    return { isValid: false, error: 'Email is required' }
  }

  if (!isValidEmail(email)) {
    return { isValid: false, error: 'Invalid email format' }
  }

  if (!email.includes('@mg.stieralarms.online')) {
    return { isValid: false, error: 'Email must be from mg.stieralarms.online domain' }
  }

  // Check if it follows the building email pattern
  const buildingEmailRegex = /^bldg-[a-z0-9]{8,12}@mg\.stieralarms\.online$/
  if (!buildingEmailRegex.test(email)) {
    return { 
      isValid: false, 
      error: 'Email should follow the pattern: bldg-[id]@mg.stieralarms.online' 
    }
  }

  return { isValid: true }
}

/**
 * Auto-save form data to localStorage
 * @param {string} formId - Unique form identifier
 * @param {Object} formData - Form data to save
 */
export function autoSaveFormData(formId, formData) {
  try {
    const key = `building_form_${formId}`
    localStorage.setItem(key, JSON.stringify({
      data: formData,
      timestamp: Date.now()
    }))
  } catch (error) {
    console.warn('Failed to auto-save form data:', error)
  }
}

/**
 * Load auto-saved form data from localStorage
 * @param {string} formId - Unique form identifier
 * @param {number} maxAge - Maximum age in milliseconds (default: 1 hour)
 * @returns {Object|null} Saved form data or null
 */
export function loadAutoSavedFormData(formId, maxAge = 3600000) {
  try {
    const key = `building_form_${formId}`
    const saved = localStorage.getItem(key)
    
    if (!saved) return null
    
    const { data, timestamp } = JSON.parse(saved)
    
    // Check if data is not too old
    if (Date.now() - timestamp > maxAge) {
      localStorage.removeItem(key)
      return null
    }
    
    return data
  } catch (error) {
    console.warn('Failed to load auto-saved form data:', error)
    return null
  }
}

/**
 * Clear auto-saved form data
 * @param {string} formId - Unique form identifier
 */
export function clearAutoSavedFormData(formId) {
  try {
    const key = `building_form_${formId}`
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to clear auto-saved form data:', error)
  }
}
