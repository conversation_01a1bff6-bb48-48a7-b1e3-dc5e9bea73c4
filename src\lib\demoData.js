// Demo data for user profile features when Supabase is not configured
export const demoUserProfile = {
  id: 'demo-user-id',
  user_id: 'demo-user-id',
  email: '<EMAIL>',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  display_name: '<PERSON>',
  phone: '(*************',
  department: 'Facilities Management',
  job_title: 'Senior Facility Manager',
  employee_id: 'FM001',
  emergency_contact_name: '<PERSON>',
  emergency_contact_phone: '(*************',
  emergency_contact_relationship: 'Spouse',
  certifications: [
    'Certified Facility Manager (CFM)',
    'HVAC Technician Certification',
    'Building Automation Systems'
  ],
  building_assignments: [
    'Main Office Building',
    'Warehouse Complex',
    'Data Center'
  ],
  last_login_at: new Date().toISOString(),
  login_count: 127,
  two_factor_enabled: false,
  is_active: true,
  created_at: '2023-01-15T10:00:00Z',
  updated_at: new Date().toISOString()
}

export const demoUserPreferences = {
  id: 'demo-prefs-id',
  user_id: 'demo-user-id',
  default_view: 'bms',
  dashboard_layout: {
    widgets: ['alarms', 'energy', 'equipment'],
    layout: 'grid'
  },
  widgets_config: {
    alarms: { position: 1, size: 'large' },
    energy: { position: 2, size: 'medium' },
    equipment: { position: 3, size: 'small' }
  },
  view_mode_preferences: {
    equipment: 'card',
    workorders: 'table',
    alarms: 'card',
    energy: 'summary'
  },
  theme: 'light',
  timezone: 'America/New_York',
  date_format: 'MM/DD/YYYY',
  time_format: '12h',
  temperature_unit: 'fahrenheit',
  measurement_unit: 'imperial',
  auto_refresh_interval: 30,
  show_tooltips: true,
  compact_mode: false,
  created_at: '2023-01-15T10:00:00Z',
  updated_at: new Date().toISOString()
}

export const demoNotificationPreferences = {
  id: 'demo-notif-id',
  user_id: 'demo-user-id',
  email_enabled: true,
  email_critical: true,
  email_high: true,
  email_medium: false,
  email_low: false,
  email_digest: true,
  email_digest_frequency: 'daily',
  in_app_enabled: true,
  in_app_sound: true,
  in_app_desktop: true,
  sms_enabled: false,
  sms_critical: false,
  sms_high: false,
  phone_number: '',
  quiet_hours_enabled: true,
  quiet_hours_start: '22:00',
  quiet_hours_end: '06:00',
  weekend_notifications: true,
  created_at: '2023-01-15T10:00:00Z',
  updated_at: new Date().toISOString()
}

export const demoUserRoles = [
  {
    id: 'demo-role-1',
    user_id: 'demo-user-id',
    building_id: 'demo-building-1',
    role_name: 'manager',
    permissions: {
      view_alarms: true,
      acknowledge_alarms: true,
      manage_equipment: true,
      view_reports: true,
      manage_users: false
    },
    is_active: true,
    buildings: {
      id: 'demo-building-1',
      name: 'Main Office Building',
      address: '123 Business Ave, City, ST 12345'
    },
    created_at: '2023-01-15T10:00:00Z',
    updated_at: new Date().toISOString()
  },
  {
    id: 'demo-role-2',
    user_id: 'demo-user-id',
    building_id: 'demo-building-2',
    role_name: 'operator',
    permissions: {
      view_alarms: true,
      acknowledge_alarms: true,
      manage_equipment: false,
      view_reports: true,
      manage_users: false
    },
    is_active: true,
    buildings: {
      id: 'demo-building-2',
      name: 'Warehouse Complex',
      address: '456 Industrial Blvd, City, ST 12345'
    },
    created_at: '2023-02-01T10:00:00Z',
    updated_at: new Date().toISOString()
  }
]

export const demoUserActivity = [
  {
    id: 'demo-activity-1',
    user_id: 'demo-user-id',
    activity_type: 'login',
    activity_description: 'User logged in successfully',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    metadata: { location: 'Office Network' },
    created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30 minutes ago
  },
  {
    id: 'demo-activity-2',
    user_id: 'demo-user-id',
    activity_type: 'alarm_acknowledge',
    activity_description: 'Acknowledged critical HVAC alarm in Building A',
    metadata: { alarm_id: 'ALM-001', building: 'Main Office Building' },
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2 hours ago
  },
  {
    id: 'demo-activity-3',
    user_id: 'demo-user-id',
    activity_type: 'profile_update',
    activity_description: 'Updated profile information',
    metadata: { fields_changed: ['phone', 'emergency_contact'] },
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1 day ago
  },
  {
    id: 'demo-activity-4',
    user_id: 'demo-user-id',
    activity_type: 'settings_update',
    activity_description: 'Updated notification preferences',
    metadata: { section: 'notifications' },
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString() // 2 days ago
  },
  {
    id: 'demo-activity-5',
    user_id: 'demo-user-id',
    activity_type: 'work_order_created',
    activity_description: 'Created work order for HVAC maintenance',
    metadata: { work_order_id: 'WO-2024-001', priority: 'high' },
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString() // 3 days ago
  }
]

// Function to get demo data based on data type
export const getDemoData = (dataType) => {
  switch (dataType) {
    case 'profile':
      return demoUserProfile
    case 'preferences':
      return demoUserPreferences
    case 'notifications':
      return demoNotificationPreferences
    case 'roles':
      return demoUserRoles
    case 'activity':
      return demoUserActivity
    default:
      return null
  }
}

// Function to simulate async data fetching
export const fetchDemoData = async (dataType, delay = 500) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(getDemoData(dataType))
    }, delay)
  })
}
