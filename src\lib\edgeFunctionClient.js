/**
 * Client for interacting with Supabase Edge Functions
 * Specifically for the Mailgun webhook handler
 */

import { supabase as _supabase } from './supabase'

// Edge Function configuration
const EDGE_FUNCTION_NAME = 'mailgun-webhook-handler'



/**
 * Get the webhook URL for the Edge Function
 * This can be used to configure Mailgun webhooks
 * 
 * @returns {string} The webhook URL
 */
export function getWebhookUrl() {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
  if (!supabaseUrl) {
    throw new Error('VITE_SUPABASE_URL environment variable is not set')
  }
  
  return `${supabaseUrl}/functions/v1/${EDGE_FUNCTION_NAME}`
}

/**
 * Validate that the Edge Function is deployed and accessible
 * 
 * @returns {Object} Validation result
 */
export async function validateEdgeFunction() {
  try {
    // Make a simple OPTIONS request to check if the function exists
    const response = await fetch(getWebhookUrl(), {
      method: 'OPTIONS',
      headers: {
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type',
      },
    })

    if (response.ok) {
      return {
        success: true,
        message: 'Edge Function is accessible',
        url: getWebhookUrl()
      }
    } else {
      return {
        success: false,
        error: `Edge Function returned status ${response.status}`,
        url: getWebhookUrl()
      }
    }
  } catch (error) {
    return {
      success: false,
      error: `Failed to reach Edge Function: ${error.message}`,
      url: getWebhookUrl()
    }
  }
}





/**
 * Get deployment information for the Edge Function
 * 
 * @returns {Object} Deployment information
 */
export function getDeploymentInfo() {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
  const projectRef = supabaseUrl ? supabaseUrl.split('//')[1].split('.')[0] : 'unknown'

  return {
    functionName: EDGE_FUNCTION_NAME,
    webhookUrl: getWebhookUrl(),
    projectRef,
    deploymentCommands: {
      deploy: `supabase functions deploy ${EDGE_FUNCTION_NAME}`,
      logs: `supabase functions logs ${EDGE_FUNCTION_NAME}`
    },
    mailgunConfiguration: {
      webhookUrl: getWebhookUrl(),
      eventType: 'Incoming Messages',
      httpMethod: 'POST'
    }
  }
}
