/**
 * Error Filter Utility
 * 
 * This utility helps filter out browser extension-related errors that flood the console
 * while preserving legitimate application errors for debugging.
 */

// Common browser extension error patterns to filter out
const EXTENSION_ERROR_PATTERNS = [
  // Chrome extension message passing errors
  /A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received/,
  /Unchecked runtime\.lastError/,
  /Extension context invalidated/,
  /Could not establish connection\. Receiving end does not exist/,
  /The message port closed before a response was received/,
  
  // Chrome extension content script errors
  /chrome-extension:\/\//,
  /moz-extension:\/\//,
  /safari-extension:\/\//,
  /ms-browser-extension:\/\//,
  
  // Common extension-related errors
  /Non-Error promise rejection captured/,
  /Script error\./,
  /ResizeObserver loop limit exceeded/,
  
  // Ad blocker and privacy extension errors
  /blocked by client/,
  /net::ERR_BLOCKED_BY_CLIENT/,
  /uBlock Origin/,
  /AdBlock/,
  /Ghostery/,
  
  // Social media extension errors
  /facebook\.com/,
  /twitter\.com/,
  /instagram\.com/,
  /pinterest\.com/,
  
  // Password manager extension errors
  /lastpass/i,
  /1password/i,
  /bitwarden/i,
  /dashlane/i,
  
  // Developer tool extension errors
  /react-devtools/,
  /redux-devtools/,
  /vue-devtools/,
  
  // Generic extension patterns
  /chrome\.runtime/,
  /browser\.runtime/,
  /webkitURL is deprecated/,
  /webkitRequestFileSystem is deprecated/
]

// Application-specific error patterns that should NOT be filtered
const APPLICATION_ERROR_PATTERNS = [
  /supabase/i,
  /localhost/,
  /vite/i,
  /react/i,
  /JSC Alarm Call-Out App/,
  /alarm/i,
  /webhook/i,
  /profile/i,
  /authentication/i,
  /database/i,
  // Google Maps API errors that should be shown (legitimate application errors)
  /google.*maps/i,
  /ApiNotActivatedMapError/i,
  /InvalidKeyMapError/i,
  /RefererNotAllowedMapError/i,
  /QuotaExceededError/i,
  /RequestDeniedError/i,
  /places.*api/i,
  /geocoding/i,
  /autocomplete/i
]

/**
 * Check if an error message should be filtered out (is likely from a browser extension)
 * @param {string|Error} error - The error message or Error object
 * @returns {boolean} - True if the error should be filtered out
 */
export function shouldFilterError(error) {
  const errorMessage = error instanceof Error ? error.message : String(error)
  const errorStack = error instanceof Error ? error.stack : ''
  const fullErrorText = `${errorMessage} ${errorStack}`.toLowerCase()
  
  // Don't filter application-specific errors
  for (const pattern of APPLICATION_ERROR_PATTERNS) {
    if (pattern.test(fullErrorText)) {
      return false
    }
  }
  
  // Filter out extension-related errors
  for (const pattern of EXTENSION_ERROR_PATTERNS) {
    if (pattern.test(fullErrorText)) {
      return true
    }
  }
  
  return false
}

// Store original console methods globally to prevent recursion
let originalConsoleError = console.error
let originalConsoleWarn = console.warn
let isInitialized = false

/**
 * Enhanced console.error that filters out browser extension errors
 * @param {...any} args - Arguments to log
 */
export function filteredConsoleError(...args) {
  const firstArg = args[0]

  if (shouldFilterError(firstArg)) {
    // Increment counter
    if (typeof window !== 'undefined') {
      window._filteredErrorsCount = (window._filteredErrorsCount || 0) + 1
    }

    // Optionally log filtered errors to a separate debug channel
    if (import.meta.env.MODE === 'development' && window.DEBUG_EXTENSION_ERRORS) {
      console.debug('[FILTERED EXTENSION ERROR]:', ...args)
    }
    return
  }

  // Log legitimate errors using original console method
  originalConsoleError.apply(console, args)
}

/**
 * Enhanced console.warn that filters out browser extension warnings
 * @param {...any} args - Arguments to log
 */
export function filteredConsoleWarn(...args) {
  const firstArg = args[0]

  if (shouldFilterError(firstArg)) {
    // Increment counter
    if (typeof window !== 'undefined') {
      window._filteredWarningsCount = (window._filteredWarningsCount || 0) + 1
    }

    if (import.meta.env.MODE === 'development' && window.DEBUG_EXTENSION_ERRORS) {
      console.debug('[FILTERED EXTENSION WARNING]:', ...args)
    }
    return
  }

  // Log legitimate warnings using original console method
  originalConsoleWarn.apply(console, args)
}

/**
 * Initialize error filtering for the application
 * This should be called early in the application lifecycle
 */
export function initializeErrorFiltering() {
  // Prevent multiple initializations
  if (isInitialized) {
    console.warn('Error filtering already initialized. Skipping...')
    return
  }

  // Update global references to original console methods (in case they were already overridden)
  originalConsoleError = console.error
  originalConsoleWarn = console.warn

  // Override console methods with filtered versions
  console.error = filteredConsoleError
  console.warn = filteredConsoleWarn

  // Mark as initialized
  isInitialized = true

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    if (shouldFilterError(event.reason)) {
      // Increment counter
      if (typeof window !== 'undefined') {
        window._filteredRejectionsCount = (window._filteredRejectionsCount || 0) + 1
      }

      event.preventDefault() // Prevent the error from being logged

      if (import.meta.env.MODE === 'development' && window.DEBUG_EXTENSION_ERRORS) {
        console.debug('[FILTERED UNHANDLED REJECTION]:', event.reason)
      }
    }
  })

  // Handle global errors
  window.addEventListener('error', (event) => {
    if (shouldFilterError(event.error || event.message)) {
      // Increment counter
      if (typeof window !== 'undefined') {
        window._filteredErrorsCount = (window._filteredErrorsCount || 0) + 1
      }

      event.preventDefault() // Prevent the error from being logged

      if (import.meta.env.MODE === 'development' && window.DEBUG_EXTENSION_ERRORS) {
        console.debug('[FILTERED GLOBAL ERROR]:', event.error || event.message)
      }
    }
  })

  // Provide a way to restore original console methods if needed
  window.restoreConsole = () => {
    console.error = originalConsoleError
    console.warn = originalConsoleWarn
    isInitialized = false
    console.log('🔧 Console methods restored to original state')
  }

  // Emergency disable function
  window.disableErrorFiltering = () => {
    window.restoreConsole()
    console.log('🚨 Error filtering disabled due to emergency')
  }
  
  // Provide a way to enable extension error debugging
  window.enableExtensionErrorDebugging = () => {
    window.DEBUG_EXTENSION_ERRORS = true
    console.log('Extension error debugging enabled. Filtered errors will be logged with [FILTERED] prefix.')
  }
  
  // Provide a way to disable extension error debugging
  window.disableExtensionErrorDebugging = () => {
    window.DEBUG_EXTENSION_ERRORS = false
    console.log('Extension error debugging disabled.')
  }
  
  console.log('Error filtering initialized. Use enableExtensionErrorDebugging() to see filtered errors.')
}

/**
 * Check if the current environment has problematic extensions
 * @returns {Object} Information about detected extensions
 */
export function detectProblematicExtensions() {
  const detectedExtensions = []
  
  // Check for common extension indicators
  if (window.chrome && window.chrome.runtime) {
    detectedExtensions.push('Chrome Extension API detected')
  }
  
  if (window.browser && window.browser.runtime) {
    detectedExtensions.push('WebExtensions API detected')
  }
  
  // Check for specific extension modifications
  if (document.querySelector('[data-lastpass-icon-root]')) {
    detectedExtensions.push('LastPass')
  }
  
  if (document.querySelector('[data-bitwarden-watching]')) {
    detectedExtensions.push('Bitwarden')
  }
  
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    detectedExtensions.push('React DevTools')
  }
  
  if (window.__REDUX_DEVTOOLS_EXTENSION__) {
    detectedExtensions.push('Redux DevTools')
  }
  
  return {
    hasExtensions: detectedExtensions.length > 0,
    extensions: detectedExtensions,
    recommendation: detectedExtensions.length > 0 
      ? 'Browser extensions detected. Error filtering is recommended to reduce console noise.'
      : 'No problematic extensions detected.'
  }
}

/**
 * Get statistics about filtered errors
 * @returns {Object} Error filtering statistics
 */
export function getErrorFilteringStats() {
  return {
    filteredErrorsCount: window._filteredErrorsCount || 0,
    filteredWarningsCount: window._filteredWarningsCount || 0,
    filteredRejectionsCount: window._filteredRejectionsCount || 0,
    isActive: typeof window.restoreConsole === 'function'
  }
}

// Initialize counters
if (typeof window !== 'undefined') {
  window._filteredErrorsCount = 0
  window._filteredWarningsCount = 0
  window._filteredRejectionsCount = 0
}
