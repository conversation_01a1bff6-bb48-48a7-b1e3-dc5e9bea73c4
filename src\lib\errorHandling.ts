/**
 * Enhanced Error Handling Utilities for JSC Alarm Call-Out App
 * Provides consistent error handling, retry logic, and user feedback
 */

import { captureError } from './sentry'

export interface RetryOptions {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
  retryCondition?: (error: Error) => boolean
}

export interface ErrorContext {
  operation: string
  component?: string
  userId?: string
  metadata?: Record<string, unknown>
}

export class AppError extends Error {
  public readonly code: string
  public readonly context: ErrorContext
  public readonly isRetryable: boolean
  public readonly userMessage: string

  constructor(
    message: string,
    code: string,
    context: ErrorContext,
    isRetryable = false,
    userMessage?: string
  ) {
    super(message)
    this.name = 'AppError'
    this.code = code
    this.context = context
    this.isRetryable = isRetryable
    this.userMessage = userMessage || this.getDefaultUserMessage(code)
  }

  private getDefaultUserMessage(code: string): string {
    const messages: Record<string, string> = {
      'NETWORK_ERROR': 'Network connection failed. Please check your internet connection and try again.',
      'AUTH_ERROR': 'Authentication failed. Please log in again.',
      'VALIDATION_ERROR': 'Invalid data provided. Please check your input and try again.',
      'PERMISSION_ERROR': 'You do not have permission to perform this action.',
      'RATE_LIMIT_ERROR': 'Too many requests. Please wait a moment and try again.',
      'SERVER_ERROR': 'Server error occurred. Please try again later.',
      'RETELL_API_ERROR': 'Voice calling service is temporarily unavailable.',
      'SUPABASE_ERROR': 'Database operation failed. Please try again.',
      'UNKNOWN_ERROR': 'An unexpected error occurred. Please try again.'
    }
    return messages[code] || messages['UNKNOWN_ERROR']
  }
}

/**
 * Exponential backoff retry utility
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {},
  context: ErrorContext
): Promise<T> {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    retryCondition = (error: Error) => isRetryableError(error)
  } = options

  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      // Log the attempt
      console.warn(`Attempt ${attempt}/${maxAttempts} failed for ${context.operation}:`, error)
      
      // Don't retry on last attempt or if error is not retryable
      if (attempt === maxAttempts || !retryCondition(lastError)) {
        break
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  // Capture error for monitoring
  captureError(lastError, {
    ...context,
    attempts: maxAttempts,
    finalAttempt: true
  })
  
  throw lastError
}

/**
 * Determine if an error is retryable
 */
export function isRetryableError(error: Error): boolean {
  // Network errors
  if (error.message.includes('fetch') || error.message.includes('network')) {
    return true
  }
  
  // Timeout errors
  if (error.message.includes('timeout')) {
    return true
  }
  
  // Rate limiting
  if (error.message.includes('rate limit') || error.message.includes('429')) {
    return true
  }
  
  // Server errors (5xx)
  if (error.message.includes('500') || error.message.includes('502') || 
      error.message.includes('503') || error.message.includes('504')) {
    return true
  }
  
  // Supabase connection errors
  if (error.message.includes('connection') || error.message.includes('PGRST')) {
    return true
  }
  
  return false
}

/**
 * Standardized error handler for API operations
 */
export async function handleApiOperation<T>(
  operation: () => Promise<T>,
  context: ErrorContext,
  retryOptions?: Partial<RetryOptions>
): Promise<{ data: T | null; error: AppError | null }> {
  try {
    const data = await withRetry(operation, retryOptions, context)
    return { data, error: null }
  } catch (error) {
    const appError = normalizeError(error as Error, context)
    return { data: null, error: appError }
  }
}

/**
 * Convert any error to AppError with proper categorization
 */
export function normalizeError(error: Error, context: ErrorContext): AppError {
  // Already an AppError
  if (error instanceof AppError) {
    return error
  }
  
  // Categorize error by message content
  let code = 'UNKNOWN_ERROR'
  let isRetryable = false
  
  if (error.message.includes('auth') || error.message.includes('unauthorized')) {
    code = 'AUTH_ERROR'
  } else if (error.message.includes('permission') || error.message.includes('forbidden')) {
    code = 'PERMISSION_ERROR'
  } else if (error.message.includes('validation') || error.message.includes('invalid')) {
    code = 'VALIDATION_ERROR'
  } else if (error.message.includes('network') || error.message.includes('fetch')) {
    code = 'NETWORK_ERROR'
    isRetryable = true
  } else if (error.message.includes('rate limit') || error.message.includes('429')) {
    code = 'RATE_LIMIT_ERROR'
    isRetryable = true
  } else if (error.message.includes('retell') || error.message.includes('voice')) {
    code = 'RETELL_API_ERROR'
    isRetryable = true
  } else if (error.message.includes('supabase') || error.message.includes('postgres')) {
    code = 'SUPABASE_ERROR'
    isRetryable = true
  } else if (error.message.includes('500') || error.message.includes('server')) {
    code = 'SERVER_ERROR'
    isRetryable = true
  }
  
  return new AppError(
    error.message,
    code,
    context,
    isRetryable
  )
}

/**
 * React hook for handling async operations with consistent error handling
 */
export function useAsyncOperation<T>() {
  const [state, setState] = React.useState<{
    data: T | null
    loading: boolean
    error: AppError | null
  }>({
    data: null,
    loading: false,
    error: null
  })

  const execute = React.useCallback(async (
    operation: () => Promise<T>,
    context: ErrorContext,
    retryOptions?: Partial<RetryOptions>
  ) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const result = await handleApiOperation(operation, context, retryOptions)
    
    setState({
      data: result.data,
      loading: false,
      error: result.error
    })
    
    return result
  }, [])

  const reset = React.useCallback(() => {
    setState({ data: null, loading: false, error: null })
  }, [])

  return { ...state, execute, reset }
}

// Re-export React for the hook
import React from 'react'
