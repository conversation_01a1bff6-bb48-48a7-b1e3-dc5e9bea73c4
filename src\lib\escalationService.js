/**
 * Escalation Service for Alarm Call-Outs
 * Manages the escalation logic and contact sequences
 */

import { supabase } from './supabase'
import retellAI from './retellAI'

class EscalationService {
  constructor() {
    this.maxEscalationLevel = parseInt(import.meta.env.MAX_ESCALATION_LEVEL) || 3
    this.escalationTimeoutMinutes = parseInt(import.meta.env.ESCALATION_TIMEOUT_MINUTES) || 5
    this.maxRetryAttempts = parseInt(import.meta.env.MAX_RETRY_ATTEMPTS) || 2
  }

  /**
   * Get escalation contacts for a building, ordered by priority
   * @param {string} buildingId - Building UUID
   * @param {boolean} activeOnly - Only return active contacts
   * @returns {Promise<Array>} Escalation contacts
   */
  async getEscalationContacts(buildingId, activeOnly = true) {
    try {
      let query = supabase
        .from('escalation_contacts')
        .select('*')
        .eq('building_id', buildingId)
        .order('priority_level', { ascending: true })

      if (activeOnly) {
        query = query.eq('is_active', true)
      }

      const { data, error } = await query

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('Error fetching escalation contacts:', error)
      throw error
    }
  }

  /**
   * Check if a contact is available based on their available hours
   * @param {Object} contact - Escalation contact
   * @param {Date} currentTime - Current time (defaults to now)
   * @returns {boolean} Whether contact is available
   */
  isContactAvailable(contact, currentTime = new Date()) {
    try {
      const availableHours = contact.available_hours || {}
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      const currentDay = dayNames[currentTime.getDay()]
      const currentTimeStr = currentTime.toTimeString().slice(0, 5) // HH:MM format

      const daySchedule = availableHours[currentDay]
      if (!daySchedule) return true // If no schedule defined, assume available

      const { start, end } = daySchedule
      if (!start || !end) return true // If no times defined, assume available

      // Simple time comparison (assumes same timezone)
      return currentTimeStr >= start && currentTimeStr <= end
    } catch (error) {
      console.error('Error checking contact availability:', error)
      return true // Default to available if error
    }
  }

  /**
   * Start escalation process for an alarm
   * @param {Object} alarm - Alarm notification object
   * @returns {Promise<Object>} Call-out record
   */
  async startEscalation(alarm) {
    try {
      console.log('Starting escalation for alarm:', alarm.id)

      // Get escalation contacts for the building
      const contacts = await this.getEscalationContacts(alarm.building_id)
      
      if (contacts.length === 0) {
        throw new Error(`No escalation contacts found for building ${alarm.building_id}`)
      }

      // Find the first available contact
      let availableContact = contacts.find(contact => this.isContactAvailable(contact))

      if (!availableContact) {
        console.warn('No contacts available at current time, using first contact anyway')
        availableContact = contacts[0]
      }

      // Create initial call-out record
      const callOutData = {
        alarm_id: alarm.id,
        contact_id: availableContact.id,
        call_out_time: new Date().toISOString(),
        status: 'dispatched',
        escalation_level: 1,
        call_type: 'voice',
        call_status: 'pending',
        retry_count: 0,
        next_escalation_time: new Date(Date.now() + this.escalationTimeoutMinutes * 60 * 1000).toISOString(),
        metadata: {
          building_id: alarm.building_id,
          alarm_severity: alarm.severity?.name,
          contact_priority: availableContact.priority_level
        }
      }

      const { data: callOut, error: callOutError } = await supabase
        .from('call_outs')
        .insert(callOutData)
        .select()
        .single()

      if (callOutError) throw callOutError

      // Initiate the first call
      await this.makeCall(callOut, alarm, availableContact)

      return callOut
    } catch (error) {
      console.error('Error starting escalation:', error)
      throw error
    }
  }

  /**
   * Make a phone call to a contact
   * @param {Object} callOut - Call-out record
   * @param {Object} alarm - Alarm data
   * @param {Object} contact - Contact to call
   * @returns {Promise<Object>} Call attempt record
   */
  async makeCall(callOut, alarm, contact) {
    try {
      console.log('Making call to:', contact.contact_name, contact.contact_phone)

      // Validate phone number
      const formattedPhone = retellAI.formatPhoneNumber(contact.contact_phone)
      if (!retellAI.isValidPhoneNumber(formattedPhone)) {
        throw new Error(`Invalid phone number format: ${contact.contact_phone}`)
      }

      // Create call attempt record
      const attemptData = {
        call_out_id: callOut.id,
        contact_id: contact.id,
        attempt_number: callOut.retry_count + 1,
        call_status: 'calling',
        call_start_time: new Date().toISOString()
      }

      const { data: attempt, error: attemptError } = await supabase
        .from('call_out_attempts')
        .insert(attemptData)
        .select()
        .single()

      if (attemptError) throw attemptError

      // Make the call via Retell AI
      const retellCall = await retellAI.createPhoneCall({
        toNumber: formattedPhone,
        alarmData: alarm,
        contactData: contact,
        metadata: {
          call_out_id: callOut.id,
          escalation_level: callOut.escalation_level,
          retry_attempt: callOut.retry_count + 1
        }
      })

      // Update call-out and attempt records with Retell AI call ID
      await Promise.all([
        supabase
          .from('call_outs')
          .update({
            retell_call_id: retellCall.call_id,
            call_status: 'calling',
            retry_count: callOut.retry_count + 1
          })
          .eq('id', callOut.id),
        
        supabase
          .from('call_out_attempts')
          .update({
            retell_call_id: retellCall.call_id,
            call_status: 'calling'
          })
          .eq('id', attempt.id)
      ])

      return { ...attempt, retell_call_id: retellCall.call_id }
    } catch (error) {
      console.error('Error making call:', error)
      
      // Update attempt record with error
      await supabase
        .from('call_out_attempts')
        .update({
          call_status: 'failed',
          error_message: error.message,
          call_end_time: new Date().toISOString()
        })
        .eq('call_out_id', callOut.id)
        .eq('attempt_number', callOut.retry_count + 1)

      throw error
    }
  }

  /**
   * Handle call completion from Retell AI webhook
   * @param {Object} callData - Call data from Retell AI
   * @returns {Promise<void>}
   */
  async handleCallCompletion(callData) {
    try {
      console.log('Handling call completion:', callData.call_id)

      // Find the call-out record
      const { data: callOut, error: callOutError } = await supabase
        .from('call_outs')
        .select('*, escalation_contacts(*), alarm_notifications(*)')
        .eq('retell_call_id', callData.call_id)
        .single()

      if (callOutError || !callOut) {
        console.error('Call-out not found for Retell call:', callData.call_id)
        return
      }

      // Update call attempt record
      const { data: attempt } = await supabase
        .from('call_out_attempts')
        .select('*')
        .eq('retell_call_id', callData.call_id)
        .single()

      if (attempt) {
        const acknowledgmentAnalysis = retellAI.analyzeAcknowledgment(callData.transcript)
        
        await supabase
          .from('call_out_attempts')
          .update({
            call_status: retellAI.mapCallStatus(callData.call_status),
            call_end_time: new Date(callData.end_timestamp).toISOString(),
            call_duration_seconds: callData.duration_ms ? Math.round(callData.duration_ms / 1000) : null,
            disconnection_reason: callData.disconnection_reason,
            call_transcript: callData.transcript,
            call_recording_url: callData.recording_url,
            call_analysis: callData.call_analysis,
            acknowledged: acknowledgmentAnalysis.acknowledged,
            contact_response: acknowledgmentAnalysis.foundKeywords?.join(', ')
          })
          .eq('id', attempt.id)
      }

      // Determine next action based on call outcome
      await this.processCallOutcome(callOut, callData)

    } catch (error) {
      console.error('Error handling call completion:', error)
    }
  }

  /**
   * Process call outcome and determine next steps
   * @param {Object} callOut - Call-out record
   * @param {Object} callData - Retell AI call data
   * @returns {Promise<void>}
   */
  async processCallOutcome(callOut, callData) {
    try {
      const acknowledgmentAnalysis = retellAI.analyzeAcknowledgment(callData.transcript)
      const callSuccessful = callData.call_analysis?.call_successful || false
      const answered = !['dial_failed', 'dial_no_answer', 'dial_busy'].includes(callData.disconnection_reason)

      if (acknowledgmentAnalysis.acknowledged && callSuccessful) {
        // Call was acknowledged - mark as completed
        await this.completeCallOut(callOut, 'acknowledged', acknowledgmentAnalysis)
      } else if (!answered || !callSuccessful) {
        // Call failed or wasn't answered - retry or escalate
        await this.handleFailedCall(callOut, callData.disconnection_reason)
      } else {
        // Call was answered but not acknowledged - escalate
        await this.escalateCallOut(callOut, 'no_acknowledgment')
      }
    } catch (error) {
      console.error('Error processing call outcome:', error)
    }
  }

  /**
   * Complete a call-out successfully
   * @param {Object} callOut - Call-out record
   * @param {string} reason - Completion reason
   * @param {Object} acknowledgmentData - Acknowledgment analysis
   * @returns {Promise<void>}
   */
  async completeCallOut(callOut, reason, acknowledgmentData = {}) {
    try {
      await supabase
        .from('call_outs')
        .update({
          status: 'completed',
          call_status: 'completed',
          acknowledged_by_contact: true,
          contact_response: acknowledgmentData.foundKeywords?.join(', ') || reason,
          completion_time: new Date().toISOString(),
          next_escalation_time: null
        })
        .eq('id', callOut.id)

      // Also update the alarm status if needed
      if (callOut.alarm_id) {
        await supabase
          .from('alarm_notifications')
          .update({
            status: 'acknowledged',
            acknowledged_at: new Date().toISOString(),
            acknowledged_by: callOut.contact_id
          })
          .eq('id', callOut.alarm_id)
      }

      console.log('Call-out completed successfully:', callOut.id)
    } catch (error) {
      console.error('Error completing call-out:', error)
    }
  }

  /**
   * Handle a failed call (retry or escalate)
   * @param {Object} callOut - Call-out record
   * @param {string} failureReason - Why the call failed
   * @returns {Promise<void>}
   */
  async handleFailedCall(callOut, failureReason) {
    try {
      if (callOut.retry_count < this.maxRetryAttempts) {
        // Retry with same contact
        await this.scheduleRetry(callOut, failureReason)
      } else {
        // Escalate to next contact
        await this.escalateCallOut(callOut, failureReason)
      }
    } catch (error) {
      console.error('Error handling failed call:', error)
    }
  }

  /**
   * Schedule a retry for the same contact
   * @param {Object} callOut - Call-out record
   * @param {string} reason - Retry reason
   * @returns {Promise<void>}
   */
  async scheduleRetry(callOut, reason) {
    try {
      const retryTime = new Date(Date.now() + 2 * 60 * 1000) // Retry in 2 minutes
      
      await supabase
        .from('call_outs')
        .update({
          call_status: 'pending',
          escalation_reason: `Retry after ${reason}`,
          next_escalation_time: retryTime.toISOString()
        })
        .eq('id', callOut.id)

      console.log('Scheduled retry for call-out:', callOut.id, 'at', retryTime)
    } catch (error) {
      console.error('Error scheduling retry:', error)
    }
  }

  /**
   * Escalate to the next contact level
   * @param {Object} callOut - Call-out record
   * @param {string} reason - Escalation reason
   * @returns {Promise<void>}
   */
  async escalateCallOut(callOut, reason) {
    try {
      const nextLevel = callOut.escalation_level + 1
      
      if (nextLevel > this.maxEscalationLevel) {
        // Max escalation reached - mark as failed
        await supabase
          .from('call_outs')
          .update({
            status: 'cancelled',
            call_status: 'failed',
            escalation_reason: 'Maximum escalation level reached',
            completion_time: new Date().toISOString(),
            next_escalation_time: null
          })
          .eq('id', callOut.id)

        console.log('Maximum escalation reached for call-out:', callOut.id)
        return
      }

      // Get next contact
      const contacts = await this.getEscalationContacts(callOut.alarm_notifications.building_id)
      const nextContact = contacts.find(c => c.priority_level === nextLevel)
      
      if (!nextContact) {
        console.error('No contact found for escalation level:', nextLevel)
        return
      }

      // Update call-out for escalation
      await supabase
        .from('call_outs')
        .update({
          contact_id: nextContact.id,
          escalation_level: nextLevel,
          call_status: 'pending',
          retry_count: 0,
          escalation_reason: reason,
          next_escalation_time: new Date(Date.now() + this.escalationTimeoutMinutes * 60 * 1000).toISOString()
        })
        .eq('id', callOut.id)

      console.log('Escalated call-out to level:', nextLevel, 'contact:', nextContact.contact_name)

      // Make call to next contact
      const { data: updatedCallOut } = await supabase
        .from('call_outs')
        .select('*, alarm_notifications(*)')
        .eq('id', callOut.id)
        .single()

      if (updatedCallOut) {
        await this.makeCall(updatedCallOut, updatedCallOut.alarm_notifications, nextContact)
      }
    } catch (error) {
      console.error('Error escalating call-out:', error)
    }
  }

  /**
   * Process pending escalations (called by scheduled job)
   * @returns {Promise<void>}
   */
  async processPendingEscalations() {
    try {
      const now = new Date().toISOString()
      
      // Find call-outs that need escalation
      const { data: pendingCallOuts, error } = await supabase
        .from('call_outs')
        .select('*, alarm_notifications(*), escalation_contacts(*)')
        .in('call_status', ['pending', 'no_answer'])
        .lte('next_escalation_time', now)
        .is('completion_time', null)

      if (error) throw error

      for (const callOut of pendingCallOuts || []) {
        console.log('Processing pending escalation:', callOut.id)
        
        if (callOut.call_status === 'pending') {
          // Make the call
          await this.makeCall(callOut, callOut.alarm_notifications, callOut.escalation_contacts)
        } else {
          // Escalate due to timeout
          await this.escalateCallOut(callOut, 'timeout')
        }
      }
    } catch (error) {
      console.error('Error processing pending escalations:', error)
    }
  }
}

// Export singleton instance
export const escalationService = new EscalationService()
export default escalationService
