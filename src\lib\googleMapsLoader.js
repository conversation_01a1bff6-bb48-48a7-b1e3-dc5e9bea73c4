// Google Maps API loader utility

let isLoaded = false
let isLoading = false
let loadPromise = null
let lastError = null

export const loadGoogleMapsAPI = () => {
  // Return existing promise if already loading
  if (loadPromise) {
    return loadPromise
  }

  // Return resolved promise if already loaded
  if (isLoaded) {
    return Promise.resolve()
  }

  // Start loading
  isLoading = true

  loadPromise = new Promise((resolve, reject) => {
    // Check if Google Maps is already available
    if (window.google && window.google.maps && window.google.maps.places) {
      isLoaded = true
      isLoading = false
      resolve()
      return
    }

    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY

    if (!apiKey) {
      const error = new Error(
        'Google Maps API key not found. Please add VITE_GOOGLE_MAPS_API_KEY to your environment variables.'
      )
      isLoading = false
      loadPromise = null
      lastError = error
      reject(error)
      return
    }

    // Create callback function name
    const callbackName = 'initGoogleMaps'

    // Set up global callback
    window[callbackName] = () => {
      try {
        // Verify that the APIs are actually loaded
        if (!window.google?.maps) {
          throw new Error('Google Maps API not properly loaded')
        }

        // Check for API errors using standard addEventListener
        window.addEventListener('load', () => {
          // Listen for API errors
          if (window.gm_authFailure) {
            const error = new Error('Google Maps API authentication failed')
            lastError = error
            console.error('Google Maps API Error:', error)
          }
        })

        isLoaded = true
        isLoading = false
        lastError = null
        delete window[callbackName] // Clean up

        console.log('✅ Google Maps API loaded successfully')
        resolve()
      } catch (error) {
        isLoading = false
        lastError = error
        delete window[callbackName]
        console.error('Google Maps API loading error:', error)
        reject(error)
      }
    }

    // Create script element with optimized loading parameters
    const script = document.createElement('script')
    // Include loading=async parameter to follow Google's best practices and eliminate performance warnings
    // This indicates that the Maps JavaScript API is being loaded asynchronously for optimal performance
    // Using v=beta to access the new Places API features including AutocompleteSuggestion
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&loading=async&v=beta&callback=${callbackName}`
    script.async = true
    script.defer = true

    // Handle script load errors
    script.onerror = (event) => {
      isLoading = false
      loadPromise = null
      delete window[callbackName]

      const error = new Error('Failed to load Google Maps API script')
      lastError = error

      console.error('Google Maps API script loading failed:', {
        error: error.message,
        event,
        apiKey: apiKey ? `${apiKey.substring(0, 8)}...` : 'not configured',
        url: script.src
      })

      reject(error)
    }

    // Set up global error handler for API authentication failures
    window.gm_authFailure = () => {
      const error = new Error('Google Maps API authentication failed - check API key and billing')
      lastError = error
      console.error('Google Maps API authentication error:', error)
    }

    // Add script to document
    document.head.appendChild(script)
  })

  return loadPromise
}

// Check if Google Maps API is loaded
export const isGoogleMapsLoaded = () => {
  return isLoaded && window.google && window.google.maps && window.google.maps.places
}

// Get loading status
export const isGoogleMapsLoading = () => {
  return isLoading
}

// Get last error information
export const getLastGoogleMapsError = () => {
  return lastError
}

// Reset the loader state (useful for testing)
export const resetGoogleMapsLoader = () => {
  isLoaded = false
  isLoading = false
  loadPromise = null
  lastError = null

  // Remove existing scripts
  const scripts = document.querySelectorAll('script[src*="maps.googleapis.com"]')
  scripts.forEach(script => script.remove())

  // Clean up global variables
  if (window.gm_authFailure) {
    delete window.gm_authFailure
  }

  console.log('🔄 Google Maps loader state reset')
}
