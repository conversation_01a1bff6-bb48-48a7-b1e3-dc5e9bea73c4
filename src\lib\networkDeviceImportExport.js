/**
 * Network Device Import/Export Utilities
 * Provides functionality for bulk device management via CSV files
 */

import { cleanNetworkDeviceFormData, validateNetworkDeviceForm } from './networkDeviceValidation'

/**
 * CSV headers for network device export/import
 */
export const CSV_HEADERS = [
  'building_id',
  'station_name',
  'device_type',
  'host_id',
  'ip_address',
  'subnet_mask',
  'gateway',
  'internal_dns_server_1',
  'internal_dns_server_2',
  'station_username',
  'station_password_encrypted',
  'windows_username',
  'windows_password_encrypted',
  'platform_username',
  'platform_password_encrypted',
  'passphrase_encrypted',
  'software_version',
  'notes',
  'is_active'
]

/**
 * Export network devices to CSV format
 * @param {Array} devices - Array of network device objects
 * @param {Array} buildings - Array of building objects for name mapping
 * @returns {string} CSV content
 */
export function exportDevicesToCSV(devices, buildings = []) {
  // Create building lookup map
  const buildingMap = buildings.reduce((map, building) => {
    map[building.id] = building.name
    return map
  }, {})

  // Create CSV header
  const headers = [
    'Building Name',
    'Station Name',
    'Device Type',
    'Host ID',
    'IP Address',
    'Subnet Mask',
    'Gateway',
    'DNS Server 1',
    'DNS Server 2',
    'Station Username',
    'Station Password',
    'Windows Username',
    'Windows Password',
    'Platform Username',
    'Platform Password',
    'Passphrase',
    'Software Version',
    'Notes',
    'Active'
  ]

  // Convert devices to CSV rows
  const rows = devices.map(device => [
    buildingMap[device.building_id] || device.building_id,
    device.station_name || '',
    device.device_type || '',
    device.host_id || '',
    device.ip_address || '',
    device.subnet_mask || '',
    device.gateway || '',
    device.internal_dns_server_1 || '',
    device.internal_dns_server_2 || '',
    device.station_username || '',
    '***ENCRYPTED***', // Don't export actual passwords for security
    device.windows_username || '',
    '***ENCRYPTED***', // Don't export actual passwords for security
    device.platform_username || '',
    '***ENCRYPTED***', // Don't export actual passwords for security
    '***ENCRYPTED***', // Don't export actual passphrases for security
    device.software_version || '',
    device.notes || '',
    device.is_active ? 'Yes' : 'No'
  ])

  // Combine headers and rows
  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
    .join('\n')

  return csvContent
}

/**
 * Download CSV file
 * @param {string} csvContent - CSV content to download
 * @param {string} filename - Filename for the download
 */
export function downloadCSV(csvContent, filename = 'network_devices.csv') {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

/**
 * Parse CSV content to device objects
 * @param {string} csvContent - CSV content to parse
 * @param {Array} buildings - Array of building objects for ID mapping
 * @returns {Object} Parse result with devices and errors
 */
export function parseCSVToDevices(csvContent, buildings = []) {
  const lines = csvContent.split('\n').filter(line => line.trim())
  
  if (lines.length < 2) {
    return { devices: [], errors: ['CSV file must contain at least a header row and one data row'] }
  }

  // Create building lookup map (by name)
  const buildingMap = buildings.reduce((map, building) => {
    map[building.name.toLowerCase()] = building.id
    return map
  }, {})

  const headers = lines[0].split(',').map(header => header.replace(/"/g, '').trim())
  const devices = []
  const errors = []

  // Validate headers (only Building Name and Station Name are required)
  const requiredHeaders = ['Building Name', 'Station Name']
  const missingHeaders = requiredHeaders.filter(header => !headers.includes(header))

  if (missingHeaders.length > 0) {
    errors.push(`Missing required headers: ${missingHeaders.join(', ')}`)
    return { devices: [], errors }
  }

  // Parse data rows
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue

    try {
      const values = parseCSVLine(line)
      
      if (values.length !== headers.length) {
        errors.push(`Row ${i + 1}: Column count mismatch (expected ${headers.length}, got ${values.length})`)
        continue
      }

      const device = {}
      
      // Map CSV values to device object
      headers.forEach((header, index) => {
        const rawValue = values[index] || ''
        const value = rawValue.trim()

        switch (header) {
          case 'Building Name':
            if (!value) {
              errors.push(`Row ${i + 1}: Building Name is required`)
              return
            }
            const buildingId = buildingMap[value.toLowerCase()]
            if (!buildingId) {
              errors.push(`Row ${i + 1}: Building "${value}" not found`)
              return
            }
            device.building_id = buildingId
            break
          case 'Station Name':
            if (!value) {
              errors.push(`Row ${i + 1}: Station Name is required`)
              return
            }
            device.station_name = value
            break
          case 'Device Type':
            device.device_type = value || null
            break
          case 'Host ID':
            device.host_id = value || null
            break
          case 'IP Address':
            device.ip_address = value || null
            break
          case 'Subnet Mask':
            device.subnet_mask = value || null
            break
          case 'Gateway':
            device.gateway = value || null
            break
          case 'DNS Server 1':
            device.internal_dns_server_1 = value || null
            break
          case 'DNS Server 2':
            device.internal_dns_server_2 = value || null
            break
          case 'Station Username':
            device.station_username = value || null
            break
          case 'Station Password':
            // Only import if not the placeholder text
            if (value && value !== '***ENCRYPTED***') {
              device.station_password = value
            }
            break
          case 'Windows Username':
            device.windows_username = value || null
            break
          case 'Windows Password':
            // Only import if not the placeholder text
            if (value && value !== '***ENCRYPTED***') {
              device.windows_password = value
            }
            break
          case 'Platform Username':
            device.platform_username = value || null
            break
          case 'Platform Password':
            // Only import if not the placeholder text
            if (value && value !== '***ENCRYPTED***') {
              device.platform_password = value
            }
            break
          case 'Passphrase':
            // Only import if not the placeholder text
            if (value && value !== '***ENCRYPTED***') {
              device.passphrase = value
            }
            break
          case 'Software Version':
            device.software_version = value || null
            break
          case 'Notes':
            device.notes = value || null
            break
          case 'Active':
            device.is_active = value.toLowerCase() === 'yes' || value.toLowerCase() === 'true'
            break
        }
      })

      // Clean and validate device data
      const cleanedDevice = cleanNetworkDeviceFormData(device)
      const validation = validateNetworkDeviceForm(cleanedDevice)
      
      if (!validation.isValid) {
        const errorMessages = Object.entries(validation.errors)
          .map(([field, message]) => `${field}: ${message}`)
          .join(', ')
        errors.push(`Row ${i + 1}: ${errorMessages}`)
        continue
      }

      devices.push(cleanedDevice)
      
    } catch (error) {
      errors.push(`Row ${i + 1}: Failed to parse - ${error.message}`)
    }
  }

  return { devices, errors }
}

/**
 * Parse a single CSV line handling quoted fields
 * @param {string} line - CSV line to parse
 * @returns {Array} Array of field values
 */
function parseCSVLine(line) {
  const result = []
  let current = ''
  let inQuotes = false
  let i = 0

  while (i < line.length) {
    const char = line[i]
    
    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // Escaped quote
        current += '"'
        i += 2
      } else {
        // Toggle quote state
        inQuotes = !inQuotes
        i++
      }
    } else if (char === ',' && !inQuotes) {
      // Field separator
      result.push(current.trim())
      current = ''
      i++
    } else {
      current += char
      i++
    }
  }
  
  // Add the last field
  result.push(current.trim())
  
  return result
}

/**
 * Generate CSV template for device import
 * @param {Array} buildings - Array of building objects
 * @returns {string} CSV template content
 */
export function generateCSVTemplate(buildings = []) {
  const headers = [
    'Building Name',
    'Station Name',
    'Device Type',
    'Host ID',
    'IP Address',
    'Subnet Mask',
    'Gateway',
    'DNS Server 1',
    'DNS Server 2',
    'Station Username',
    'Station Password',
    'Windows Username',
    'Windows Password',
    'Platform Username',
    'Platform Password',
    'Passphrase',
    'Software Version',
    'Notes',
    'Active'
  ]

  // Add example rows
  const exampleRows = [
    [
      buildings.length > 0 ? buildings[0].name : 'Example Building',
      'MAIN-ROUTER-01',
      'BACnet router',
      'RTR-001-MAIN',
      '***********',
      '*************',
      '***********',
      '*******',
      '*******',
      'admin',
      'SecurePassword123!',
      'domain\\admin',
      'DomainPassword456!',
      'platform_user',
      'PlatformPassword789!',
      'MySecurePassphrase2024',
      'v2.1.3',
      'Main building router - primary network gateway',
      'Yes'
    ],
    [
      buildings.length > 0 ? buildings[0].name : 'Example Building',
      'HVAC-CONTROLLER-01',
      'JACE',
      'JACE-001-HVAC',
      '***********00',
      '*************',
      '***********',
      '***********',
      '*******',
      'hvac_admin',
      'HvacPassword123!',
      '',
      '',
      'hvac_platform',
      'HvacPlatformPass456!',
      '',
      'JACE-8000 v4.8',
      'Primary HVAC controller for zones 1-5',
      'Yes'
    ]
  ]

  const csvContent = [headers, ...exampleRows]
    .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
    .join('\n')

  return csvContent
}

/**
 * Validate CSV file before processing
 * @param {File} file - CSV file to validate
 * @returns {Object} Validation result
 */
export function validateCSVFile(file) {
  const errors = []
  
  // Check file type
  if (!file.type.includes('csv') && !file.name.endsWith('.csv')) {
    errors.push('File must be a CSV file')
  }
  
  // Check file size (max 5MB)
  const maxSize = 5 * 1024 * 1024 // 5MB
  if (file.size > maxSize) {
    errors.push('File size must be less than 5MB')
  }
  
  // Check if file is empty
  if (file.size === 0) {
    errors.push('File cannot be empty')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Read CSV file content
 * @param {File} file - CSV file to read
 * @returns {Promise<string>} File content
 */
export function readCSVFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (event) => {
      resolve(event.target.result)
    }
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    
    reader.readAsText(file)
  })
}
