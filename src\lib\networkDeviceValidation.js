/**
 * Network Device Validation Library
 * Provides validation functions for network device management forms
 */

// Device type options
export const DEVICE_TYPES = [
  'BACnet router',
  'Switch',
  'Access Point',
  'Server',
  'Workstation',
  'JACE',
  'Gateway',
  'Firewall',
  'Load Balancer',
  'NAS',
  'Printer',
  'Camera',
  'Other'
]

// Common subnet masks
export const COMMON_SUBNET_MASKS = [
  '*************',   // /24
  '*************',   // /23
  '*************',   // /22
  '*************',   // /21
  '*************',   // /20
  '***********',     // /16
  '***********',     // /15
  '***********',     // /14
  '***********',     // /13
  '***********',     // /12
  '*********'        // /8
]

/**
 * Validate IPv4 address format
 * @param {string} ip - IP address to validate
 * @returns {boolean} True if valid IPv4 address
 */
export function isValidIPv4(ip) {
  if (!ip || typeof ip !== 'string') return false
  
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipv4Regex.test(ip.trim())
}

/**
 * Validate subnet mask format
 * @param {string} mask - Subnet mask to validate
 * @returns {boolean} True if valid subnet mask
 */
export function isValidSubnetMask(mask) {
  if (!isValidIPv4(mask)) return false
  
  // Convert to binary and check if it's a valid subnet mask
  const parts = mask.split('.').map(part => parseInt(part, 10))
  const binary = parts.map(part => part.toString(2).padStart(8, '0')).join('')
  
  // Valid subnet mask should have all 1s followed by all 0s
  const match = binary.match(/^(1*)0*$/)
  return match !== null
}

/**
 * Check if IP address is in private range
 * @param {string} ip - IP address to check
 * @returns {boolean} True if private IP
 */
export function isPrivateIP(ip) {
  if (!isValidIPv4(ip)) return false
  
  const parts = ip.split('.').map(part => parseInt(part, 10))
  const [a, b] = parts
  
  // Private IP ranges:
  // 10.0.0.0 - ************** (10.0.0.0/8)
  // ********** - ************** (**********/12)
  // *********** - *************** (***********/16)
  // ********* - *************** (*********/8) - localhost
  
  return (
    a === 10 ||
    (a === 172 && b >= 16 && b <= 31) ||
    (a === 192 && b === 168) ||
    a === 127
  )
}

/**
 * Validate host ID format
 * @param {string} hostId - Host ID to validate
 * @returns {boolean} True if valid host ID
 */
export function isValidHostId(hostId) {
  if (!hostId || typeof hostId !== 'string') return false
  
  const trimmed = hostId.trim()
  
  // Host ID should be 3-50 characters, alphanumeric with hyphens and underscores
  const hostIdRegex = /^[A-Za-z0-9_-]{3,50}$/
  return hostIdRegex.test(trimmed)
}

/**
 * Validate station name format
 * @param {string} name - Station name to validate
 * @returns {boolean} True if valid station name
 */
export function isValidStationName(name) {
  if (!name || typeof name !== 'string') return false
  
  const trimmed = name.trim()
  
  // Station name should be 2-255 characters
  return trimmed.length >= 2 && trimmed.length <= 255
}

/**
 * Validate username format
 * @param {string} username - Username to validate
 * @returns {boolean} True if valid username
 */
export function isValidUsername(username) {
  if (!username || typeof username !== 'string') return false
  
  const trimmed = username.trim()
  
  // Username should be 2-100 characters, alphanumeric with underscores and dots
  const usernameRegex = /^[A-Za-z0-9_.]{2,100}$/
  return usernameRegex.test(trimmed)
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with strength info
 */
export function validatePasswordStrength(password) {
  if (!password || typeof password !== 'string') {
    return { isValid: false, strength: 'none', message: 'Password is required' }
  }

  const length = password.length
  const hasLower = /[a-z]/.test(password)
  const hasUpper = /[A-Z]/.test(password)
  const hasNumber = /\d/.test(password)
  const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)

  if (length < 8) {
    return { isValid: false, strength: 'weak', message: 'Password must be at least 8 characters' }
  }

  let score = 0
  if (hasLower) score++
  if (hasUpper) score++
  if (hasNumber) score++
  if (hasSpecial) score++
  if (length >= 12) score++

  if (score < 3) {
    return { isValid: false, strength: 'weak', message: 'Password should contain uppercase, lowercase, and numbers' }
  } else if (score < 4) {
    return { isValid: true, strength: 'medium', message: 'Good password strength' }
  } else {
    return { isValid: true, strength: 'strong', message: 'Strong password' }
  }
}

/**
 * Validate passphrase strength (more lenient than password)
 * @param {string} passphrase - Passphrase to validate
 * @returns {Object} Validation result with strength info
 */
export function validatePassphraseStrength(passphrase) {
  if (!passphrase || typeof passphrase !== 'string') {
    return { isValid: true, strength: 'none', message: 'Passphrase is optional' }
  }

  const length = passphrase.length

  if (length < 12) {
    return { isValid: true, strength: 'weak', message: 'Passphrase should be at least 12 characters for better security' }
  }

  if (length < 20) {
    return { isValid: true, strength: 'medium', message: 'Good passphrase length' }
  } else {
    return { isValid: true, strength: 'strong', message: 'Strong passphrase' }
  }
}

/**
 * Validate network device form data
 * @param {Object} formData - Form data to validate
 * @param {boolean} isUpdate - Whether this is an update operation
 * @returns {Object} Validation result with errors and warnings
 */
export function validateNetworkDeviceForm(formData, isUpdate = false) {
  const errors = {}
  const warnings = {}

  // Station name validation (REQUIRED)
  if (!formData.station_name || !isValidStationName(formData.station_name)) {
    errors.station_name = 'Station name must be 2-255 characters'
  }

  // Device type validation (OPTIONAL)
  if (formData.device_type && formData.device_type.trim() !== '' && !DEVICE_TYPES.includes(formData.device_type)) {
    errors.device_type = 'Please select a valid device type'
  }

  // Host ID validation (OPTIONAL) - format warning only
  if (formData.host_id && formData.host_id.trim() !== '' && !isValidHostId(formData.host_id)) {
    warnings.host_id = 'Host ID should be 3-50 alphanumeric characters (hyphens and underscores allowed) for best compatibility'
  }

  // IP address validation (OPTIONAL)
  if (formData.ip_address && formData.ip_address.trim() !== '') {
    if (!isValidIPv4(formData.ip_address)) {
      errors.ip_address = 'Please enter a valid IPv4 address'
    } else if (!isPrivateIP(formData.ip_address)) {
      warnings.ip_address = 'Consider using a private IP range (10.x.x.x, 172.16-31.x.x, 192.168.x.x) for better network security'
    }
  }

  // Subnet mask validation (OPTIONAL)
  if (formData.subnet_mask && formData.subnet_mask.trim() !== '' && !isValidSubnetMask(formData.subnet_mask)) {
    errors.subnet_mask = 'Please enter a valid subnet mask'
  }
  
  // Gateway validation (optional)
  if (formData.gateway && formData.gateway.trim() && !isValidIPv4(formData.gateway)) {
    errors.gateway = 'Please enter a valid gateway IP address'
  }
  
  // DNS server validation (optional)
  if (formData.internal_dns_server_1 && formData.internal_dns_server_1.trim() && !isValidIPv4(formData.internal_dns_server_1)) {
    errors.internal_dns_server_1 = 'Please enter a valid DNS server IP address'
  }
  
  if (formData.internal_dns_server_2 && formData.internal_dns_server_2.trim() && !isValidIPv4(formData.internal_dns_server_2)) {
    errors.internal_dns_server_2 = 'Please enter a valid DNS server IP address'
  }
  
  // Username validation (optional)
  if (formData.station_username && formData.station_username.trim() && !isValidUsername(formData.station_username)) {
    errors.station_username = 'Username must be 2-100 alphanumeric characters (underscores and dots allowed)'
  }

  if (formData.windows_username && formData.windows_username.trim() && !isValidUsername(formData.windows_username)) {
    errors.windows_username = 'Username must be 2-100 alphanumeric characters (underscores and dots allowed)'
  }

  if (formData.platform_username && formData.platform_username.trim() && !isValidUsername(formData.platform_username)) {
    errors.platform_username = 'Username must be 2-100 alphanumeric characters (underscores and dots allowed)'
  }

  // Password validation (optional with soft warnings for strength recommendations)
  if (formData.station_password && formData.station_password.trim()) {
    const passwordValidation = validatePasswordStrength(formData.station_password)
    if (passwordValidation.strength === 'weak' || passwordValidation.strength === 'medium') {
      warnings.station_password = passwordValidation.message
    }
  }

  if (formData.windows_password && formData.windows_password.trim()) {
    const passwordValidation = validatePasswordStrength(formData.windows_password)
    if (passwordValidation.strength === 'weak' || passwordValidation.strength === 'medium') {
      warnings.windows_password = passwordValidation.message
    }
  }

  if (formData.platform_password && formData.platform_password.trim()) {
    const passwordValidation = validatePasswordStrength(formData.platform_password)
    if (passwordValidation.strength === 'weak' || passwordValidation.strength === 'medium') {
      warnings.platform_password = passwordValidation.message
    }
  }

  // Passphrase validation (optional with soft warning for length)
  if (formData.passphrase && formData.passphrase.trim()) {
    const passphraseValidation = validatePassphraseStrength(formData.passphrase)
    if (passphraseValidation.strength === 'weak' || passphraseValidation.strength === 'medium') {
      warnings.passphrase = passphraseValidation.message
    }
  }
  
  // Building ID validation
  if (!formData.building_id || formData.building_id.trim().length === 0) {
    errors.building_id = 'Please select a building'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  }
}

/**
 * Clean and normalize network device form data
 * @param {Object} formData - Raw form data
 * @returns {Object} Cleaned form data
 */
export function cleanNetworkDeviceFormData(formData) {
  const cleaned = {
    building_id: formData.building_id?.trim() || '',
    station_name: formData.station_name?.trim() || '',
    device_type: formData.device_type?.trim() || null,
    host_id: formData.host_id?.trim()?.toUpperCase() || null,
    ip_address: formData.ip_address?.trim() || null,
    subnet_mask: formData.subnet_mask?.trim() || null,
    gateway: formData.gateway?.trim() || null,
    internal_dns_server_1: formData.internal_dns_server_1?.trim() || null,
    internal_dns_server_2: formData.internal_dns_server_2?.trim() || null,
    station_username: formData.station_username?.trim() || null,
    windows_username: formData.windows_username?.trim() || null,
    platform_username: formData.platform_username?.trim() || null,
    software_version: formData.software_version?.trim() || null,
    notes: formData.notes?.trim() || null,
    is_active: formData.is_active !== undefined ? formData.is_active : true
  }

  // Handle passwords separately - always include them for processing
  // These will be converted to encrypted fields by the createDevice/updateDevice functions
  // Include them even if empty so the password processing logic can handle them properly
  cleaned.station_password = formData.station_password || null
  cleaned.windows_password = formData.windows_password || null
  cleaned.platform_password = formData.platform_password || null
  cleaned.passphrase = formData.passphrase || null

  return cleaned
}

/**
 * Generate suggested host ID from station name
 * @param {string} stationName - Station name
 * @returns {string} Suggested host ID
 */
export function generateHostIdSuggestion(stationName) {
  if (!stationName || typeof stationName !== 'string') return ''
  
  return stationName
    .trim()
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .substring(0, 50)
}

/**
 * Auto-save form data to localStorage
 * @param {string} formId - Unique form identifier
 * @param {Object} formData - Form data to save
 */
export function autoSaveNetworkDeviceFormData(formId, formData) {
  try {
    const key = `network_device_form_${formId}`
    localStorage.setItem(key, JSON.stringify({
      data: formData,
      timestamp: Date.now()
    }))
  } catch (error) {
    console.warn('Failed to auto-save form data:', error)
  }
}

/**
 * Load auto-saved form data from localStorage
 * @param {string} formId - Unique form identifier
 * @returns {Object|null} Saved form data or null
 */
export function loadAutoSavedNetworkDeviceFormData(formId) {
  try {
    const key = `network_device_form_${formId}`
    const saved = localStorage.getItem(key)
    
    if (!saved) return null
    
    const { data, timestamp } = JSON.parse(saved)
    
    // Only return data if it's less than 24 hours old
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours
    if (Date.now() - timestamp > maxAge) {
      localStorage.removeItem(key)
      return null
    }
    
    return data
  } catch (error) {
    console.warn('Failed to load auto-saved form data:', error)
    return null
  }
}

/**
 * Clear auto-saved form data
 * @param {string} formId - Unique form identifier
 */
export function clearAutoSavedNetworkDeviceFormData(formId) {
  try {
    const key = `network_device_form_${formId}`
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to clear auto-saved form data:', error)
  }
}
