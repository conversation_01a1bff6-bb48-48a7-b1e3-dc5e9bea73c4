/**
 * Retell AI Integration Service
 * Handles voice calling for alarm notifications with escalation
 *
 * Configured Agent: JSC Alarm Call-Out Agent
 * Agent ID: agent_beac3aef1a176d48c4d85d2541
 * LLM ID: llm_f42d54584f5cfc07e6ee62b1cb83
 */

import type {
  RetellAICallRequest,
  RetellAICallResponse,
  AlarmNotification,
  EscalationContact
} from '@/types'

const RETELL_AI_BASE_URL = 'https://api.retellai.com/v2'

// Default agent configuration for JSC Alarm Call-Out system
const DEFAULT_AGENT_ID = 'agent_beac3aef1a176d48c4d85d2541'
const DEFAULT_LLM_ID = 'llm_f54a7921ecbb480b7c96a94f4ebe' // Enhanced LLM with custom tools

interface CallMetadata {
  call_out_id: string
  escalation_level: number
  retry_attempt: number
}

interface CallPayload {
  from_number: string
  to_number: string
  agent_id: string
  retell_llm_dynamic_variables: Record<string, string>
  metadata: Record<string, unknown>
}

class RetellAIService {
  private apiKey: string | undefined
  private fromNumber: string | undefined
  private agentId: string
  private llmId: string
  private webhookSecret: string | undefined
  private webhookUrl: string
  constructor() {
    this.apiKey = import.meta.env.VITE_RETELL_AI_API_KEY
    this.fromNumber = import.meta.env.VITE_RETELL_AI_FROM_NUMBER
    this.agentId = import.meta.env.VITE_RETELL_AI_AGENT_ID || DEFAULT_AGENT_ID
    this.llmId = import.meta.env.VITE_RETELL_AI_LLM_ID || DEFAULT_LLM_ID
    this.webhookSecret = import.meta.env.VITE_RETELL_AI_WEBHOOK_SECRET
    this.webhookUrl = import.meta.env.VITE_SUPABASE_URL ?
      `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/retell-webhook-handler` :
      'https://pdnclfznadtbuxeoszjx.supabase.co/functions/v1/retell-webhook-handler'

    if (!this.apiKey) {
      console.warn('Retell AI API key not configured')
    }

    console.log('Retell AI Service initialized:', {
      agentId: this.agentId,
      llmId: this.llmId,
      hasApiKey: !!this.apiKey,
      hasFromNumber: !!this.fromNumber,
      webhookUrl: this.webhookUrl
    })
  }

  /**
   * Check if Retell AI is properly configured
   */
  isConfigured() {
    return !!(this.apiKey && this.fromNumber && this.agentId)
  }

  /**
   * Get the current agent configuration
   * @returns {Object} Agent configuration details
   */
  getAgentConfig() {
    return {
      agentId: this.agentId,
      llmId: this.llmId,
      fromNumber: this.fromNumber,
      webhookUrl: this.webhookUrl,
      isConfigured: this.isConfigured()
    }
  }

  /**
   * Create an outbound phone call via Retell AI
   */
  async createPhoneCall({
    toNumber,
    alarmData,
    contactData,
    metadata = {}
  }: RetellAICallRequest): Promise<RetellAICallResponse> {
    if (!this.isConfigured()) {
      throw new Error('Retell AI is not properly configured')
    }

    try {
      // Prepare comprehensive dynamic variables for the JSC Alarm Call-Out Agent
      const dynamicVariables = {
        // Contact Information
        contact_name: contactData.contact_name || 'Emergency Contact',
        contact_role: contactData.contact_role || 'Facility Manager',

        // Building Information
        building_name: alarmData.building?.name || alarmData.building_name || 'Unknown Building',
        building_address: alarmData.building?.address || alarmData.building_address || 'Address not specified',

        // Alarm Details
        alarm_type: alarmData.alarm_type?.name || alarmData.alarm_type || 'System Alarm',
        alarm_severity: alarmData.severity?.name || alarmData.alarm_severity || 'High Priority',
        alarm_details: alarmData.alarm_details || 'Alarm details not available',
        alarm_location: alarmData.location_details || 'Location not specified',
        alarm_time: alarmData.alarm_time ? new Date(alarmData.alarm_time).toLocaleString() : new Date().toLocaleString(),

        // Call Management
        escalation_level: String(metadata.escalation_level || 1),
        retry_attempt: String(metadata.retry_attempt || 1),

        // Webhook Configuration
        webhook_url: this.webhookUrl
      }

      const callPayload = {
        from_number: this.fromNumber,
        to_number: toNumber,
        agent_id: this.agentId,
        retell_llm_dynamic_variables: dynamicVariables,
        metadata: {
          alarm_id: alarmData.id,
          contact_id: contactData.id,
          building_id: alarmData.building_id,
          call_out_id: metadata.call_out_id,
          escalation_level: metadata.escalation_level,
          retry_attempt: metadata.retry_attempt,
          created_by: 'jsc-alarm-system'
        }
      }

      const response = await fetch(`${RETELL_AI_BASE_URL}/create-phone-call`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(callPayload)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`Retell AI API error: ${response.status} - ${errorData.message || response.statusText}`)
      }

      const callData = await response.json()
      
      console.log('Retell AI call created:', {
        call_id: callData.call_id,
        to_number: toNumber,
        alarm_id: alarmData.id
      })

      return callData
    } catch (error) {
      console.error('Error creating Retell AI call:', error)
      throw error
    }
  }

  /**
   * Get call details from Retell AI
   * @param {string} callId - Retell AI call ID
   * @returns {Promise<Object>} Call details
   */
  async getCall(callId) {
    if (!this.isConfigured()) {
      throw new Error('Retell AI is not properly configured')
    }

    try {
      const response = await fetch(`${RETELL_AI_BASE_URL}/get-call/${callId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`Retell AI API error: ${response.status} - ${errorData.message || response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Error getting Retell AI call:', error)
      throw error
    }
  }

  /**
   * List recent calls from Retell AI
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} List of calls
   */
  async listCalls(params = {}) {
    if (!this.isConfigured()) {
      throw new Error('Retell AI is not properly configured')
    }

    try {
      const queryParams = new URLSearchParams(params)
      const response = await fetch(`${RETELL_AI_BASE_URL}/list-calls?${queryParams}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`Retell AI API error: ${response.status} - ${errorData.message || response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Error listing Retell AI calls:', error)
      throw error
    }
  }

  /**
   * Verify webhook signature from Retell AI
   * @param {string} payload - Raw webhook payload
   * @param {string} signature - Signature from webhook headers
   * @returns {boolean} Whether signature is valid
   */
  verifyWebhookSignature(payload, signature) {
    if (!this.webhookSecret) {
      console.warn('Retell AI webhook secret not configured')
      return false
    }

    try {
      // Retell AI uses HMAC-SHA256 for webhook signatures
      const crypto = require('crypto')
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(payload)
        .digest('hex')
      
      return signature === expectedSignature
    } catch (error) {
      console.error('Error verifying webhook signature:', error)
      return false
    }
  }

  /**
   * Format phone number to E.164 format
   * @param {string} phoneNumber - Phone number in various formats
   * @returns {string} E.164 formatted phone number
   */
  formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '')
    
    // Add country code if missing (assume US +1)
    if (digits.length === 10) {
      return `+1${digits}`
    } else if (digits.length === 11 && digits.startsWith('1')) {
      return `+${digits}`
    } else if (digits.startsWith('+')) {
      return phoneNumber
    }
    
    // Return as-is if we can't determine format
    return phoneNumber
  }

  /**
   * Validate phone number format
   * @param {string} phoneNumber - Phone number to validate
   * @returns {boolean} Whether phone number is valid E.164 format
   */
  isValidPhoneNumber(phoneNumber) {
    // E.164 format: +[country code][number] (max 15 digits total)
    const e164Regex = /^\+[1-9]\d{1,14}$/
    return e164Regex.test(phoneNumber)
  }

  /**
   * Get call status mapping for our system
   * @param {string} retellStatus - Retell AI call status
   * @returns {string} Our system's call status
   */
  mapCallStatus(retellStatus) {
    const statusMap = {
      'registered': 'pending',
      'calling': 'calling',
      'in_progress': 'answered',
      'ended': 'completed',
      'failed': 'failed'
    }
    
    return statusMap[retellStatus] || 'unknown'
  }

  /**
   * Extract acknowledgment from call transcript
   * @param {string} transcript - Call transcript
   * @returns {Object} Acknowledgment analysis
   */
  analyzeAcknowledgment(transcript) {
    if (!transcript) {
      return { acknowledged: false, confidence: 0, reason: 'No transcript available' }
    }

    // Simple keyword-based acknowledgment detection
    const acknowledgmentKeywords = [
      'acknowledged', 'confirm', 'confirmed', 'received', 'understand',
      'got it', 'copy', 'roger', 'yes', 'okay', 'ok', 'will handle',
      'on my way', 'responding', 'en route'
    ]

    const transcript_lower = transcript.toLowerCase()
    const foundKeywords = acknowledgmentKeywords.filter(keyword => 
      transcript_lower.includes(keyword)
    )

    const acknowledged = foundKeywords.length > 0
    const confidence = Math.min(foundKeywords.length * 0.3, 1.0) // Max confidence of 1.0

    return {
      acknowledged,
      confidence,
      foundKeywords,
      reason: acknowledged ? 'Keywords found in transcript' : 'No acknowledgment keywords found'
    }
  }
}

// Export singleton instance
export const retellAI = new RetellAIService()
export default retellAI
