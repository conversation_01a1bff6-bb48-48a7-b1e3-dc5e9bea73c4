/**
 * Security utilities and validation for JSC Alarm Call-Out App
 * Implements security best practices and input validation
 */

import { captureError } from './sentry'

// Security configuration
const SECURITY_CONFIG = {
  // Rate limiting
  MAX_REQUESTS_PER_MINUTE: 60,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION_MS: 15 * 60 * 1000, // 15 minutes
  
  // Input validation
  MAX_INPUT_LENGTH: 1000,
  MAX_PHONE_NUMBER_LENGTH: 20,
  MAX_EMAIL_LENGTH: 254,
  
  // API security
  API_TIMEOUT_MS: 30000,
  MAX_WEBHOOK_PAYLOAD_SIZE: 1024 * 1024, // 1MB
  
  // Content Security
  ALLOWED_FILE_TYPES: ['application/json', 'text/plain'],
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
}

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()
const loginAttemptStore = new Map<string, { attempts: number; lockedUntil?: number }>()

/**
 * Input sanitization and validation
 */
export class InputValidator {
  /**
   * Sanitize string input to prevent XSS
   */
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string')
    }
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, SECURITY_CONFIG.MAX_INPUT_LENGTH)
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    if (!email || email.length > SECURITY_CONFIG.MAX_EMAIL_LENGTH) {
      return false
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Validate phone number format (E.164)
   */
  static validatePhoneNumber(phone: string): boolean {
    if (!phone || phone.length > SECURITY_CONFIG.MAX_PHONE_NUMBER_LENGTH) {
      return false
    }
    
    const phoneRegex = /^\+[1-9]\d{1,14}$/
    return phoneRegex.test(phone)
  }

  /**
   * Validate UUID format
   */
  static validateUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(uuid)
  }

  /**
   * Validate alarm status
   */
  static validateAlarmStatus(status: string): boolean {
    const validStatuses = ['received', 'acknowledged', 'resolved', 'escalated']
    return validStatuses.includes(status)
  }

  /**
   * Validate building ID format
   */
  static validateBuildingId(buildingId: string): boolean {
    // Building IDs should follow pattern: bldg-[random-id]@domain
    const buildingIdRegex = /^bldg-[a-zA-Z0-9-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return buildingIdRegex.test(buildingId)
  }

  /**
   * Validate webhook signature format
   */
  static validateWebhookSignature(signature: string): boolean {
    // Should be a hex string
    const hexRegex = /^[a-f0-9]{64}$/i
    return hexRegex.test(signature)
  }
}

/**
 * Rate limiting functionality
 */
export class RateLimiter {
  /**
   * Check if request is within rate limit
   */
  static checkRateLimit(identifier: string, maxRequests = SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE): boolean {
    const now = Date.now()
    const windowStart = now - 60000 // 1 minute window
    
    const current = rateLimitStore.get(identifier)
    
    if (!current || current.resetTime < now) {
      // Reset or initialize
      rateLimitStore.set(identifier, { count: 1, resetTime: now + 60000 })
      return true
    }
    
    if (current.count >= maxRequests) {
      return false
    }
    
    current.count++
    return true
  }

  /**
   * Check login attempts and implement lockout
   */
  static checkLoginAttempts(identifier: string): { allowed: boolean; lockedUntil?: number } {
    const now = Date.now()
    const attempts = loginAttemptStore.get(identifier)
    
    if (!attempts) {
      return { allowed: true }
    }
    
    // Check if still locked out
    if (attempts.lockedUntil && attempts.lockedUntil > now) {
      return { allowed: false, lockedUntil: attempts.lockedUntil }
    }
    
    // Reset if lockout period has passed
    if (attempts.lockedUntil && attempts.lockedUntil <= now) {
      loginAttemptStore.delete(identifier)
      return { allowed: true }
    }
    
    // Check if max attempts reached
    if (attempts.attempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
      const lockedUntil = now + SECURITY_CONFIG.LOCKOUT_DURATION_MS
      loginAttemptStore.set(identifier, { ...attempts, lockedUntil })
      return { allowed: false, lockedUntil }
    }
    
    return { allowed: true }
  }

  /**
   * Record failed login attempt
   */
  static recordFailedLogin(identifier: string): void {
    const current = loginAttemptStore.get(identifier) || { attempts: 0 }
    current.attempts++
    loginAttemptStore.set(identifier, current)
  }

  /**
   * Clear login attempts on successful login
   */
  static clearLoginAttempts(identifier: string): void {
    loginAttemptStore.delete(identifier)
  }
}

/**
 * API security utilities
 */
export class ApiSecurity {
  /**
   * Validate API request headers
   */
  static validateHeaders(headers: Record<string, string>): boolean {
    // Check for required security headers
    const requiredHeaders = ['user-agent', 'content-type']
    
    for (const header of requiredHeaders) {
      if (!headers[header]) {
        return false
      }
    }
    
    // Validate content-type
    const contentType = headers['content-type']
    if (!SECURITY_CONFIG.ALLOWED_FILE_TYPES.some(type => contentType.includes(type))) {
      return false
    }
    
    return true
  }

  /**
   * Validate webhook payload
   */
  static validateWebhookPayload(payload: unknown, signature: string): boolean {
    try {
      // Check payload size
      const payloadString = JSON.stringify(payload)
      if (payloadString.length > SECURITY_CONFIG.MAX_WEBHOOK_PAYLOAD_SIZE) {
        return false
      }
      
      // Validate signature format
      if (!InputValidator.validateWebhookSignature(signature)) {
        return false
      }
      
      return true
    } catch (error) {
      captureError(error as Error, { operation: 'validateWebhookPayload' })
      return false
    }
  }

  /**
   * Generate secure random token
   */
  static generateSecureToken(length = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    
    return result
  }

  /**
   * Validate environment configuration
   */
  static validateEnvironmentConfig(): { valid: boolean; issues: string[] } {
    const issues: string[] = []
    
    // Check required environment variables
    const requiredEnvVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY',
      'VITE_SENTRY_DSN'
    ]
    
    for (const envVar of requiredEnvVars) {
      if (!import.meta.env[envVar]) {
        issues.push(`Missing required environment variable: ${envVar}`)
      }
    }
    
    // Check for placeholder values in production
    if (import.meta.env.PROD) {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
      if (supabaseUrl?.includes('placeholder')) {
        issues.push('Production environment using placeholder Supabase URL')
      }
      
      const sentryDsn = import.meta.env.VITE_SENTRY_DSN
      if (sentryDsn?.includes('test') || sentryDsn?.includes('placeholder')) {
        issues.push('Production environment using test/placeholder Sentry DSN')
      }
    }
    
    return {
      valid: issues.length === 0,
      issues
    }
  }
}

/**
 * Content Security Policy utilities
 */
export class ContentSecurity {
  /**
   * Generate CSP header value
   */
  static generateCSPHeader(): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://js.sentry-cdn.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https:",
      "connect-src 'self' https://*.supabase.co https://*.sentry.io https://api.retellai.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ]
    
    return directives.join('; ')
  }

  /**
   * Sanitize HTML content
   */
  static sanitizeHTML(html: string): string {
    // Basic HTML sanitization - in production, use a library like DOMPurify
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
  }
}

/**
 * Security audit utilities
 */
export class SecurityAudit {
  /**
   * Perform comprehensive security check
   */
  static performSecurityAudit(): {
    passed: boolean
    issues: Array<{ severity: 'low' | 'medium' | 'high' | 'critical'; message: string }>
  } {
    const issues: Array<{ severity: 'low' | 'medium' | 'high' | 'critical'; message: string }> = []
    
    // Check environment configuration
    const envCheck = ApiSecurity.validateEnvironmentConfig()
    if (!envCheck.valid) {
      envCheck.issues.forEach(issue => {
        issues.push({ severity: 'high', message: issue })
      })
    }
    
    // Check for HTTPS in production
    if (import.meta.env.PROD && location.protocol !== 'https:') {
      issues.push({ 
        severity: 'critical', 
        message: 'Application not served over HTTPS in production' 
      })
    }
    
    // Check for secure cookie settings
    if (import.meta.env.PROD && document.cookie.includes('Secure=false')) {
      issues.push({ 
        severity: 'medium', 
        message: 'Cookies not marked as Secure in production' 
      })
    }
    
    // Check for CSP header
    const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]')
    if (!metaCSP) {
      issues.push({ 
        severity: 'medium', 
        message: 'Content Security Policy not implemented' 
      })
    }
    
    return {
      passed: issues.filter(i => i.severity === 'critical' || i.severity === 'high').length === 0,
      issues
    }
  }
}

// Export security configuration for testing
export { SECURITY_CONFIG }
