/**
 * Sentry Configuration for JSC Alarm Call-Out App
 * Production-ready error monitoring and performance tracking
 */

import * as Sentry from '@sentry/react'
import React from 'react'

// Sentry DSN from the existing alarm-call-out project
const SENTRY_DSN = 'https://<EMAIL>/4509431416487937'

// Environment configuration
const environment = import.meta.env.MODE || 'development'
const release = import.meta.env.VITE_SENTRY_RELEASE || `jsc-alarm-app@${import.meta.env.VITE_APP_VERSION || 'dev'}`

/**
 * Initialize Sentry with production-ready configuration
 */
export function initSentry() {
  Sentry.init({
    dsn: SENTRY_DSN,
    environment,
    release,
    
    // Performance monitoring
    tracesSampleRate: environment === 'production' ? 0.1 : 1.0, // 10% sampling in production
    
    // Session replay for debugging (only in production for critical errors)
    replaysSessionSampleRate: 0.0, // No session replay by default
    replaysOnErrorSampleRate: environment === 'production' ? 0.1 : 1.0, // 10% on errors in production
    
    // Integration configuration
    integrations: [
      // Browser tracing for performance monitoring
      Sentry.browserTracingIntegration(),

      // Session replay integration
      Sentry.replayIntegration({
        // Mask sensitive data
        maskAllText: true,
        maskAllInputs: true,
        blockAllMedia: true,
      }),

      // Browser profiling (only in production)
      ...(environment === 'production' ? [
        Sentry.browserProfilingIntegration()
      ] : []),
    ],
    
    // Error filtering to avoid noise
    beforeSend(event, hint) {
      // Filter out browser extension errors (integrate with existing error filter)
      const error = hint.originalException
      
      if (shouldFilterSentryError(error)) {
        return null // Don't send to Sentry
      }
      
      // Filter out development-only errors
      if (environment === 'development') {
        // Allow all errors in development for debugging
        return event
      }
      
      // Production filtering
      if (event.exception) {
        const exception = event.exception.values?.[0]
        if (exception) {
          // Filter out common browser/network errors that aren't actionable
          const ignoredErrors = [
            'Network Error',
            'NetworkError',
            'Failed to fetch',
            'Load failed',
            'Script error',
            'ResizeObserver loop limit exceeded',
            'Non-Error promise rejection captured',
          ]
          
          if (ignoredErrors.some(ignored => 
            exception.value?.includes(ignored) || exception.type?.includes(ignored)
          )) {
            return null
          }
        }
      }
      
      return event
    },
    
    // Performance filtering
    beforeSendTransaction(event) {
      // Filter out very fast transactions to reduce noise
      if (event.start_timestamp && event.timestamp) {
        const duration = (event.timestamp - event.start_timestamp) * 1000
        if (duration < 10) { // Less than 10ms
          return null
        }
      }
      
      return event
    },
    
    // Additional configuration for production
    ...(environment === 'production' && {
      // Enable automatic error boundary creation
      autoSessionTracking: true,
      
      // Capture unhandled promise rejections
      captureUnhandledRejections: true,
      
      // Maximum breadcrumbs to keep
      maxBreadcrumbs: 50,
      
      // Attach stack traces to pure capture message calls
      attachStacktrace: true,
    }),
  })
}

/**
 * Enhanced error filtering that integrates with existing error filter
 * @param {Error|string} error - The error to check
 * @returns {boolean} - True if error should be filtered out
 */
function shouldFilterSentryError(error) {
  if (!error) return false
  
  const errorString = error.toString ? error.toString() : String(error)
  const errorMessage = error.message || errorString
  const errorStack = error.stack || ''
  
  // Browser extension patterns (from existing errorFilter.js)
  const extensionPatterns = [
    /chrome-extension:/,
    /moz-extension:/,
    /safari-extension:/,
    /ms-browser-extension:/,
    /extension\//,
    /extensions\//,
    /NonExistentClass/,
    /Object Not Found Matching Id/,
    /atomicFindClose/,
    /conduitPage/,
    /anonymous function/,
    /Can't find variable: PasswordCredential/,
    /SecurityError.*localStorage/,
    /QuotaExceededError/,
  ]
  
  // Check if error matches extension patterns
  const matchesExtensionPattern = extensionPatterns.some(pattern => 
    pattern.test(errorMessage) || pattern.test(errorStack)
  )
  
  if (matchesExtensionPattern) {
    console.debug('[Sentry] Filtered browser extension error:', errorMessage)
    return true
  }
  
  // Additional Sentry-specific filters
  const sentryIgnorePatterns = [
    /Script error/,
    /Network request failed/,
    /Loading chunk \d+ failed/,
    /Loading CSS chunk/,
    /ChunkLoadError/,
  ]
  
  return sentryIgnorePatterns.some(pattern => 
    pattern.test(errorMessage) || pattern.test(errorStack)
  )
}

/**
 * Set user context for better error tracking
 * @param {Object} user - User information
 */
export function setSentryUser(user) {
  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.email, // Use email as username for building management context
    // Don't include sensitive information like phone numbers or addresses
  })
}

/**
 * Set custom context for alarm-related operations
 * @param {Object} context - Alarm context information
 */
export function setSentryAlarmContext(context) {
  Sentry.setContext('alarm', {
    building_id: context.building_id,
    alarm_type: context.alarm_type,
    severity: context.severity,
    // Don't include sensitive building details
  })
}

/**
 * Set custom context for building operations
 * @param {Object} context - Building context information
 */
export function setSentryBuildingContext(context) {
  Sentry.setContext('building', {
    building_id: context.building_id,
    building_type: context.building_type,
    // Don't include sensitive location details
  })
}

/**
 * Capture a custom error with additional context
 * @param {Error} error - The error to capture
 * @param {Object} extra - Additional context
 */
export function captureError(error, extra = {}) {
  Sentry.withScope((scope) => {
    // Add extra context
    Object.keys(extra).forEach(key => {
      scope.setExtra(key, extra[key])
    })
    
    // Add timestamp
    scope.setExtra('timestamp', new Date().toISOString())
    
    Sentry.captureException(error)
  })
}

/**
 * Capture a custom message with context
 * @param {string} message - The message to capture
 * @param {string} level - The severity level
 * @param {Object} extra - Additional context
 */
export function captureMessage(message, level = 'info', extra = {}) {
  Sentry.withScope((scope) => {
    // Add extra context
    Object.keys(extra).forEach(key => {
      scope.setExtra(key, extra[key])
    })
    
    scope.setLevel(level)
    Sentry.captureMessage(message)
  })
}

/**
 * Create a Sentry-wrapped React Router (if using React Router)
 * Note: This app doesn't use React Router currently, but this is here for future use
 * @param {Array} routes - Router configuration
 */
export function createSentryRouter() {
  // This would require react-router-dom to be installed
  // return Sentry.wrapCreateBrowserRouter(createBrowserRouter)(routes)
  console.warn('createSentryRouter called but React Router is not configured')
  return null
}

// Export Sentry for direct access when needed
export { Sentry }
