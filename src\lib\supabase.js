import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI4MDAsImV4cCI6MTk2MDc2ODgwMH0.placeholder'

// Check if we have real Supabase credentials
export const hasSupabaseConfig = !!(
  import.meta.env.VITE_SUPABASE_URL &&
  import.meta.env.VITE_SUPABASE_ANON_KEY &&
  import.meta.env.VITE_SUPABASE_URL !== 'your_supabase_project_url' &&
  import.meta.env.VITE_SUPABASE_ANON_KEY !== 'your_supabase_anon_key' &&
  import.meta.env.VITE_SUPABASE_URL.startsWith('https://') &&
  import.meta.env.VITE_SUPABASE_URL.includes('.supabase.co')
)

// Create a singleton Supabase client to avoid multiple instances during HMR
// Store on globalThis to persist across hot reloads
const SUPABASE_KEY = Symbol.for('supabase.client')

function getSupabaseClient() {
  if (!globalThis[SUPABASE_KEY]) {
    globalThis[SUPABASE_KEY] = createClient(supabaseUrl, supabaseAnonKey)
  }
  return globalThis[SUPABASE_KEY]
}

export const supabase = getSupabaseClient()
