/**
 * Webhook handler utilities for processing Mailgun alarm notifications
 * 
 * Note: This is a client-side utility for demonstration purposes.
 * In production, you would need a proper server-side webhook endpoint.
 */

import { parseAlarmEmail, validateWebhookSignature } from './alarmUtils'

/**
 * Process a Mailgun webhook payload
 * This function simulates what would happen on a server-side webhook endpoint
 * 
 * @param {Object} webhookPayload - The raw webhook payload from Mailgun
 * @param {Function} onAlarmProcessed - Callback function to handle processed alarm
 * @returns {Object} Processing result
 */
export async function processMailgunWebhook(webhookPayload, onAlarmProcessed) {
  try {
    console.log('Processing Mailgun webhook:', webhookPayload)

    // Validate the webhook signature (placeholder implementation)
    const isValidSignature = validateWebhookSignature(
      webhookPayload.signature,
      webhookPayload.timestamp,
      webhookPayload.token
    )

    if (!isValidSignature) {
      console.error('Invalid webhook signature')
      return {
        success: false,
        error: 'Invalid webhook signature',
        statusCode: 401
      }
    }

    // Parse the alarm email content
    const parsedAlarm = parseAlarmEmail(webhookPayload)
    
    console.log('Parsed alarm data:', parsedAlarm)

    // Call the callback function to process the alarm
    if (typeof onAlarmProcessed === 'function') {
      const result = await onAlarmProcessed(parsedAlarm, webhookPayload)
      return {
        success: true,
        data: result,
        statusCode: 200
      }
    }

    return {
      success: true,
      data: parsedAlarm,
      statusCode: 200
    }

  } catch (error) {
    console.error('Error processing webhook:', error)
    return {
      success: false,
      error: error.message,
      statusCode: 500
    }
  }
}



/**
 * Webhook endpoint setup instructions
 * This provides guidance for setting up a proper server-side webhook endpoint
 */
export const WEBHOOK_SETUP_INSTRUCTIONS = {
  title: "Setting Up Mailgun Webhook Endpoint",
  description: "To receive real alarm notifications, you need to set up a server-side webhook endpoint.",
  
  steps: [
    {
      step: 1,
      title: "Create a Server-Side Endpoint",
      description: "Set up an HTTP endpoint that can receive POST requests from Mailgun",
      example: `
// Example using Express.js
app.post('/webhook/mailgun', express.raw({type: 'application/x-www-form-urlencoded'}), (req, res) => {
  const webhookData = req.body;
  
  // Process the webhook
  processMailgunWebhook(webhookData, async (parsedAlarm) => {
    // Save to Supabase database
    const result = await supabase
      .from('alarm_notifications')
      .insert(parsedAlarm);
    
    return result;
  });
  
  res.status(200).send('OK');
});`
    },
    {
      step: 2,
      title: "Configure Mailgun Webhook",
      description: "In your Mailgun dashboard, set up a webhook to point to your endpoint",
      details: [
        "Go to Mailgun Dashboard > Webhooks",
        "Add a new webhook for 'Incoming Messages'",
        "Set URL to: https://your-domain.com/webhook/mailgun",
        "Enable webhook and test the connection"
      ]
    },
    {
      step: 3,
      title: "Implement Signature Verification",
      description: "Verify webhook authenticity using Mailgun's signature",
      example: `
const crypto = require('crypto');

function verifyWebhookSignature(signature, timestamp, token, apiKey) {
  const value = timestamp + token;
  const hash = crypto
    .createHmac('sha256', apiKey)
    .update(value)
    .digest('hex');
  
  return hash === signature;
}`
    },
    {
      step: 4,
      title: "Handle Email Parsing",
      description: "Parse incoming emails and extract alarm information",
      details: [
        "Extract alarm type, severity, and details from email body",
        "Parse timestamps and convert to proper format",
        "Map building email addresses to building records",
        "Store raw webhook data for debugging"
      ]
    },
    {
      step: 5,
      title: "Error Handling and Logging",
      description: "Implement proper error handling and logging",
      details: [
        "Log all incoming webhooks for audit trail",
        "Handle parsing errors gracefully",
        "Implement retry logic for database failures",
        "Send alerts for webhook processing failures"
      ]
    }
  ],
  
  security: {
    title: "Security Considerations",
    points: [
      "Always verify webhook signatures",
      "Use HTTPS for webhook endpoints",
      "Implement rate limiting",
      "Validate and sanitize all input data",
      "Store webhook secrets securely",
      "Monitor for suspicious activity"
    ]
  },
  

}

/**
 * Validate webhook payload structure
 * @param {Object} payload - Webhook payload to validate
 * @returns {Object} Validation result
 */
export function validateWebhookPayload(payload) {
  const requiredFields = ['sender', 'recipient', 'subject', 'body-plain']
  const missingFields = requiredFields.filter(field => !payload[field])
  
  if (missingFields.length > 0) {
    return {
      valid: false,
      error: `Missing required fields: ${missingFields.join(', ')}`
    }
  }
  
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(payload.sender)) {
    return {
      valid: false,
      error: 'Invalid sender email format'
    }
  }
  
  if (!emailRegex.test(payload.recipient)) {
    return {
      valid: false,
      error: 'Invalid recipient email format'
    }
  }
  
  return { valid: true }
}

/**
 * Format webhook response for Mailgun
 * @param {boolean} success - Whether processing was successful
 * @param {string} message - Response message
 * @returns {Object} Formatted response
 */
export function formatWebhookResponse(success, message = '') {
  return {
    status: success ? 200 : 500,
    body: success ? 'OK' : 'Error',
    message: message
  }
}
