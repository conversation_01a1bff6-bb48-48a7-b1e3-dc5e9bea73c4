import { describe, it, expect } from 'vitest'

/**
 * Demonstration test showing the fix for the search functionality crash
 * This test simulates the exact scenario that was causing the crash
 */
describe('Search Functionality Fix - Demonstration', () => {
  // Mock devices with null values that were causing the crash
  const devicesWithNullValues = [
    {
      id: '1',
      station_name: null, // This was causing the crash
      host_id: 'RTR-001',
      ip_address: '***********',
      device_type: 'BACnet router',
      building: { name: 'Main Office' }
    },
    {
      id: '2',
      station_name: 'Switch-02',
      host_id: null, // This was causing the crash
      ip_address: '***********',
      device_type: 'Switch',
      building: { name: 'Main Office' }
    },
    {
      id: '3',
      station_name: 'Access-Point-03',
      host_id: 'AP-003',
      ip_address: null, // This was causing the crash
      device_type: null, // This was causing the crash
      building: null // This was causing the crash
    }
  ]

  // The OLD (broken) filter function that was causing the crash
  const oldFilterFunction = (devices, searchTerm) => {
    return devices.filter(device => {
      const matchesSearch = !searchTerm || 
        device.station_name.toLowerCase().includes(searchTerm.toLowerCase()) || // ❌ CRASH HERE
        device.host_id.toLowerCase().includes(searchTerm.toLowerCase()) || // ❌ CRASH HERE
        device.ip_address.includes(searchTerm) || // ❌ CRASH HERE
        device.device_type.toLowerCase().includes(searchTerm.toLowerCase()) || // ❌ CRASH HERE
        device.building?.name.toLowerCase().includes(searchTerm.toLowerCase()) // ❌ CRASH HERE
      
      return matchesSearch
    })
  }

  // The NEW (fixed) filter function that handles null values safely
  const newFilterFunction = (devices, searchTerm) => {
    return devices.filter(device => {
      const matchesSearch = !searchTerm || 
        (device.station_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) || // ✅ SAFE
        (device.host_id?.toLowerCase() || '').includes(searchTerm.toLowerCase()) || // ✅ SAFE
        (device.ip_address || '').includes(searchTerm) || // ✅ SAFE
        (device.device_type?.toLowerCase() || '').includes(searchTerm.toLowerCase()) || // ✅ SAFE
        (device.building?.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) // ✅ SAFE
      
      return matchesSearch
    })
  }

  it('should demonstrate that the old function crashes with null values', () => {
    expect(() => {
      oldFilterFunction(devicesWithNullValues, 'router')
    }).toThrow('Cannot read properties of null (reading \'toLowerCase\')')
  })

  it('should demonstrate that the new function handles null values safely', () => {
    expect(() => {
      const results = newFilterFunction(devicesWithNullValues, 'router')
      expect(Array.isArray(results)).toBe(true)
    }).not.toThrow()
  })

  it('should return correct results with the new function despite null values', () => {
    // Search for "router" - should find the BACnet router despite null station_name
    const routerResults = newFilterFunction(devicesWithNullValues, 'router')
    expect(routerResults).toHaveLength(1)
    expect(routerResults[0].device_type).toBe('BACnet router')

    // Search for "RTR-001" - should find device despite null station_name
    const hostResults = newFilterFunction(devicesWithNullValues, 'RTR-001')
    expect(hostResults).toHaveLength(1)
    expect(hostResults[0].host_id).toBe('RTR-001')

    // Search for "Switch-02" - should find device despite null host_id
    const switchResults = newFilterFunction(devicesWithNullValues, 'Switch-02')
    expect(switchResults).toHaveLength(1)
    expect(switchResults[0].station_name).toBe('Switch-02')

    // Search for empty string - should return all devices
    const allResults = newFilterFunction(devicesWithNullValues, '')
    expect(allResults).toHaveLength(3)

    // Search for non-existent term - should return empty array
    const noResults = newFilterFunction(devicesWithNullValues, 'nonexistent')
    expect(noResults).toHaveLength(0)
  })

  it('should handle edge cases safely', () => {
    // Test with null search term
    expect(() => {
      const results = newFilterFunction(devicesWithNullValues, null)
      expect(results).toHaveLength(3) // Should return all devices
    }).not.toThrow()

    // Test with undefined search term
    expect(() => {
      const results = newFilterFunction(devicesWithNullValues, undefined)
      expect(results).toHaveLength(3) // Should return all devices
    }).not.toThrow()

    // Test with empty devices array
    expect(() => {
      const results = newFilterFunction([], 'test')
      expect(results).toHaveLength(0)
    }).not.toThrow()
  })
})
