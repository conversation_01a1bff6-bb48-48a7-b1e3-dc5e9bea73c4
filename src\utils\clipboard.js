/**
 * Clipboard utilities for secure copy operations
 */

/**
 * Copy text to clipboard with fallback support
 * @param {string} text - Text to copy to clipboard
 * @param {string} label - Label for user feedback (e.g., "Username", "Password")
 * @returns {Promise<{success: boolean, message: string}>}
 */
export async function copyToClipboard(text, label = 'Text') {
  if (!text || typeof text !== 'string') {
    return {
      success: false,
      message: `No ${label.toLowerCase()} to copy`
    }
  }

  try {
    // Modern clipboard API (preferred)
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return {
        success: true,
        message: `${label} copied to clipboard`
      }
    }
    
    // Fallback for older browsers or non-secure contexts
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    
    if (successful) {
      return {
        success: true,
        message: `${label} copied to clipboard`
      }
    } else {
      throw new Error('Copy command failed')
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return {
      success: false,
      message: `Failed to copy ${label.toLowerCase()}`
    }
  }
}

/**
 * Copy button component props helper
 * @param {string} text - Text to copy
 * @param {string} label - Label for the copy action
 * @param {Function} onSuccess - Success callback
 * @param {Function} onError - Error callback
 * @returns {Object} Props for copy button
 */
export function createCopyButtonProps(text, label, onSuccess, onError) {
  return {
    onClick: async (e) => {
      e.preventDefault()
      e.stopPropagation()
      
      const result = await copyToClipboard(text, label)
      
      if (result.success) {
        onSuccess?.(result.message)
      } else {
        onError?.(result.message)
      }
    },
    title: `Copy ${label}`,
    'aria-label': `Copy ${label} to clipboard`
  }
}

/**
 * Secure copy function that masks sensitive data in logs
 * @param {string} text - Text to copy
 * @param {string} label - Label for user feedback
 * @param {boolean} isSensitive - Whether the data is sensitive (passwords, etc.)
 * @returns {Promise<{success: boolean, message: string}>}
 */
export async function secureCopyToClipboard(text, label = 'Text', isSensitive = false) {
  if (!text || typeof text !== 'string') {
    return {
      success: false,
      message: `No ${label.toLowerCase()} to copy`
    }
  }

  try {
    // Modern clipboard API (preferred)
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      
      // Log success without exposing sensitive data
      if (isSensitive) {
        console.log(`Sensitive ${label.toLowerCase()} copied to clipboard`)
      } else {
        console.log(`${label} copied to clipboard:`, text.substring(0, 10) + '...')
      }
      
      return {
        success: true,
        message: `${label} copied to clipboard`
      }
    }
    
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    
    if (successful) {
      if (isSensitive) {
        console.log(`Sensitive ${label.toLowerCase()} copied to clipboard`)
      } else {
        console.log(`${label} copied to clipboard:`, text.substring(0, 10) + '...')
      }
      
      return {
        success: true,
        message: `${label} copied to clipboard`
      }
    } else {
      throw new Error('Copy command failed')
    }
  } catch (error) {
    console.error(`Failed to copy ${label.toLowerCase()} to clipboard:`, error.message)
    return {
      success: false,
      message: `Failed to copy ${label.toLowerCase()}`
    }
  }
}
