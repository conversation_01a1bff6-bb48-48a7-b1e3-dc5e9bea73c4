/**
 * Network device credential utilities
 * Handles decryption and processing of device credentials
 */

/**
 * Decrypts all credential fields for a network device
 * @param {Object} device - The device object containing encrypted passwords
 * @returns {Promise<Object>} Object containing decrypted credential fields
 */
export const getDecryptedCredentials = async (device) => {
  const credentials = {}

  if (!device) {
    return credentials
  }

  try {
    const { decryptPassword } = await import('../passwordEncryption.js')

    // Decrypt each password field if it exists
    const passwordFields = [
      'station_password_encrypted',
      'windows_password_encrypted',
      'platform_password_encrypted',
      'passphrase_encrypted'
    ]

    for (const field of passwordFields) {
      if (device[field]) {
        try {
          const fieldName = field.replace('_encrypted', '')
          credentials[fieldName] = decryptPassword(device[field])
        } catch (error) {
          console.error(`Error decrypting ${field}:`, error)
          // Set to empty string if decryption fails
          credentials[field.replace('_encrypted', '')] = ''
        }
      }
    }
  } catch (error) {
    console.error('Error decrypting passwords:', error)
  }

  return credentials
}
