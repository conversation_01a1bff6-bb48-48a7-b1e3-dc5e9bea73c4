/**
 * Network Device Management Event Handlers
 * Centralizes all event handling logic for the NetworkDeviceManagement component
 */
import {
  validateNetworkDeviceForm,
  cleanNetworkDeviceFormData,
  generateHostIdSuggestion,
  clearAutoSavedNetworkDeviceFormData
} from '../lib/networkDeviceValidation'
import {
  exportDevicesToCSV,
  downloadCSV,
  generateCSVTemplate,
  parseCSVToDevices,
  validateCSVFile,
  readCSVFile
} from '../lib/networkDeviceImportExport'

export const createNetworkDeviceHandlers = (state, actions) => {
  const {
    user,
    editingDevice,
    formData,
    formErrors,
    formWarnings,
    importPreview,
    importErrors,
    filteredDevices,
    buildings,
    isHostIdUnique,
    isIpAddressUnique,
    createDevice,
    updateDevice,
    deleteDevice,
    toggleDeviceStatus,
    validateDeviceBatch
  } = state

  const {
    setFormData,
    setFormErrors,
    setFormWarnings,
    setEditingDevice,
    setShowForm,
    setIsSubmitting,
    setShowDeleteConfirm,
    setShowImportModal,
    setImportFile,
    setImportPreview,
    setImportErrors,
    setIsImporting,
    setShowPasswords,
    showNotification,
    resetForm
  } = actions

  const handleOpenForm = async (device = null) => {
    if (device) {
      setEditingDevice(device)

      // Decrypt passwords for display
      let decryptedPasswords = {
        station_password: '',
        windows_password: '',
        platform_password: '',
        passphrase: ''
      }

      try {
        const { decryptPassword } = await import('../utils/passwordEncryption.js')

        if (device.station_password_encrypted) {
          decryptedPasswords.station_password = decryptPassword(device.station_password_encrypted)
        }
        if (device.windows_password_encrypted) {
          decryptedPasswords.windows_password = decryptPassword(device.windows_password_encrypted)
        }
        if (device.platform_password_encrypted) {
          decryptedPasswords.platform_password = decryptPassword(device.platform_password_encrypted)
        }
        if (device.passphrase_encrypted) {
          decryptedPasswords.passphrase = decryptPassword(device.passphrase_encrypted)
        }
      } catch (error) {
        console.error('Error decrypting passwords:', error)
      }

      setFormData({
        building_id: device.building_id || '',
        station_name: device.station_name || '',
        device_type: device.device_type || '',
        host_id: device.host_id || '',
        ip_address: device.ip_address || '',
        subnet_mask: device.subnet_mask || '*************',
        gateway: device.gateway || '',
        internal_dns_server_1: device.internal_dns_server_1 || '',
        internal_dns_server_2: device.internal_dns_server_2 || '',
        station_username: device.station_username || '',
        station_password: decryptedPasswords.station_password,
        windows_username: device.windows_username || '',
        windows_password: decryptedPasswords.windows_password,
        platform_username: device.platform_username || '',
        platform_password: decryptedPasswords.platform_password,
        passphrase: decryptedPasswords.passphrase,
        software_version: device.software_version || '',
        notes: device.notes || '',
        is_active: device.is_active
      })
    } else {
      resetForm()
    }
    setShowForm(true)
  }

  const handleCloseForm = () => {
    setShowForm(false)
    resetForm()
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Auto-generate host ID from station name
    if (field === 'station_name' && value && !editingDevice) {
      const suggestion = generateHostIdSuggestion(value)
      if (suggestion && !formData.host_id) {
        setFormData(prev => ({ ...prev, host_id: suggestion }))
      }
    }
    
    // Clear field-specific errors and warnings
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
    if (formWarnings[field]) {
      setFormWarnings(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = async () => {
    const cleanedData = cleanNetworkDeviceFormData(formData)
    const validation = validateNetworkDeviceForm(cleanedData, !!editingDevice)

    // Check host ID uniqueness
    if (validation.isValid && cleanedData.host_id && cleanedData.building_id) {
      const isUnique = await isHostIdUnique(
        cleanedData.host_id,
        cleanedData.building_id,
        editingDevice ? editingDevice.id : null
      )
      if (!isUnique) {
        validation.isValid = false
        validation.errors.host_id = 'This host ID is already in use for this building'
      }
    }

    // Check IP address uniqueness
    if (validation.isValid && cleanedData.ip_address && cleanedData.building_id) {
      const isUnique = await isIpAddressUnique(
        cleanedData.ip_address,
        cleanedData.building_id,
        editingDevice ? editingDevice.id : null
      )
      if (!isUnique) {
        validation.isValid = false
        validation.errors.ip_address = 'This IP address is already in use for this building'
      }
    }

    setFormErrors(validation.errors)
    setFormWarnings(validation.warnings || {})
    return validation.isValid
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!user) {
      showNotification('You must be logged in to manage network devices', 'error')
      return
    }

    setIsSubmitting(true)
    
    try {
      const isValid = await validateForm()
      if (!isValid) {
        setIsSubmitting(false)
        return
      }

      const cleanedData = cleanNetworkDeviceFormData(formData)
      let result

      if (editingDevice) {
        result = await updateDevice(editingDevice.id, cleanedData)
      } else {
        result = await createDevice(cleanedData)
      }

      if (result.success) {
        showNotification(
          `Network device ${editingDevice ? 'updated' : 'created'} successfully`,
          'success'
        )
        handleCloseForm()
      } else {
        showNotification(result.error, 'error')
      }
    } catch {
      showNotification('An unexpected error occurred', 'error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (deviceId) => {
    try {
      const result = await deleteDevice(deviceId)
      if (result.success) {
        showNotification('Network device deleted successfully', 'success')
        setShowDeleteConfirm(null)
      } else {
        showNotification(result.error, 'error')
      }
    } catch {
      showNotification('Failed to delete network device', 'error')
    }
  }

  const handleToggleStatus = async (deviceId, currentStatus) => {
    try {
      const result = await toggleDeviceStatus(deviceId, !currentStatus)
      if (result.success) {
        showNotification(
          `Device ${!currentStatus ? 'activated' : 'deactivated'} successfully`,
          'success'
        )
      } else {
        showNotification(result.error, 'error')
      }
    } catch {
      showNotification('Failed to update device status', 'error')
    }
  }

  const handleExportDevices = () => {
    try {
      const csvContent = exportDevicesToCSV(filteredDevices, buildings)
      const timestamp = new Date().toISOString().split('T')[0]
      downloadCSV(csvContent, `network_devices_${timestamp}.csv`)
      showNotification('Devices exported successfully', 'success')
    } catch {
      showNotification('Failed to export devices', 'error')
    }
  }

  const handleDownloadTemplate = () => {
    try {
      const csvContent = generateCSVTemplate(buildings)
      downloadCSV(csvContent, 'network_devices_template.csv')
      showNotification('Template downloaded successfully', 'success')
    } catch {
      showNotification('Failed to download template', 'error')
    }
  }

  const handleImportFile = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    const validation = validateCSVFile(file)
    if (!validation.isValid) {
      setImportErrors(validation.errors)
      return
    }

    try {
      const content = await readCSVFile(file)
      const parseResult = parseCSVToDevices(content, buildings)

      setImportFile(file)
      setImportPreview(parseResult.devices)
      setImportErrors(parseResult.errors)

      if (parseResult.devices.length === 0 && parseResult.errors.length > 0) {
        showNotification('No valid devices found in CSV file', 'error')
      }
    } catch (error) {
      setImportErrors([`Failed to read file: ${error.message}`])
    }
  }

  const handleConfirmImport = async () => {
    if (!importPreview || importPreview.length === 0) return

    setIsImporting(true)
    let successCount = 0
    let errorCount = 0
    const detailedErrors = []

    try {
      // Enable debug mode for CSV import
      const debugMode = process.env.NODE_ENV === 'development'

      if (debugMode) {
        console.log('Starting CSV import with debug mode enabled')
        console.log('Import preview data:', importPreview)
      }

      // Perform batch validation first
      console.log('Performing batch validation...')
      const batchErrors = await validateDeviceBatch(importPreview)

      if (batchErrors.length > 0) {
        console.error('Batch validation errors:', batchErrors)
        setImportErrors([...importErrors, ...batchErrors])
        showNotification(`Import validation failed: ${batchErrors.length} conflicts found`, 'error')
        setIsImporting(false)
        return
      }

      for (let i = 0; i < importPreview.length; i++) {
        const deviceData = importPreview[i]
        const rowNumber = i + 1

        if (debugMode) {
          console.log(`Processing row ${rowNumber}:`, deviceData)
        }

        const result = await createDevice(deviceData, debugMode)
        if (result.success) {
          successCount++
          if (debugMode) {
            console.log(`Row ${rowNumber}: Success`)
          }
        } else {
          errorCount++
          const errorMessage = `Row ${rowNumber}: ${result.error}`
          detailedErrors.push(errorMessage)

          if (debugMode) {
            console.error(`Row ${rowNumber}: Failed -`, result.error)
          }
        }
      }

      // Show detailed results
      if (detailedErrors.length > 0) {
        console.error('Import errors:', detailedErrors)
        setImportErrors(detailedErrors)
      }

      showNotification(
        `Import completed: ${successCount} devices created, ${errorCount} errors`,
        successCount > 0 ? 'success' : 'error'
      )

      // Only close modal if there were no errors or user confirms
      if (errorCount === 0) {
        setShowImportModal(false)
        setImportFile(null)
        setImportPreview(null)
        setImportErrors([])
      }
    } catch (error) {
      console.error('Import failed with exception:', error)
      showNotification(`Import failed: ${error.message}`, 'error')
    } finally {
      setIsImporting(false)
    }
  }

  // Additional handlers for modal management
  const handleSetDeleteConfirm = (device) => {
    setShowDeleteConfirm(device)
  }

  const handleOpenImportModal = () => {
    setShowImportModal(true)
  }

  const handleCloseImportModal = () => {
    setShowImportModal(false)
    setImportFile(null)
    setImportPreview(null)
    setImportErrors([])
  }

  const handleTogglePassword = (type) => {
    setShowPasswords(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  const getFieldStyling = (fieldName) => {
    const hasError = formErrors[fieldName]
    const hasWarning = formWarnings[fieldName]

    if (hasError) {
      return { className: 'border-red-300' }
    } else if (hasWarning) {
      return { className: 'border-yellow-300' }
    } else {
      return { className: 'border-gray-300' }
    }
  }

  return {
    handleOpenForm,
    handleCloseForm,
    handleInputChange,
    handleSubmit,
    handleDelete,
    handleToggleStatus,
    handleExportDevices,
    handleDownloadTemplate,
    handleImportFile,
    handleConfirmImport,
    handleSetDeleteConfirm,
    handleOpenImportModal,
    handleCloseImportModal,
    handleTogglePassword,
    getFieldStyling,
    validateForm
  }
}
