/**
 * Password encryption utilities for network device credentials
 * 
 * Note: This is a basic implementation for demonstration.
 * In production, use proper encryption libraries and key management.
 */

/**
 * Simple base64 encoding for password obfuscation
 * @param {string} password - Plain text password
 * @returns {string} Encoded password
 */
export function encryptPassword(password) {
  if (!password || typeof password !== 'string') {
    return null
  }
  
  try {
    // Simple base64 encoding with a prefix to identify encrypted passwords
    const encoded = btoa(password)
    return `enc_${encoded}`
  } catch (error) {
    console.error('Password encryption failed:', error)
    return password // Fallback to plain text if encryption fails
  }
}

/**
 * Decode base64 encoded password
 * @param {string} encryptedPassword - Encoded password
 * @returns {string} Decoded password
 */
export function decryptPassword(encryptedPassword) {
  if (!encryptedPassword || typeof encryptedPassword !== 'string') {
    return ''
  }
  
  try {
    // Check if password is encrypted (has our prefix)
    if (encryptedPassword.startsWith('enc_')) {
      const encoded = encryptedPassword.substring(4) // Remove 'enc_' prefix
      return atob(encoded)
    }
    
    // If not encrypted, return as-is (for backward compatibility)
    return encryptedPassword
  } catch (error) {
    console.error('Password decryption failed:', error)
    return encryptedPassword // Return as-is if decryption fails
  }
}

/**
 * Check if a password is encrypted
 * @param {string} password - Password to check
 * @returns {boolean} True if encrypted
 */
export function isPasswordEncrypted(password) {
  return typeof password === 'string' && password.startsWith('enc_')
}

/**
 * Encrypt multiple password fields in a device object
 * @param {Object} deviceData - Device data with password fields
 * @returns {Object} Device data with encrypted passwords
 */
export function encryptDevicePasswords(deviceData) {
  const encrypted = { ...deviceData }
  
  // List of password fields to encrypt
  const passwordFields = [
    'station_password',
    'windows_password', 
    'platform_password',
    'passphrase'
  ]
  
  passwordFields.forEach(field => {
    if (encrypted[field] && typeof encrypted[field] === 'string' && encrypted[field].trim() !== '') {
      encrypted[field] = encryptPassword(encrypted[field])
    }
  })
  
  return encrypted
}

/**
 * Decrypt multiple password fields in a device object
 * @param {Object} deviceData - Device data with encrypted password fields
 * @returns {Object} Device data with decrypted passwords
 */
export function decryptDevicePasswords(deviceData) {
  const decrypted = { ...deviceData }
  
  // List of encrypted password fields to decrypt
  const encryptedPasswordFields = [
    'station_password_encrypted',
    'windows_password_encrypted',
    'platform_password_encrypted', 
    'passphrase_encrypted'
  ]
  
  encryptedPasswordFields.forEach(field => {
    if (decrypted[field]) {
      decrypted[field] = decryptPassword(decrypted[field])
    }
  })
  
  return decrypted
}

/**
 * Mask password for display (show only first and last characters)
 * @param {string} password - Password to mask
 * @returns {string} Masked password
 */
export function maskPassword(password) {
  if (!password || typeof password !== 'string' || password.length < 3) {
    return '***'
  }
  
  const first = password.charAt(0)
  const last = password.charAt(password.length - 1)
  const middle = '*'.repeat(Math.max(password.length - 2, 3))
  
  return `${first}${middle}${last}`
}

/**
 * Generate a secure random password
 * @param {number} length - Password length (default: 12)
 * @param {Object} options - Password generation options
 * @returns {string} Generated password
 */
export function generateSecurePassword(length = 12, options = {}) {
  const {
    includeUppercase = true,
    includeLowercase = true,
    includeNumbers = true,
    includeSymbols = true,
    excludeSimilar = true
  } = options
  
  let charset = ''
  
  if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz'
  if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  if (includeNumbers) charset += '0123456789'
  if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?'
  
  if (excludeSimilar) {
    // Remove similar looking characters
    charset = charset.replace(/[0O1lI]/g, '')
  }
  
  if (charset === '') {
    throw new Error('No character set selected for password generation')
  }
  
  let password = ''
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  
  return password
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with strength and suggestions
 */
export function validatePasswordStrength(password) {
  if (!password || typeof password !== 'string') {
    return {
      strength: 'weak',
      score: 0,
      message: 'Password is required',
      suggestions: ['Enter a password']
    }
  }
  
  let score = 0
  const suggestions = []
  
  // Length check
  if (password.length >= 8) score += 1
  else suggestions.push('Use at least 8 characters')
  
  if (password.length >= 12) score += 1
  else if (password.length >= 8) suggestions.push('Consider using 12+ characters for better security')
  
  // Character variety checks
  if (/[a-z]/.test(password)) score += 1
  else suggestions.push('Include lowercase letters')
  
  if (/[A-Z]/.test(password)) score += 1
  else suggestions.push('Include uppercase letters')
  
  if (/[0-9]/.test(password)) score += 1
  else suggestions.push('Include numbers')
  
  if (/[^a-zA-Z0-9]/.test(password)) score += 1
  else suggestions.push('Include special characters (!@#$%^&*)')
  
  // Determine strength
  let strength = 'weak'
  let message = 'Password is weak'
  
  if (score >= 5) {
    strength = 'strong'
    message = 'Password is strong'
  } else if (score >= 3) {
    strength = 'medium'
    message = 'Password is medium strength'
  }
  
  return {
    strength,
    score,
    message,
    suggestions: suggestions.length > 0 ? suggestions : ['Password meets security requirements']
  }
}
