/**
 * Debug utilities for testing password processing in network devices
 */

/**
 * Test password processing logic without database interaction
 * @param {Object} deviceData - Device data with password fields
 * @returns {Object} Processed data showing field transformations
 */
export function testPasswordProcessing(deviceData) {
  console.log('🔍 Testing Password Processing Logic')
  console.log('Input Data:', deviceData)
  
  // Simulate the password processing logic from useNetworkDevices.js
  const processedData = { ...deviceData }
  const transformations = []

  // Handle password fields - convert to encrypted fields and remove originals
  if (processedData.station_password && typeof processedData.station_password === 'string' && processedData.station_password.trim() !== '') {
    processedData.station_password_encrypted = processedData.station_password
    transformations.push(`station_password: "${processedData.station_password}" → station_password_encrypted`)
  } else {
    transformations.push(`station_password: ${processedData.station_password} → (not converted - empty/null)`)
  }
  delete processedData.station_password

  if (processedData.windows_password && typeof processedData.windows_password === 'string' && processedData.windows_password.trim() !== '') {
    processedData.windows_password_encrypted = processedData.windows_password
    transformations.push(`windows_password: "${processedData.windows_password}" → windows_password_encrypted`)
  } else {
    transformations.push(`windows_password: ${processedData.windows_password} → (not converted - empty/null)`)
  }
  delete processedData.windows_password

  if (processedData.platform_password && typeof processedData.platform_password === 'string' && processedData.platform_password.trim() !== '') {
    processedData.platform_password_encrypted = processedData.platform_password
    transformations.push(`platform_password: "${processedData.platform_password}" → platform_password_encrypted`)
  } else {
    transformations.push(`platform_password: ${processedData.platform_password} → (not converted - empty/null)`)
  }
  delete processedData.platform_password

  if (processedData.passphrase && typeof processedData.passphrase === 'string' && processedData.passphrase.trim() !== '') {
    processedData.passphrase_encrypted = processedData.passphrase
    transformations.push(`passphrase: "${processedData.passphrase}" → passphrase_encrypted`)
  } else {
    transformations.push(`passphrase: ${processedData.passphrase} → (not converted - empty/null)`)
  }
  delete processedData.passphrase

  // Clean up empty string values for optional fields
  Object.keys(processedData).forEach(key => {
    if (processedData[key] === '') {
      processedData[key] = null
    }
  })

  console.log('🔄 Field Transformations:')
  transformations.forEach(t => console.log(`  ${t}`))
  
  console.log('✅ Final Processed Data:', processedData)
  
  // Check for any remaining non-encrypted password fields
  const remainingPasswordFields = Object.keys(processedData).filter(key => 
    key.includes('password') && !key.includes('encrypted') || key === 'passphrase'
  )
  
  if (remainingPasswordFields.length > 0) {
    console.error('❌ ERROR: Non-encrypted password fields still present:', remainingPasswordFields)
  } else {
    console.log('✅ SUCCESS: All password fields properly converted or removed')
  }

  return {
    input: deviceData,
    output: processedData,
    transformations,
    hasErrors: remainingPasswordFields.length > 0,
    remainingPasswordFields
  }
}

/**
 * Test data cleaning logic
 * @param {Object} formData - Raw form data
 * @returns {Object} Cleaned data
 */
export function testDataCleaning(formData) {
  console.log('🧹 Testing Data Cleaning Logic')
  console.log('Input Form Data:', formData)
  
  // Simulate cleanNetworkDeviceFormData logic
  const cleaned = {
    building_id: formData.building_id?.trim() || '',
    station_name: formData.station_name?.trim() || '',
    device_type: formData.device_type?.trim() || null,
    host_id: formData.host_id?.trim()?.toUpperCase() || null,
    ip_address: formData.ip_address?.trim() || null,
    subnet_mask: formData.subnet_mask?.trim() || null,
    gateway: formData.gateway?.trim() || null,
    internal_dns_server_1: formData.internal_dns_server_1?.trim() || null,
    internal_dns_server_2: formData.internal_dns_server_2?.trim() || null,
    station_username: formData.station_username?.trim() || null,
    windows_username: formData.windows_username?.trim() || null,
    platform_username: formData.platform_username?.trim() || null,
    software_version: formData.software_version?.trim() || null,
    notes: formData.notes?.trim() || null,
    is_active: formData.is_active !== undefined ? formData.is_active : true
  }

  // Handle passwords separately - always include them for processing
  cleaned.station_password = formData.station_password || null
  cleaned.windows_password = formData.windows_password || null
  cleaned.platform_password = formData.platform_password || null
  cleaned.passphrase = formData.passphrase || null

  console.log('✅ Cleaned Data:', cleaned)
  
  // Check that password fields are included
  const passwordFields = ['station_password', 'windows_password', 'platform_password', 'passphrase']
  const includedPasswordFields = passwordFields.filter(field => field in cleaned)
  
  console.log(`📋 Password Fields Included: ${includedPasswordFields.length}/${passwordFields.length}`)
  includedPasswordFields.forEach(field => {
    console.log(`  ${field}: ${cleaned[field]}`)
  })

  return cleaned
}

/**
 * Test complete data flow from form to database-ready format
 * @param {Object} formData - Raw form data
 * @returns {Object} Complete processing result
 */
export function testCompleteDataFlow(formData) {
  console.log('🔄 Testing Complete Data Flow')
  console.log('=' .repeat(50))
  
  // Step 1: Data Cleaning
  const cleaned = testDataCleaning(formData)
  
  console.log('\n' + '=' .repeat(50))
  
  // Step 2: Password Processing
  const processed = testPasswordProcessing(cleaned)
  
  console.log('\n' + '=' .repeat(50))
  console.log('📊 Complete Data Flow Summary:')
  console.log(`Input Fields: ${Object.keys(formData).length}`)
  console.log(`Cleaned Fields: ${Object.keys(cleaned).length}`)
  console.log(`Final Fields: ${Object.keys(processed.output).length}`)
  console.log(`Password Transformations: ${processed.transformations.length}`)
  console.log(`Processing Errors: ${processed.hasErrors ? 'YES' : 'NO'}`)
  
  return {
    formData,
    cleaned,
    processed: processed.output,
    transformations: processed.transformations,
    hasErrors: processed.hasErrors
  }
}

/**
 * Test CSV import data processing
 * @param {Array} csvDevices - Array of device objects from CSV parsing
 * @returns {Array} Processing results for each device
 */
export function testCSVImportProcessing(csvDevices) {
  console.log('📄 Testing CSV Import Processing')
  console.log(`Processing ${csvDevices.length} devices from CSV`)
  
  const results = csvDevices.map((device, index) => {
    console.log(`\n--- Device ${index + 1}: ${device.station_name || 'Unnamed'} ---`)
    return testCompleteDataFlow(device)
  })
  
  const summary = {
    totalDevices: csvDevices.length,
    successfulProcessing: results.filter(r => !r.hasErrors).length,
    errorCount: results.filter(r => r.hasErrors).length,
    results
  }
  
  console.log('\n' + '=' .repeat(50))
  console.log('📊 CSV Import Processing Summary:')
  console.log(`Total Devices: ${summary.totalDevices}`)
  console.log(`Successful: ${summary.successfulProcessing}`)
  console.log(`Errors: ${summary.errorCount}`)
  
  return summary
}

// Example usage in browser console:
// import { testCompleteDataFlow } from './src/utils/passwordProcessingDebug.js'
// testCompleteDataFlow({
//   station_name: 'TEST-DEVICE',
//   building_id: 'building-123',
//   station_password: 'mypassword',
//   windows_password: '',
//   platform_password: null,
//   passphrase: 'my secure passphrase'
// })
