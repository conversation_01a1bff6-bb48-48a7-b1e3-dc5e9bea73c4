/**
 * Shared Escalation Service for Deno Edge Functions
 * 
 * A universal escalation service optimized for Deno environments
 * for the JSC Alarm Call-Out System.
 */

// Type definitions
export interface AlarmData {
  id: string
  building_id: string
  severity_id: number
  alarm_type_id?: number
  alarm_details: string
  location_details?: string
  status: string
  created_at?: string
  building?: {
    id: string
    name: string
    address?: string
  }
  severity?: {
    id: number
    name: string
    level: number
  }
  alarm_type?: {
    id: number
    name: string
  }
}

export interface EscalationContact {
  id: string
  building_id: string
  contact_name: string
  contact_phone: string
  contact_email?: string
  contact_role: string
  priority_level: number
  is_active: boolean
  available_hours?: Record<string, { start: string; end: string }>
  notification_preferences?: {
    voice_calls: boolean
    sms: boolean
    email: boolean
  }
}

export interface CallOutRecord {
  id?: string
  alarm_id: string
  contact_id: string
  call_out_time: string
  status: string
  escalation_level: number
  call_type: string
  call_status: string
  retry_count: number
  next_escalation_time: string
  retell_call_id?: string
  metadata?: Record<string, any>
}

export interface EscalationResult {
  success: boolean
  callOut?: CallOutRecord
  error?: string
  escalationTriggered: boolean
  retellCallId?: string
}

export interface DatabaseClient {
  from: (table: string) => any
}

/**
 * Determine if an alarm should trigger escalation
 */
export function shouldTriggerEscalation(alarm: AlarmData): boolean {
  console.log('Checking if alarm should trigger escalation:', alarm.id)

  // Don't escalate if already acknowledged or resolved
  if (alarm.status === 'acknowledged' || alarm.status === 'resolved') {
    console.log('Alarm already acknowledged/resolved, skipping escalation:', alarm.id)
    return false
  }

  // Don't escalate if no building association
  if (!alarm.building_id) {
    console.warn('Cannot escalate alarm without building association:', alarm.id)
    return false
  }

  // Escalate based on severity level
  // Severity levels: 1=Low, 2=Medium, 3=High, 4=Critical
  const severityId = alarm.severity_id
  const shouldEscalate = severityId >= 3 // High (3) and Critical (4)

  console.log('Escalation decision:', {
    alarmId: alarm.id,
    severityId,
    shouldEscalate,
    severityName: alarm.severity?.name
  })

  return shouldEscalate
}

/**
 * Check if a contact is available based on their available hours
 */
export function isContactAvailable(contact: EscalationContact, currentTime: Date = new Date()): boolean {
  try {
    const availableHours = contact.available_hours || {}
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
    const currentDay = dayNames[currentTime.getDay()]
    const currentTimeStr = currentTime.toTimeString().slice(0, 5) // HH:MM format

    const daySchedule = availableHours[currentDay]
    if (!daySchedule) return true // If no schedule defined, assume available

    const { start, end } = daySchedule
    if (!start || !end) return true // If no times defined, assume available

    // Simple time comparison (assumes same timezone)
    const isAvailable = currentTimeStr >= start && currentTimeStr <= end
    
    console.log('Contact availability check:', {
      contactId: contact.id,
      contactName: contact.contact_name,
      currentDay,
      currentTime: currentTimeStr,
      schedule: daySchedule,
      isAvailable
    })

    return isAvailable
  } catch (error) {
    console.error('Error checking contact availability:', { 
      contactId: contact.id, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    })
    return true // Default to available if error
  }
}

/**
 * Get escalation contacts for a building
 */
export async function getEscalationContacts(
  db: DatabaseClient, 
  buildingId: string, 
  activeOnly: boolean = true
): Promise<EscalationContact[]> {
  try {
    console.log('Fetching escalation contacts for building:', buildingId)

    let query = db
      .from('escalation_contacts')
      .select('*')
      .eq('building_id', buildingId)
      .order('priority_level', { ascending: true })

    if (activeOnly) {
      query = query.eq('is_active', true)
    }

    const { data, error } = await query

    if (error) {
      console.error('Database error fetching escalation contacts:', { buildingId, error })
      throw error
    }

    const contacts = data || []
    console.log('Escalation contacts retrieved:', { 
      buildingId, 
      contactCount: contacts.length,
      contacts: contacts.map((c: EscalationContact) => ({
        id: c.id,
        name: c.contact_name,
        priority: c.priority_level,
        active: c.is_active
      }))
    })

    return contacts
  } catch (error) {
    console.error('Error fetching escalation contacts:', { 
      buildingId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    })
    throw error
  }
}

/**
 * Format phone number to E.164 format
 */
export function formatPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) return ''
  
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '')
  
  // Handle different formats
  if (digits.length === 10) {
    // US number without country code
    return `+1${digits}`
  } else if (digits.length === 11 && digits.startsWith('1')) {
    // US number with country code
    return `+${digits}`
  } else if (phoneNumber.startsWith('+')) {
    // Already in E.164 format
    return phoneNumber
  }
  
  // Default: assume US number
  return `+1${digits}`
}

/**
 * Validate phone number format
 */
export function isValidPhoneNumber(phoneNumber: string): boolean {
  if (!phoneNumber) return false
  
  // E.164 format: +[country code][number]
  const e164Regex = /^\+[1-9]\d{1,14}$/
  return e164Regex.test(phoneNumber)
}

/**
 * Get enriched alarm data for Retell AI call
 */
export async function getEnrichedAlarmData(db: DatabaseClient, alarm: AlarmData): Promise<AlarmData> {
  try {
    // Get building information
    const { data: building } = await db
      .from('buildings')
      .select('*')
      .eq('id', alarm.building_id)
      .single()

    // Get severity information
    const { data: severity } = await db
      .from('severity_levels')
      .select('*')
      .eq('id', alarm.severity_id)
      .single()

    // Get alarm type information
    const { data: alarmType } = alarm.alarm_type_id ? await db
      .from('alarm_types')
      .select('*')
      .eq('id', alarm.alarm_type_id)
      .single() : { data: null }

    return {
      ...alarm,
      building,
      severity,
      alarm_type: alarmType
    }
  } catch (error) {
    console.error('Error enriching alarm data:', { 
      alarmId: alarm.id, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    })
    return alarm // Return original alarm if enrichment fails
  }
}

/**
 * Create a Retell AI call
 */
export async function createRetellCall(
  callOut: CallOutRecord,
  alarm: AlarmData,
  contact: EscalationContact
): Promise<{ success: boolean; callId?: string; error?: string }> {
  try {
    const retellApiKey = Deno.env.get('RETELL_AI_API_KEY')
    const retellFromNumber = Deno.env.get('RETELL_AI_FROM_NUMBER')
    const retellAgentId = Deno.env.get('RETELL_AI_AGENT_ID') || 'agent_beac3aef1a176d48c4d85d2541'
    const webhookUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/retell-webhook-handler`

    if (!retellApiKey) {
      throw new Error('Retell AI API key not configured')
    }

    // Prepare dynamic variables for the call
    const dynamicVariables = {
      contact_name: contact.contact_name || 'Emergency Contact',
      contact_role: contact.contact_role || 'Facility Manager',
      building_name: alarm.building?.name || 'Unknown Building',
      building_address: alarm.building?.address || 'Address not specified',
      alarm_type: alarm.alarm_type?.name || 'System Alarm',
      alarm_severity: alarm.severity?.name || 'High Priority',
      alarm_details: alarm.alarm_details || 'Alarm details not available',
      alarm_location: alarm.location_details || 'Location not specified',
      escalation_level: String(callOut.escalation_level || 1),
      retry_attempt: String((callOut.retry_count || 0) + 1),
      webhook_url: webhookUrl
    }

    // Prepare call data
    const callData = {
      to_number: formatPhoneNumber(contact.contact_phone),
      from_number: retellFromNumber,
      agent_id: retellAgentId,
      retell_llm_dynamic_variables: dynamicVariables,
      metadata: {
        alarm_id: alarm.id,
        contact_id: contact.id,
        building_id: alarm.building_id,
        call_out_id: callOut.id,
        escalation_level: callOut.escalation_level,
        retry_attempt: (callOut.retry_count || 0) + 1
      }
    }

    console.log('Creating Retell AI call:', {
      toNumber: callData.to_number,
      agentId: callData.agent_id,
      alarmId: alarm.id
    })

    // Make the API call
    const response = await fetch('https://api.retellai.com/v2/create-phone-call', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${retellApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(callData)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Retell AI API error: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    
    console.log('Retell AI call created:', {
      callId: result.call_id,
      status: result.status
    })

    return {
      success: true,
      callId: result.call_id
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error creating Retell AI call:', { error: errorMessage })
    return {
      success: false,
      error: errorMessage
    }
  }
}

/**
 * Start escalation process for an alarm
 */
export async function startEscalation(
  db: DatabaseClient,
  alarm: AlarmData
): Promise<EscalationResult> {
  try {
    console.log('Starting escalation process for alarm:', alarm.id)

    // Get escalation contacts for the building
    const contacts = await getEscalationContacts(db, alarm.building_id)

    if (contacts.length === 0) {
      const error = `No escalation contacts found for building ${alarm.building_id}`
      console.error(error, { alarmId: alarm.id, buildingId: alarm.building_id })
      return { success: false, error, escalationTriggered: false }
    }

    // Find the first available contact
    let availableContact = contacts.find(contact => isContactAvailable(contact))

    if (!availableContact) {
      console.warn('No contacts available at current time, using first contact anyway', {
        alarmId: alarm.id,
        contactCount: contacts.length
      })
      availableContact = contacts[0]
    }

    // Create initial call-out record
    const callOutData: Omit<CallOutRecord, 'id'> = {
      alarm_id: alarm.id,
      contact_id: availableContact.id,
      call_out_time: new Date().toISOString(),
      status: 'dispatched',
      escalation_level: 1,
      call_type: 'voice',
      call_status: 'pending',
      retry_count: 0,
      next_escalation_time: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes
      metadata: {
        building_id: alarm.building_id,
        alarm_severity: alarm.severity_id,
        contact_priority: availableContact.priority_level,
        escalation_service_version: '2.0'
      }
    }

    console.log('Creating call-out record', {
      alarmId: alarm.id,
      contactId: availableContact.id,
      contactName: availableContact.contact_name
    })

    const { data: callOut, error: callOutError } = await db
      .from('call_outs')
      .insert(callOutData)
      .select()
      .single()

    if (callOutError) {
      console.error('Failed to create call-out record', {
        alarmId: alarm.id,
        error: callOutError
      })
      throw callOutError
    }

    // Initiate the first call
    const callResult = await makeCall(db, callOut, alarm, availableContact)

    console.log('Escalation started successfully', {
      alarmId: alarm.id,
      callOutId: callOut.id,
      retellCallId: callResult.retellCallId
    })

    return {
      success: true,
      callOut,
      escalationTriggered: true,
      retellCallId: callResult.retellCallId
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error starting escalation', {
      alarmId: alarm.id,
      error: errorMessage
    })
    return {
      success: false,
      error: errorMessage,
      escalationTriggered: false
    }
  }
}

/**
 * Make a phone call to a contact via Retell AI
 */
export async function makeCall(
  db: DatabaseClient,
  callOut: CallOutRecord,
  alarm: AlarmData,
  contact: EscalationContact
): Promise<{ success: boolean; retellCallId?: string; error?: string }> {
  try {
    console.log('Initiating Retell AI call', {
      callOutId: callOut.id,
      contactName: contact.contact_name,
      contactPhone: contact.contact_phone
    })

    // Validate phone number format
    const formattedPhone = formatPhoneNumber(contact.contact_phone)
    if (!isValidPhoneNumber(formattedPhone)) {
      throw new Error(`Invalid phone number format: ${contact.contact_phone}`)
    }

    // Create call attempt record
    const attemptData = {
      call_out_id: callOut.id,
      contact_id: contact.id,
      attempt_number: (callOut.retry_count || 0) + 1,
      call_status: 'calling',
      call_start_time: new Date().toISOString()
    }

    const { data: attempt, error: attemptError } = await db
      .from('call_out_attempts')
      .insert(attemptData)
      .select()
      .single()

    if (attemptError) {
      console.error('Failed to create call attempt record', {
        callOutId: callOut.id,
        error: attemptError
      })
      throw attemptError
    }

    // Get additional data for the call
    const enrichedData = await getEnrichedAlarmData(db, alarm)

    // Make the call via Retell AI
    const retellResult = await createRetellCall(callOut, enrichedData, contact)

    if (!retellResult.success) {
      throw new Error(retellResult.error || 'Failed to create Retell AI call')
    }

    // Update call-out and attempt with Retell call ID
    await Promise.all([
      db
        .from('call_outs')
        .update({
          retell_call_id: retellResult.callId,
          call_status: 'calling'
        })
        .eq('id', callOut.id),

      db
        .from('call_out_attempts')
        .update({ retell_call_id: retellResult.callId })
        .eq('id', attempt.id)
    ])

    console.log('Retell AI call created successfully', {
      callOutId: callOut.id,
      retellCallId: retellResult.callId
    })

    return {
      success: true,
      retellCallId: retellResult.callId
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error making call', {
      callOutId: callOut.id,
      error: errorMessage
    })

    // Update call-out status to failed
    try {
      await db
        .from('call_outs')
        .update({
          call_status: 'failed',
          escalation_reason: `Call failed: ${errorMessage}`
        })
        .eq('id', callOut.id)
    } catch (updateError) {
      console.error('Failed to update call-out status', {
        callOutId: callOut.id,
        error: updateError
      })
    }

    return {
      success: false,
      error: errorMessage
    }
  }
}

/**
 * Convenience function for triggering escalation
 */
export async function triggerEscalation(
  db: DatabaseClient,
  alarm: AlarmData
): Promise<EscalationResult> {
  if (!shouldTriggerEscalation(alarm)) {
    return {
      success: false,
      error: 'Alarm does not meet escalation criteria',
      escalationTriggered: false
    }
  }

  return await startEscalation(db, alarm)
}
