# Mailgun Webhook Handler - Supabase Edge Function

This Edge Function handles live alarm notifications from Mailgun webhooks for the JSC Alarm Call-Out App.

## Overview

The function receives POST requests from Mailgun webhooks, validates the signature, parses alarm email content, and stores alarm notifications in the Supabase database.

## Features

- ✅ **Webhook Signature Verification**: HMAC-SHA256 validation for security
- ✅ **Email Parsing**: Extracts alarm details from email content
- ✅ **Database Integration**: Stores alarms in existing database schema
- ✅ **CORS Support**: Handles cross-origin requests
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Input Validation**: Validates all webhook payload data

## Environment Variables

Set these environment variables in your Supabase project:

```bash
# Mailgun Configuration
MAILGUN_WEBHOOK_SIGNING_KEY=your_mailgun_webhook_signing_key

# Supabase Configuration (automatically available)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Deployment

### 1. Install Supabase CLI

```bash
npm install -g supabase
```

### 2. Login to Supabase

```bash
supabase login
```

### 3. Link to your project

```bash
supabase link --project-ref pdnclfznadtbuxeoszjx
```

### 4. Set environment variables

```bash
# Set the Mailgun webhook signing key
supabase secrets set MAILGUN_WEBHOOK_SIGNING_KEY=your_actual_signing_key
```

### 5. Deploy the function

```bash
supabase functions deploy mailgun-webhook-handler
```

### 6. Get the function URL

After deployment, you'll get a URL like:
```
https://pdnclfznadtbuxeoszjx.supabase.co/functions/v1/mailgun-webhook-handler
```

## Mailgun Configuration

### 1. Add Webhook in Mailgun Dashboard

1. Go to your Mailgun dashboard
2. Navigate to **Webhooks** section
3. Click **Add webhook**
4. Set the following:
   - **Event type**: `Incoming Messages`
   - **URL**: `https://pdnclfznadtbuxeoszjx.supabase.co/functions/v1/mailgun-webhook-handler`
   - **HTTP method**: `POST`

### 2. Configure Email Routing

Set up email routing rules to forward alarm emails to your webhook:

1. Go to **Routes** in Mailgun dashboard
2. Create a new route with:
   - **Expression**: `match_recipient("bldg-.*@mg.stieralarms.online")`
   - **Actions**: 
     - `forward("https://pdnclfznadtbuxeoszjx.supabase.co/functions/v1/mailgun-webhook-handler")`
     - `store(notify="https://pdnclfznadtbuxeoszjx.supabase.co/functions/v1/mailgun-webhook-handler")`

## Testing

### 1. Test with curl

```bash
curl -X POST https://pdnclfznadtbuxeoszjx.supabase.co/functions/v1/mailgun-webhook-handler \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "sender=<EMAIL>" \
  -d "recipient=<EMAIL>" \
  -d "subject=ALARM NOTIFICATION" \
  -d "body-plain=Time: $(date)
Alarm Type: Fire Detection System
Severity: CRITICAL
Details: Test alarm notification" \
  -d "timestamp=$(date +%s)" \
  -d "token=test_token" \
  -d "signature=test_signature"
```

### 2. Monitor Function Logs

```bash
supabase functions logs mailgun-webhook-handler
```

### 3. Test from Production

Test the Edge Function with real Mailgun webhooks in your production environment.

## Database Schema

The function works with the existing database schema:

- **buildings**: Maps email addresses to building records
- **alarm_types**: Categorizes different alarm types
- **severity_levels**: Defines alarm severity levels
- **alarm_notifications**: Stores all alarm records

## Security Features

### Webhook Signature Verification

The function verifies Mailgun webhook signatures using HMAC-SHA256:

```typescript
const value = timestamp + token
const computedSignature = hmac_sha256(MAILGUN_WEBHOOK_SIGNING_KEY, value)
return computedSignature === signature
```

### Input Validation

- Validates required fields (sender, recipient, subject, body-plain)
- Validates email address formats
- Sanitizes all input data before database insertion

### CORS Configuration

Properly configured CORS headers for cross-origin requests:

```typescript
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}
```

## Error Handling

The function includes comprehensive error handling:

- **400**: Invalid webhook payload or missing required fields
- **401**: Invalid webhook signature
- **405**: Method not allowed (only POST accepted)
- **500**: Internal server error (database issues, parsing errors)

All errors are logged with detailed context for debugging.

## Integration with Existing App

The Edge Function is designed to work seamlessly with the existing alarm dashboard:

- Uses the same database schema and table structure
- Maintains the existing alarm status workflow
- Compatible with all current filtering and view toggle functionality
- Preserves the alarm parsing logic from `src/lib/alarmUtils.js`

## Monitoring and Maintenance

### View Function Logs

```bash
supabase functions logs mailgun-webhook-handler --follow
```

### Update Environment Variables

```bash
supabase secrets set MAILGUN_WEBHOOK_SIGNING_KEY=new_signing_key
```

### Redeploy Function

```bash
supabase functions deploy mailgun-webhook-handler
```

## Troubleshooting

### Common Issues

1. **Signature Verification Fails**
   - Check that `MAILGUN_WEBHOOK_SIGNING_KEY` is set correctly
   - Verify the signing key matches your Mailgun webhook configuration

2. **Database Errors**
   - Ensure the Supabase service role key has proper permissions
   - Check that all required database tables exist

3. **Parsing Errors**
   - Review the email format and ensure it matches expected patterns
   - Check function logs for detailed parsing error messages

4. **CORS Issues**
   - Verify CORS headers are properly configured
   - Check that the client is sending requests to the correct URL

### Debug Mode

To enable detailed logging, add debug statements in the function and redeploy:

```typescript
console.log('Debug: Webhook data received:', webhookData)
console.log('Debug: Parsed alarm:', parsedAlarm)
console.log('Debug: Database insert data:', alarmData)
```

## Performance Considerations

- The function is designed to handle high-volume webhook traffic
- Database queries are optimized with proper indexing
- Error handling prevents function crashes from invalid data
- Logging provides visibility into processing performance

## Security Best Practices

1. **Always verify webhook signatures** - Never process unverified webhooks
2. **Use HTTPS only** - Ensure all webhook URLs use HTTPS
3. **Validate all input data** - Never trust external input without validation
4. **Monitor for suspicious activity** - Set up alerts for unusual webhook patterns
5. **Rotate signing keys regularly** - Update Mailgun webhook signing keys periodically
