import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts"

// Import the shared escalation service
import { triggerEscalation, type AlarmData } from '../_shared/escalationService.ts'

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Mailgun configuration
const MAILGUN_WEBHOOK_SIGNING_KEY = Deno.env.get('MAILGUN_WEBHOOK_SIGNING_KEY')!

/**
 * Verify Mailgun webhook signature using HMAC-SHA256
 */
async function verifyWebhookSignature(
  timestamp: string,
  token: string,
  signature: string
): Promise<boolean> {
  try {
    const value = timestamp + token
    const key = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(MAILGUN_WEBHOOK_SIGNING_KEY),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signatureBuffer = await crypto.subtle.sign('HMAC', key, new TextEncoder().encode(value))
    const computedSignature = Array.from(new Uint8Array(signatureBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    
    return computedSignature === signature
  } catch (error) {
    console.error('Error verifying webhook signature:', error)
    return false
  }
}

/**
 * Parse alarm email content to extract structured data
 */
function parseAlarmEmail(webhookData: any) {
  const {
    subject,
    sender,
    recipient,
    'body-plain': bodyPlain,
    'body-html': bodyHtml,
    'message-headers': messageHeaders,
    timestamp,
    signature,
    token
  } = webhookData

  // Extract message ID from headers
  const messageId = extractMessageId(messageHeaders)

  // Parse alarm details from email body
  const alarmDetails = parseAlarmContent(bodyPlain || '')

  return {
    subject,
    senderEmail: sender,
    recipientEmail: recipient,
    messageId,
    bodyPlain,
    bodyHtml,
    webhookSignature: signature,
    webhookTimestamp: timestamp,
    webhookToken: token,
    rawWebhookData: webhookData,
    ...alarmDetails
  }
}

/**
 * Extract message ID from email headers
 */
function extractMessageId(messageHeaders: any[]): string | null {
  if (!Array.isArray(messageHeaders)) return null
  
  const messageIdHeader = messageHeaders.find(header => 
    Array.isArray(header) && header[0] === 'Message-Id'
  )
  
  return messageIdHeader ? messageIdHeader[1] : null
}

/**
 * Parse alarm content from email body text
 */
function parseAlarmContent(bodyText: string) {
  const details = {
    alarmTime: null as string | null,
    alarmType: null as string | null,
    severity: null as string | null,
    alarmDetails: null as string | null,
    locationDetails: null as string | null,
    buildingAlarmId: null as string | null
  }

  if (!bodyText) return details

  // Normalize line breaks and clean up the text
  const normalizedText = bodyText
    .replace(/\r\n/g, ' ')
    .replace(/\r/g, ' ')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()

  // Parse building alarm ID - look for various patterns that building systems might use
  const alarmIdPatterns = [
    /Building\s*Alarm\s*ID:\s*([A-Za-z0-9\-_]+)/i, // "Building Alarm ID: xxx"
    /Alarm\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Event\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Reference\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
    /Incident\s*(?:ID|Number):\s*([A-Za-z0-9\-_]+)/i,
    /Alert\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /Notification\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /System\s*ID:\s*([A-Za-z0-9\-_]+)/i,
    /ID:\s*([A-Za-z0-9\-_]{4,})/i, // Generic ID pattern with minimum 4 characters
    /#([A-Za-z0-9\-_]{4,})/i, // Hash-prefixed ID
    /\[([A-Za-z0-9\-_]{4,})\]/i // Bracket-enclosed ID
  ]

  for (const pattern of alarmIdPatterns) {
    const idMatch = normalizedText.match(pattern)
    if (idMatch && idMatch[1]) {
      details.buildingAlarmId = idMatch[1].trim()
      break
    }
  }

  // Parse time - look for patterns like "Time: 4/27/2025, 11:16:06 PM EST"
  const timeMatch = normalizedText.match(/Time:\s*([^A-Za-z]*(?:AM|PM)?[^A-Za-z]*(?:EST|CST|MST|PST|EDT|CDT|MDT|PDT)?)/i)
  if (timeMatch) {
    const timeStr = timeMatch[1].trim()
    const parsedDate = parseAlarmTime(timeStr)
    if (parsedDate) {
      details.alarmTime = parsedDate.toISOString()
    }
  }

  // Parse alarm type - look for patterns like "Alarm Type: Fire Detection System"
  const typeMatch = normalizedText.match(/Alarm Type:\s*([^A-Za-z]*[A-Za-z][^:]*?)(?:\s+(?:Severity|Details|Location|$))/i)
  if (typeMatch) {
    details.alarmType = typeMatch[1].trim()
  }

  // Parse severity - look for patterns like "Severity: CRITICAL" with improved handling
  const severityMatch = normalizedText.match(/Severity:\s*(CRITICAL|HIGH|MEDIUM|LOW|EMERGENCY|WARNING|INFO|ALERT)/i)
  if (severityMatch) {
    details.severity = severityMatch[1].trim().toUpperCase()
  }

  // Parse details - look for patterns like "Details: Smoke detectors have been triggered..."
  // Handle multi-line details by capturing everything after "Details:" until next section or end
  const detailsMatch = normalizedText.match(/Details:\s*(.+?)(?:\s*(?:---|\*This email|$))/i)
  if (detailsMatch) {
    details.alarmDetails = detailsMatch[1].trim()
  }

  // Extract location information with improved patterns
  const locationPatterns = [
    /Location:\s*(.+?)(?:\s*(?:Details|Time|Alarm Type|Severity|---|\*This email|$))/i,
    /(?:in the|at the|location:?\s*)([^.]+(?:building|room|floor|area|zone|facility|site)[^.]*)/i,
    /(?:triggered in|detected in|alarm in)\s*([^.]+)/i
  ]

  for (const pattern of locationPatterns) {
    const locationMatch = normalizedText.match(pattern)
    if (locationMatch && locationMatch[1].trim().length > 1) {
      details.locationDetails = locationMatch[1].trim()
      break
    }
  }

  return details
}

/**
 * Parse alarm time string into Date object
 */
function parseAlarmTime(timeStr: string): Date | null {
  try {
    if (!timeStr || typeof timeStr !== 'string') {
      console.warn('Invalid time string provided:', timeStr)
      return null
    }

    // Clean up the time string - remove extra content that might be concatenated
    let cleanTimeStr = timeStr.trim()

    // Remove any trailing content after timezone or time
    cleanTimeStr = cleanTimeStr.replace(/\s+(Alarm Type|Severity|Details).*$/i, '')

    // Remove timezone abbreviations but keep the time structure
    let normalizedTime = cleanTimeStr
      .replace(/\s+(EST|CST|MST|PST|EDT|CDT|MDT|PDT)\s*$/i, '')
      .trim()

    // Handle edge case where time string might be empty after cleaning
    if (!normalizedTime) {
      console.warn('Time string became empty after normalization:', timeStr)
      return null
    }

    // Try multiple date parsing approaches
    let date: Date | null = null

    // First try: Direct parsing
    date = new Date(normalizedTime)
    if (!isNaN(date.getTime())) {
      return date
    }

    // Second try: Handle MM/DD/YYYY, HH:MM:SS AM/PM format
    const dateTimeMatch = normalizedTime.match(/(\d{1,2}\/\d{1,2}\/\d{4}),?\s*(\d{1,2}:\d{2}:\d{2})\s*(AM|PM)?/i)
    if (dateTimeMatch) {
      const [, datePart, timePart, ampm] = dateTimeMatch
      const fullTimeStr = ampm ? `${datePart} ${timePart} ${ampm}` : `${datePart} ${timePart}`
      date = new Date(fullTimeStr)
      if (!isNaN(date.getTime())) {
        return date
      }
    }

    // Third try: ISO-like format conversion
    const isoMatch = normalizedTime.match(/(\d{1,2})\/(\d{1,2})\/(\d{4}),?\s*(\d{1,2}):(\d{2}):(\d{2})\s*(AM|PM)?/i)
    if (isoMatch) {
      const [, month, day, year, hour, minute, second, ampm] = isoMatch
      let hour24 = parseInt(hour)

      if (ampm) {
        if (ampm.toUpperCase() === 'PM' && hour24 !== 12) {
          hour24 += 12
        } else if (ampm.toUpperCase() === 'AM' && hour24 === 12) {
          hour24 = 0
        }
      }

      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), hour24, parseInt(minute), parseInt(second))
      if (!isNaN(date.getTime())) {
        return date
      }
    }

    console.warn('Failed to parse alarm time after all attempts:', {
      original: timeStr,
      cleaned: cleanTimeStr,
      normalized: normalizedTime
    })
    return null
  } catch (error) {
    console.error('Error parsing alarm time:', error, 'Input:', timeStr)
    return null
  }
}

/**
 * Validate webhook payload structure
 */
function validateWebhookPayload(payload: any): { valid: boolean; error?: string } {
  const requiredFields = ['sender', 'recipient', 'subject', 'body-plain']
  const missingFields = requiredFields.filter(field => !payload[field])
  
  if (missingFields.length > 0) {
    return {
      valid: false,
      error: `Missing required fields: ${missingFields.join(', ')}`
    }
  }
  
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(payload.sender)) {
    return {
      valid: false,
      error: 'Invalid sender email format'
    }
  }
  
  if (!emailRegex.test(payload.recipient)) {
    return {
      valid: false,
      error: 'Invalid recipient email format'
    }
  }
  
  return { valid: true }
}

/**
 * Main webhook handler function
 */
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }

  try {
    // Parse the webhook payload
    const formData = await req.formData()
    const webhookData: any = {}
    
    // Convert FormData to object
    for (const [key, value] of formData.entries()) {
      if (key === 'message-headers') {
        try {
          webhookData[key] = JSON.parse(value as string)
        } catch {
          webhookData[key] = value
        }
      } else {
        webhookData[key] = value
      }
    }

    console.log('Received webhook data:', JSON.stringify(webhookData, null, 2))

    // Validate webhook payload
    const validation = validateWebhookPayload(webhookData)
    if (!validation.valid) {
      console.error('Webhook validation failed:', validation.error)
      return new Response(
        JSON.stringify({ error: validation.error }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Verify webhook signature
    const { timestamp, token, signature } = webhookData
    if (!await verifyWebhookSignature(timestamp, token, signature)) {
      console.error('Webhook signature verification failed')
      return new Response(
        JSON.stringify({ error: 'Invalid webhook signature' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse the alarm email
    const parsedAlarm = parseAlarmEmail(webhookData)
    console.log('Parsed alarm data:', JSON.stringify(parsedAlarm, null, 2))

    // Find the building by email address
    const { data: buildings, error: buildingsError } = await supabase
      .from('buildings')
      .select('*')
      .eq('email_address', parsedAlarm.recipientEmail)
      .eq('is_active', true)
      .single()

    if (buildingsError && buildingsError.code !== 'PGRST116') {
      console.error('Error fetching building:', buildingsError)
      throw new Error('Database error while fetching building')
    }

    const building = buildings

    // Find alarm type
    let alarmType = null
    if (parsedAlarm.alarmType) {
      const { data: alarmTypes, error: alarmTypesError } = await supabase
        .from('alarm_types')
        .select('*')
        .ilike('name', parsedAlarm.alarmType)
        .single()

      if (!alarmTypesError) {
        alarmType = alarmTypes
      }
    }

    // Find severity level
    let severityLevel = null
    if (parsedAlarm.severity) {
      const { data: severityLevels, error: severityError } = await supabase
        .from('severity_levels')
        .select('*')
        .ilike('name', parsedAlarm.severity)
        .single()

      if (!severityError) {
        severityLevel = severityLevels
      }
    }

    // Generate a fallback alarm ID if none was parsed from the email
    // This ensures we always have some form of unique identifier
    const buildingAlarmId = parsedAlarm.buildingAlarmId ||
      parsedAlarm.messageId ||
      `fallback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // Prepare alarm notification data
    const alarmData = {
      building_id: building?.id || null,
      building_alarm_id: buildingAlarmId,
      alarm_type_id: alarmType?.id || null,
      severity_id: severityLevel?.id || null,
      subject: parsedAlarm.subject,
      sender_email: parsedAlarm.senderEmail,
      recipient_email: parsedAlarm.recipientEmail,
      message_id: parsedAlarm.messageId,
      alarm_time: parsedAlarm.alarmTime,
      alarm_details: parsedAlarm.alarmDetails,
      location_details: parsedAlarm.locationDetails,
      body_html: parsedAlarm.bodyHtml,
      body_plain: parsedAlarm.bodyPlain,
      webhook_signature: parsedAlarm.webhookSignature,
      webhook_timestamp: parseInt(parsedAlarm.webhookTimestamp),
      webhook_token: parsedAlarm.webhookToken,
      raw_webhook_data: parsedAlarm.rawWebhookData,
      status: 'received'
    }

    // Use upsert to handle deduplication gracefully
    // If a duplicate alarm is received, update the existing record with new webhook data
    const { data: insertedAlarm, error: insertError } = await supabase
      .from('alarm_notifications')
      .upsert(alarmData, {
        onConflict: 'building_id,building_alarm_id',
        ignoreDuplicates: false // Update existing record with new data
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error upserting alarm notification:', insertError)
      throw new Error('Database error while processing alarm notification')
    }

    const isNewAlarm = insertedAlarm.created_at === insertedAlarm.updated_at
    console.log(`Successfully processed alarm: ${insertedAlarm.id} (${isNewAlarm ? 'new' : 'duplicate updated'})`)

    // Trigger escalation for new critical alarms using shared service
    let escalationResult = { escalationTriggered: false, success: false }
    if (isNewAlarm) {
      try {
        console.log('Checking escalation for alarm:', insertedAlarm.id)
        escalationResult = await triggerEscalation(supabase, insertedAlarm as AlarmData)
        if (escalationResult.escalationTriggered) {
          console.log('Escalation triggered successfully for alarm:', insertedAlarm.id)
        } else {
          console.log('Escalation not triggered for alarm:', insertedAlarm.id, 'Reason:', escalationResult.error)
        }
      } catch (escalationError) {
        console.error('Failed to start escalation:', escalationError)
        // Don't fail the webhook processing if escalation fails
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        alarmId: insertedAlarm.id,
        buildingAlarmId: buildingAlarmId,
        isNewAlarm: isNewAlarm,
        escalationTriggered: escalationResult.escalationTriggered,
        message: isNewAlarm ? 'New alarm notification processed successfully' : 'Duplicate alarm updated successfully'
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error processing webhook:', error)

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

// Old escalation functions removed - now using shared escalation service
