/**
 * Retell AI Webhook Handler for JSC Alarm Call-Out System
 * Handles webhooks from Retell AI for call status updates and custom tool responses
 *
 * Supports:
 * - Call lifecycle events (started, ended, analyzed)
 * - Custom tool responses (record_alarm_response, escalate_alarm)
 * - Integration with JSC alarm deduplication system
 * - Escalation workflow management
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { crypto } from 'https://deno.land/std@0.168.0/crypto/mod.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-retell-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface RetellWebhookEvent {
  event: 'call_started' | 'call_ended' | 'call_analyzed'
  call: {
    call_id: string
    call_status: string
    from_number: string
    to_number: string
    start_timestamp?: number
    end_timestamp?: number
    duration_ms?: number
    transcript?: string
    transcript_object?: any[]
    recording_url?: string
    disconnection_reason?: string
    call_analysis?: {
      call_summary: string
      in_voicemail: boolean
      user_sentiment: string
      call_successful: boolean
      custom_analysis_data: any
      acknowledgment_status?: string
      response_status?: string
      estimated_response_time?: string
      transfer_requested?: boolean
      additional_notes?: string
    }
    metadata?: {
      alarm_id?: string
      contact_id?: string
      building_id?: string
      call_out_id?: string
      escalation_level?: number
      retry_attempt?: number
    }
  }
}

interface CustomToolRequest {
  call_id: string
  tool_name: string
  parameters: {
    response_status?: string
    acknowledgment_status?: string
    estimated_response_time?: string
    additional_notes?: string
    escalation_reason?: string
  }
  metadata?: {
    alarm_id?: string
    contact_id?: string
    building_id?: string
    call_out_id?: string
    escalation_level?: number
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response('Method not allowed', {
      status: 405,
      headers: corsHeaders
    })
  }

  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const retellWebhookSecret = Deno.env.get('RETELL_AI_WEBHOOK_SECRET')

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase configuration')
    }

    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get request body and signature
    const body = await req.text()
    const signature = req.headers.get('x-retell-signature')
    const url = new URL(req.url)

    // Handle custom tool endpoints
    if (url.pathname.includes('/record-response')) {
      return await handleRecordResponse(supabase, body)
    }

    if (url.pathname.includes('/escalate')) {
      return await handleEscalateAlarm(supabase, body)
    }

    // Verify webhook signature - MANDATORY for security
    if (!retellWebhookSecret) {
      console.error('Webhook secret not configured - rejecting request')
      return new Response('Webhook secret not configured', {
        status: 500,
        headers: corsHeaders
      })
    }

    if (!signature) {
      console.error('Missing webhook signature')
      return new Response('Missing signature', {
        status: 401,
        headers: corsHeaders
      })
    }

    try {
      const expectedSignature = await crypto.subtle.digest(
        'SHA-256',
        new TextEncoder().encode(retellWebhookSecret + body)
      )
      const expectedHex = Array.from(new Uint8Array(expectedSignature))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')

      if (signature !== expectedHex) {
        console.error('Invalid webhook signature', {
          received: signature,
          expected: expectedHex.substring(0, 8) + '...'
        })
        return new Response('Unauthorized', {
          status: 401,
          headers: corsHeaders
        })
      }
    } catch (error) {
      console.error('Error verifying webhook signature:', error)
      return new Response('Signature verification failed', {
        status: 500,
        headers: corsHeaders
      })
    }

    // Parse webhook payload
    const webhookData: RetellWebhookEvent = JSON.parse(body)
    const { event, call } = webhookData

    console.log(`Received Retell AI webhook: ${event} for call ${call.call_id}`)

    // Handle different webhook events
    switch (event) {
      case 'call_started':
        await handleCallStarted(supabase, call)
        break

      case 'call_ended':
        await handleCallEnded(supabase, call)
        break

      case 'call_analyzed':
        await handleCallAnalyzed(supabase, call)
        break

      default:
        console.log(`Unhandled webhook event: ${event}`)
    }

    return new Response('OK', {
      status: 200,
      headers: corsHeaders
    })

  } catch (error) {
    console.error('Error processing Retell AI webhook:', error)
    return new Response('Internal Server Error', {
      status: 500,
      headers: corsHeaders
    })
  }
})

/**
 * Handle call_started webhook event
 */
async function handleCallStarted(supabase: any, call: any) {
  try {
    // Update call-out record with call start time
    const { error } = await supabase
      .from('call_outs')
      .update({
        call_status: 'calling',
        call_out_time: new Date(call.start_timestamp).toISOString()
      })
      .eq('retell_call_id', call.call_id)

    if (error) {
      console.error('Error updating call-out for call_started:', error)
    }

    // Update call attempt record
    await supabase
      .from('call_out_attempts')
      .update({
        call_status: 'calling',
        call_start_time: new Date(call.start_timestamp).toISOString()
      })
      .eq('retell_call_id', call.call_id)

  } catch (error) {
    console.error('Error handling call_started:', error)
  }
}

/**
 * Handle call_ended webhook event
 */
async function handleCallEnded(supabase: any, call: any) {
  try {
    const callStatus = mapRetellStatus(call.call_status)
    const answered = !['dial_failed', 'dial_no_answer', 'dial_busy'].includes(call.disconnection_reason)
    
    // Update call-out record
    const { error: callOutError } = await supabase
      .from('call_outs')
      .update({
        call_status: callStatus,
        call_duration_seconds: call.duration_ms ? Math.round(call.duration_ms / 1000) : null,
        call_transcript: call.transcript,
        call_recording_url: call.recording_url,
        response_time: call.end_timestamp ? new Date(call.end_timestamp).toISOString() : null
      })
      .eq('retell_call_id', call.call_id)

    if (callOutError) {
      console.error('Error updating call-out for call_ended:', callOutError)
    }

    // Update call attempt record
    await supabase
      .from('call_out_attempts')
      .update({
        call_status: callStatus,
        call_end_time: call.end_timestamp ? new Date(call.end_timestamp).toISOString() : null,
        call_duration_seconds: call.duration_ms ? Math.round(call.duration_ms / 1000) : null,
        disconnection_reason: call.disconnection_reason,
        call_transcript: call.transcript,
        call_recording_url: call.recording_url
      })
      .eq('retell_call_id', call.call_id)

    // Trigger escalation processing if call failed or wasn't answered
    if (!answered || callStatus === 'failed') {
      await triggerEscalationProcessing(supabase, call.call_id, call.disconnection_reason)
    }

  } catch (error) {
    console.error('Error handling call_ended:', error)
  }
}

/**
 * Handle call_analyzed webhook event
 */
async function handleCallAnalyzed(supabase: any, call: any) {
  try {
    const acknowledgmentAnalysis = analyzeAcknowledgment(call.transcript)
    const callSuccessful = call.call_analysis?.call_successful || false

    // Extract enhanced analysis data from our JSC agent
    const analysisData = call.call_analysis?.custom_analysis_data || {}
    const acknowledgmentStatus = analysisData.acknowledgment_status ||
      (acknowledgmentAnalysis.acknowledged ? 'acknowledged' : 'not_acknowledged')
    const responseStatus = analysisData.response_status || 'no_response'
    const estimatedResponseTime = analysisData.estimated_response_time || null
    const transferRequested = analysisData.transfer_requested || false
    const additionalNotes = analysisData.additional_notes || acknowledgmentAnalysis.foundKeywords?.join(', ')

    // Update call attempt with comprehensive analysis
    await supabase
      .from('call_out_attempts')
      .update({
        call_analysis: call.call_analysis,
        acknowledged: acknowledgmentStatus === 'acknowledged',
        contact_response: additionalNotes || responseStatus
      })
      .eq('retell_call_id', call.call_id)

    // Determine call outcome based on enhanced analysis
    const isAcknowledged = acknowledgmentStatus === 'acknowledged' && callSuccessful
    const needsEscalation = responseStatus === 'cannot_respond' ||
                           responseStatus === 'need_assistance' ||
                           transferRequested ||
                           (!isAcknowledged && acknowledgmentStatus !== 'voicemail')

    if (isAcknowledged && !needsEscalation) {
      // Call was successfully acknowledged - mark as completed
      await supabase
        .from('call_outs')
        .update({
          status: responseStatus === 'on_way' ? 'en_route' : 'completed',
          call_status: 'completed',
          acknowledged_by_contact: true,
          contact_response: additionalNotes || 'acknowledged',
          completion_time: new Date().toISOString(),
          next_escalation_time: null
        })
        .eq('retell_call_id', call.call_id)

      // Update alarm status
      if (call.metadata?.alarm_id) {
        await supabase
          .from('alarm_notifications')
          .update({
            status: 'acknowledged',
            acknowledged_at: new Date().toISOString(),
            acknowledged_by: call.metadata.contact_id
          })
          .eq('id', call.metadata.alarm_id)
      }
    } else {
      // Call needs escalation
      const escalationReason = transferRequested ? 'transfer_requested' :
                              responseStatus === 'cannot_respond' ? 'contact_unavailable' :
                              responseStatus === 'need_assistance' ? 'assistance_requested' :
                              acknowledgmentStatus === 'voicemail' ? 'voicemail' :
                              'no_acknowledgment'

      await triggerEscalationProcessing(supabase, call.call_id, escalationReason)
    }

  } catch (error) {
    console.error('Error handling call_analyzed:', error)
  }
}

/**
 * Trigger escalation processing for a failed call
 */
async function triggerEscalationProcessing(supabase: any, retellCallId: string, reason: string) {
  try {
    // This would typically trigger a background job or queue
    // For now, we'll update the call-out to indicate escalation is needed
    await supabase
      .from('call_outs')
      .update({
        escalation_reason: reason,
        next_escalation_time: new Date(Date.now() + 2 * 60 * 1000).toISOString() // 2 minutes from now
      })
      .eq('retell_call_id', retellCallId)

    console.log(`Triggered escalation processing for call ${retellCallId} due to ${reason}`)
  } catch (error) {
    console.error('Error triggering escalation processing:', error)
  }
}

/**
 * Map Retell AI call status to our system status
 */
function mapRetellStatus(retellStatus: string): string {
  const statusMap: { [key: string]: string } = {
    'registered': 'pending',
    'calling': 'calling',
    'in_progress': 'answered',
    'ended': 'completed',
    'failed': 'failed'
  }
  
  return statusMap[retellStatus] || 'unknown'
}

/**
 * Analyze call transcript for acknowledgment keywords
 */
function analyzeAcknowledgment(transcript: string): { acknowledged: boolean; foundKeywords: string[] } {
  if (!transcript) {
    return { acknowledged: false, foundKeywords: [] }
  }

  const acknowledgmentKeywords = [
    'acknowledged', 'confirm', 'confirmed', 'received', 'understand',
    'got it', 'copy', 'roger', 'yes', 'okay', 'ok', 'will handle',
    'on my way', 'responding', 'en route'
  ]

  const transcriptLower = transcript.toLowerCase()
  const foundKeywords = acknowledgmentKeywords.filter(keyword =>
    transcriptLower.includes(keyword)
  )

  return {
    acknowledged: foundKeywords.length > 0,
    foundKeywords
  }
}

/**
 * Handle record_alarm_response custom tool
 */
async function handleRecordResponse(supabase: any, body: string) {
  try {
    const toolRequest: CustomToolRequest = JSON.parse(body)
    console.log('Recording alarm response:', toolRequest)

    // Update call-out record with response details
    const { error } = await supabase
      .from('call_outs')
      .update({
        contact_response: toolRequest.parameters.additional_notes || 'Response recorded',
        acknowledged_by_contact: toolRequest.parameters.acknowledgment_status === 'acknowledged',
        status: toolRequest.parameters.response_status === 'on_way' ? 'en_route' : 'acknowledged'
      })
      .eq('retell_call_id', toolRequest.call_id)

    if (error) {
      console.error('Error recording response:', error)
      return new Response(JSON.stringify({ error: 'Failed to record response' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Response recorded successfully'
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Error handling record response:', error)
    return new Response(JSON.stringify({ error: 'Invalid request' }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

/**
 * Handle escalate_alarm custom tool
 */
async function handleEscalateAlarm(supabase: any, body: string) {
  try {
    const toolRequest: CustomToolRequest = JSON.parse(body)
    console.log('Escalating alarm:', toolRequest)

    // Update call-out to trigger escalation
    const { error } = await supabase
      .from('call_outs')
      .update({
        escalation_reason: toolRequest.parameters.escalation_reason || 'Manual escalation requested',
        next_escalation_time: new Date(Date.now() + 2 * 60 * 1000).toISOString(), // 2 minutes
        status: 'escalating'
      })
      .eq('retell_call_id', toolRequest.call_id)

    if (error) {
      console.error('Error escalating alarm:', error)
      return new Response(JSON.stringify({ error: 'Failed to escalate alarm' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Alarm escalation initiated'
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Error handling escalate alarm:', error)
    return new Response(JSON.stringify({ error: 'Invalid request' }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}
