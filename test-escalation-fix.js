/**
 * Test Script for Escalation Fix
 * 
 * This script tests the fixed escalation system by manually triggering
 * escalation for the recent alarm that didn't get processed.
 */

// Test alarm ID from the recent CRITICAL alarm
const TEST_ALARM_ID = 'c44c0967-f177-4f27-b0db-9e62c4b4d533'
const SUPABASE_URL = 'https://pdnclfznadtbuxeoszjx.supabase.co'
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBkbmNsZnpuYWR0YnV4ZW9zemp4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODczNzg3OSwiZXhwIjoyMDY0MzEzODc5fQ.Bpqnsm8R9WsXT_miPXv7OibxvXnuq6-jNJdQcCt77xs'

/**
 * Test the escalation system by calling the Mailgun webhook handler
 * with a simulated alarm that should trigger escalation
 */
async function testEscalationFix() {
  console.log('🧪 Testing Escalation Fix...')
  console.log('=' .repeat(50))
  
  try {
    // Step 1: Check current alarm status
    console.log('📋 Step 1: Checking current alarm status...')
    
    const alarmResponse = await fetch(`${SUPABASE_URL}/rest/v1/alarm_notifications?id=eq.${TEST_ALARM_ID}&select=*,building:buildings(*),severity:severity_levels(*)`, {
      headers: {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!alarmResponse.ok) {
      throw new Error(`Failed to fetch alarm: ${alarmResponse.status}`)
    }
    
    const alarms = await alarmResponse.json()
    if (alarms.length === 0) {
      throw new Error('Alarm not found')
    }
    
    const alarm = alarms[0]
    console.log('✅ Alarm found:', {
      id: alarm.id,
      building: alarm.building?.name,
      severity: alarm.severity?.name,
      status: alarm.status
    })
    
    // Step 2: Check if call-outs already exist
    console.log('\\n📞 Step 2: Checking existing call-outs...')
    
    const callOutResponse = await fetch(`${SUPABASE_URL}/rest/v1/call_outs?alarm_id=eq.${TEST_ALARM_ID}`, {
      headers: {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    const existingCallOuts = await callOutResponse.json()
    console.log(`Found ${existingCallOuts.length} existing call-outs`)
    
    if (existingCallOuts.length > 0) {
      console.log('⚠️  Call-outs already exist for this alarm. Checking status...')
      existingCallOuts.forEach((co, index) => {
        console.log(`   ${index + 1}. Status: ${co.status}, Call Status: ${co.call_status}, Level: ${co.escalation_level}`)
      })
    }
    
    // Step 3: Test escalation trigger by simulating webhook
    console.log('\\n🚀 Step 3: Testing escalation trigger...')
    
    // Create a test webhook payload that simulates a new alarm
    const testWebhookPayload = {
      'event-data': {
        event: 'delivered',
        message: {
          headers: {
            to: '<EMAIL>',
            from: '<EMAIL>',
            subject: 'ALARM NOTIFICATION'
          }
        }
      },
      'body-html': `
        Time: ${new Date().toLocaleString()} EST
        Alarm Type: Fire Detection System
        Severity: CRITICAL
        Building Alarm ID: test-escalation-${Date.now()}
        
        Details: Test escalation trigger - smoke detectors activated.
      `,
      'body-plain': `Test escalation alarm - CRITICAL severity`,
      recipient: '<EMAIL>',
      sender: '<EMAIL>',
      subject: 'ALARM NOTIFICATION',
      timestamp: Math.floor(Date.now() / 1000),
      token: 'test-token-' + Date.now(),
      signature: 'test-signature'
    }
    
    console.log('📤 Sending test webhook to Mailgun handler...')
    
    const webhookResponse = await fetch(`${SUPABASE_URL}/functions/v1/mailgun-webhook-handler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`
      },
      body: JSON.stringify(testWebhookPayload)
    })
    
    if (!webhookResponse.ok) {
      const errorText = await webhookResponse.text()
      console.error('❌ Webhook failed:', webhookResponse.status, errorText)
      return
    }
    
    const webhookResult = await webhookResponse.json()
    console.log('✅ Webhook processed successfully:', {
      success: webhookResult.success,
      alarmId: webhookResult.alarmId,
      isNewAlarm: webhookResult.isNewAlarm,
      escalationTriggered: webhookResult.escalationTriggered
    })
    
    if (webhookResult.escalationTriggered) {
      console.log('🎉 Escalation was triggered!')
      
      // Wait a moment and check for new call-outs
      console.log('\\n⏳ Waiting 3 seconds for call-out creation...')
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const newCallOutResponse = await fetch(`${SUPABASE_URL}/rest/v1/call_outs?alarm_id=eq.${webhookResult.alarmId}&select=*,escalation_contacts(*)`, {
        headers: {
          'apikey': SUPABASE_SERVICE_KEY,
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
          'Content-Type': 'application/json'
        }
      })
      
      const newCallOuts = await newCallOutResponse.json()
      console.log(`\\n📞 Found ${newCallOuts.length} call-out(s) for the new alarm:`)
      
      newCallOuts.forEach((co, index) => {
        console.log(`   ${index + 1}. Contact: ${co.escalation_contacts?.contact_name}`)
        console.log(`      Phone: ${co.escalation_contacts?.contact_phone}`)
        console.log(`      Status: ${co.status}`)
        console.log(`      Call Status: ${co.call_status}`)
        console.log(`      Retell Call ID: ${co.retell_call_id || 'Not set'}`)
        console.log(`      Created: ${co.created_at}`)
      })
      
      // Check call attempts
      if (newCallOuts.length > 0) {
        const callOutId = newCallOuts[0].id
        const attemptsResponse = await fetch(`${SUPABASE_URL}/rest/v1/call_out_attempts?call_out_id=eq.${callOutId}`, {
          headers: {
            'apikey': SUPABASE_SERVICE_KEY,
            'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
            'Content-Type': 'application/json'
          }
        })
        
        const attempts = await attemptsResponse.json()
        console.log(`\\n📱 Found ${attempts.length} call attempt(s):`)
        
        attempts.forEach((attempt, index) => {
          console.log(`   ${index + 1}. Attempt #${attempt.attempt_number}`)
          console.log(`      Status: ${attempt.call_status}`)
          console.log(`      Retell Call ID: ${attempt.retell_call_id || 'Not set'}`)
          console.log(`      Start Time: ${attempt.call_start_time}`)
        })
      }
    } else {
      console.log('⚠️  Escalation was not triggered. Check the logic.')
    }
    
    console.log('\\n✅ Test completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack trace:', error.stack)
  }
}

/**
 * Check the current status of the system
 */
async function checkSystemStatus() {
  console.log('\\n🔍 System Status Check')
  console.log('=' .repeat(30))
  
  try {
    // Check Supabase connection
    const healthResponse = await fetch(`${SUPABASE_URL}/rest/v1/`, {
      headers: {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`
      }
    })
    
    console.log('✅ Supabase connection:', healthResponse.ok ? 'OK' : 'Failed')
    
    // Check Edge Functions
    const functionsResponse = await fetch(`${SUPABASE_URL}/functions/v1/`, {
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`
      }
    })
    
    console.log('✅ Edge Functions:', functionsResponse.ok ? 'OK' : 'Failed')
    
    // Check escalation contacts
    const contactsResponse = await fetch(`${SUPABASE_URL}/rest/v1/escalation_contacts?building_id=eq.b7882a75-f3e1-46a0-ba65-404a68c20f20&select=count`, {
      headers: {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Prefer': 'count=exact'
      }
    })
    
    const contactCount = contactsResponse.headers.get('content-range')?.split('/')[1] || '0'
    console.log('✅ Escalation contacts for test building:', contactCount)
    
  } catch (error) {
    console.error('❌ System status check failed:', error.message)
  }
}

// Run the tests
async function main() {
  await checkSystemStatus()
  await testEscalationFix()
}

main().catch(console.error)
