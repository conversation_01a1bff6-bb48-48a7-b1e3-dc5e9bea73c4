/**
 * Test Script for JSC Alarm Call-Out Voice Agent
 * 
 * This script demonstrates how to use the newly created Retell AI voice agent
 * for alarm notifications in the JSC Building Management system.
 * 
 * Usage:
 * 1. Set up your environment variables (see RETELL_AI_AGENT_CONFIG.md)
 * 2. Run: node test-voice-agent.js
 * 3. Check the console output and Retell AI dashboard for call details
 */

// Note: This test script needs to be run in a browser environment or with proper Vite setup
// For Node.js testing, we'll simulate the environment variables

// Simulate Vite environment variables for Node.js testing
if (typeof globalThis.import === 'undefined') {
  globalThis.import = {
    meta: {
      env: {
        VITE_RETELL_AI_API_KEY: process.env.VITE_RETELL_AI_API_KEY || 'key_fea9ff712a76008a05236c358269',
        VITE_RETELL_AI_FROM_NUMBER: process.env.VITE_RETELL_AI_FROM_NUMBER || '+18563419193',
        VITE_RETELL_AI_AGENT_ID: process.env.VITE_RETELL_AI_AGENT_ID || 'agent_beac3aef1a176d48c4d85d2541',
        VITE_RETELL_AI_LLM_ID: process.env.VITE_RETELL_AI_LLM_ID || 'llm_f42d54584f5cfc07e6ee62b1cb83',
        VITE_RETELL_AI_WEBHOOK_SECRET: process.env.VITE_RETELL_AI_WEBHOOK_SECRET || 'key_77a41edb8328af9fbb21e809d241',
        VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL || 'https://pdnclfznadtbuxeoszjx.supabase.co'
      }
    }
  }
}

import { retellAI } from './src/lib/retellAI.js'

// Test configuration
const TEST_CONFIG = {
  // Replace with a real phone number for testing (your own number)
  testPhoneNumber: '+15551234567', // CHANGE THIS TO YOUR PHONE NUMBER
  
  // Sample alarm data
  sampleAlarm: {
    id: 'test-alarm-' + Date.now(),
    building_id: 'test-building-123',
    building_name: 'JSC Medical Office Building',
    building_address: '123 Main Street, Anytown, ST 12345',
    alarm_type: 'Fire Alarm',
    alarm_severity: 'Critical',
    alarm_details: 'Smoke detected in server room - automatic sprinkler system activated',
    location_details: 'Building A, Floor 3, Server Room 301',
    alarm_time: new Date().toISOString()
  },
  
  // Sample contact data
  sampleContact: {
    id: 'test-contact-456',
    contact_name: 'John Smith',
    contact_role: 'Facility Manager',
    contact_phone: '+15551234567' // Same as test number
  },
  
  // Call metadata
  callMetadata: {
    call_out_id: 'test-callout-789',
    escalation_level: 1,
    retry_attempt: 1
  }
}

/**
 * Test the voice agent configuration
 */
async function testAgentConfiguration() {
  console.log('🔧 Testing Agent Configuration...')
  
  const config = retellAI.getAgentConfig()
  console.log('Agent Configuration:', config)
  
  if (!config.isConfigured) {
    console.error('❌ Retell AI is not properly configured!')
    console.log('Please check your environment variables:')
    console.log('- RETELL_AI_API_KEY')
    console.log('- RETELL_AI_FROM_NUMBER')
    console.log('- RETELL_AI_AGENT_ID (optional, defaults to JSC agent)')
    return false
  }
  
  console.log('✅ Agent configuration is valid')
  return true
}

/**
 * Test creating a voice call
 */
async function _testVoiceCall() {
  console.log('\n📞 Testing Voice Call Creation...')
  
  try {
    const callResult = await retellAI.createPhoneCall({
      toNumber: TEST_CONFIG.testPhoneNumber,
      alarmData: TEST_CONFIG.sampleAlarm,
      contactData: TEST_CONFIG.sampleContact,
      metadata: TEST_CONFIG.callMetadata
    })
    
    console.log('✅ Call created successfully!')
    console.log('Call Details:', {
      call_id: callResult.call_id,
      status: callResult.status,
      to_number: callResult.to_number
    })
    
    return callResult
    
  } catch (error) {
    console.error('❌ Failed to create call:', error.message)
    return null
  }
}

/**
 * Test getting call details
 */
async function _testGetCallDetails(callId) {
  if (!callId) {
    console.log('\n⏭️  Skipping call details test (no call ID)')
    return
  }
  
  console.log('\n📋 Testing Get Call Details...')
  
  try {
    // Wait a moment for the call to be processed
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const callDetails = await retellAI.getCall(callId)
    console.log('✅ Call details retrieved successfully!')
    console.log('Call Status:', callDetails.status)
    console.log('Call Duration:', callDetails.duration_ms ? `${callDetails.duration_ms}ms` : 'N/A')
    
    return callDetails
    
  } catch (error) {
    console.error('❌ Failed to get call details:', error.message)
    return null
  }
}

/**
 * Test phone number formatting
 */
function testPhoneNumberFormatting() {
  console.log('\n📱 Testing Phone Number Formatting...')
  
  const testNumbers = [
    '5551234567',      // 10 digits
    '15551234567',     // 11 digits with country code
    '+15551234567',    // E.164 format
    '(*************',  // Formatted US number
    '************'     // Dashed format
  ]
  
  testNumbers.forEach(number => {
    const formatted = retellAI.formatPhoneNumber(number)
    const isValid = retellAI.isValidPhoneNumber(formatted)
    console.log(`${number} → ${formatted} (${isValid ? '✅' : '❌'})`)
  })
}

/**
 * Test acknowledgment analysis
 */
function testAcknowledgmentAnalysis() {
  console.log('\n🎯 Testing Acknowledgment Analysis...')
  
  const testTranscripts = [
    'Yes, I acknowledge the alarm and I am on my way to the building.',
    'I understand there is a fire alarm. I will respond immediately.',
    'Got it, I will handle this situation right away.',
    'I cannot respond right now, please call the backup contact.',
    'Hello? I cannot hear you very well.'
  ]
  
  testTranscripts.forEach((transcript, index) => {
    const analysis = retellAI.analyzeAcknowledgment(transcript)
    console.log(`\nTranscript ${index + 1}: "${transcript}"`)
    console.log(`Analysis:`, {
      acknowledged: analysis.acknowledged ? '✅' : '❌',
      confidence: `${Math.round(analysis.confidence * 100)}%`,
      keywords: analysis.foundKeywords
    })
  })
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚨 JSC Alarm Call-Out Voice Agent Test Suite')
  console.log('=' .repeat(50))
  
  // Test 1: Configuration
  const isConfigured = await testAgentConfiguration()
  if (!isConfigured) {
    console.log('\n❌ Cannot proceed with tests - configuration invalid')
    return
  }
  
  // Test 2: Phone number formatting
  testPhoneNumberFormatting()
  
  // Test 3: Acknowledgment analysis
  testAcknowledgmentAnalysis()
  
  // Test 4: Voice call (only if properly configured)
  if (isConfigured && TEST_CONFIG.testPhoneNumber !== '+15551234567') {
    console.log('\n⚠️  WARNING: About to place a real phone call!')
    console.log(`Calling: ${TEST_CONFIG.testPhoneNumber}`)
    console.log('Make sure this is your phone number and you are ready to answer.')
    
    // Uncomment the next lines to actually place a test call
    // const callResult = await testVoiceCall()
    // await testGetCallDetails(callResult?.call_id)
    
    console.log('\n📞 Test call is commented out for safety.')
    console.log('Uncomment the lines above to place an actual test call.')
  } else {
    console.log('\n📞 Skipping actual call test (update TEST_CONFIG.testPhoneNumber)')
  }
  
  console.log('\n✅ Test suite completed!')
  console.log('\nNext steps:')
  console.log('1. Update TEST_CONFIG.testPhoneNumber with your phone number')
  console.log('2. Uncomment the test call lines to place a real call')
  console.log('3. Check the Retell AI dashboard for call logs')
  console.log('4. Integrate with your escalation service')
}

// Run the tests
runTests().catch(console.error)
