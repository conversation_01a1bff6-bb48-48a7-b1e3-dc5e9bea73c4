import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Sentry plugin for source maps and release management
    sentryVitePlugin({
      org: 's-tier-building-automation-3b',
      project: 'alarm-call-out',
      authToken: process.env.SENTRY_AUTH_TOKEN,
      sourcemaps: {
        assets: './dist/**',
        ignore: ['node_modules/**'],
      },
      release: {
        name: process.env.SENTRY_RELEASE || `jsc-alarm-app@${process.env.npm_package_version || 'dev'}`,
        deploy: {
          env: process.env.NODE_ENV || 'development',
        },
      },
      // Only upload source maps in production builds
      disable: process.env.NODE_ENV === 'development',
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/lib': resolve(__dirname, './src/lib'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/contexts': resolve(__dirname, './src/contexts'),
      '@/types': resolve(__dirname, './src/types'),
    },
  },
  build: {
    sourcemap: true, // Enable source maps for Sentry
    // Code splitting optimization
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
          sentry: ['@sentry/react'],
        },
      },
    },
  },
})
